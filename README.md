# 2D Game Portfolio

A 2D side-scrolling game built with Next.js and PixiJS featuring a character that can move through a layered background environment.

## Features

- **Layered Background System**: Multiple PNG layers stacked to create depth (sky, jungle background, trees & bushes, lianas, grasses, grass & road)
- **Animated Character**: Sprite sheet animation for running character
- **Dual Input Support**: 
  - **Desktop**: WASD keys or Arrow keys for movement
  - **Mobile**: Virtual joystick for touch controls
- **Responsive Design**: Adapts to different screen sizes
- **Ground Collision**: Character moves on the grass & road layer

## Controls

### Desktop
- **A** or **Left Arrow**: Move left
- **D** or **Right Arrow**: Move right
- **W/S** or **Up/Down Arrows**: Currently not used (left/right movement only)

### Mobile
- Use the virtual joystick in the bottom-left corner
- Drag left or right to move the character

## Getting Started

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Run the development server**:
   ```bash
   npm run dev
   ```

3. **Open your browser** and navigate to `http://localhost:3000`

## Project Structure

```
src/
├── app/
│   ├── layout.tsx          # Root layout
│   ├── page.tsx            # Main page
│   └── globals.css         # Global styles
├── components/
│   └── Game.tsx            # Main game component
└── lib/
    ├── GameEngine.ts       # Core game engine
    ├── Background.ts       # Background rendering system
    ├── Player.ts           # Character sprite and animation
    └── InputManager.ts     # Keyboard and mobile input handling

public/
├── background/             # Background layer images
│   ├── sky.png
│   ├── jungle_bg.png
│   ├── trees&bushes.png
│   ├── lianas.png
│   ├── grasses.png
│   └── grass&road.png
└── character/
    └── Run.png             # Character sprite sheet
```

## Technologies Used

- **Next.js 15**: React framework with App Router
- **PixiJS**: 2D WebGL renderer for high-performance graphics
- **TypeScript**: Type-safe JavaScript
- **nipplejs**: Virtual joystick for mobile controls

## Game Mechanics

- Character moves left and right only
- Character stays on the ground level (grass & road layer)
- Smooth sprite sheet animation during movement
- Responsive canvas that adapts to screen size
- Parallax scrolling ready (can be extended)

## Development

To add new features:

1. **New background layers**: Add PNG files to `public/background/` and update the `backgroundLayers` array in `Background.ts`
2. **New character animations**: Add sprite sheets to `public/character/` and extend the `Player.ts` class
3. **New controls**: Extend the `InputManager.ts` class
4. **Game mechanics**: Add logic to the `GameEngine.ts` class

## Build for Production

```bash
npm run build
npm start
```

The application will be optimized for production deployment.
