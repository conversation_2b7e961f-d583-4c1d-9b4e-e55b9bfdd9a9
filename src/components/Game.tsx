'use client'

import { useEffect, useRef } from 'react'
import * as PIXI from 'pixi.js'
import { GameEngine } from '@/lib/GameEngine'

export default function Game() {
  const gameRef = useRef<HTMLDivElement>(null)
  const gameEngineRef = useRef<GameEngine | null>(null)

  useEffect(() => {
    if (!gameRef.current) return

    // Initialize PIXI Application
    const app = new PIXI.Application()
    
    const initGame = async () => {
      await app.init({
        width: window.innerWidth,
        height: window.innerHeight,
        backgroundColor: 0x000000,
        resizeTo: window,
      })

      // Add canvas to DOM
      if (gameRef.current) {
        gameRef.current.appendChild(app.canvas as HTMLCanvasElement)
      }

      // Initialize game engine
      gameEngineRef.current = new GameEngine(app)
      await gameEngineRef.current.init()
    }

    initGame()

    // Handle window resize
    const handleResize = () => {
      if (gameEngineRef.current) {
        gameEngineRef.current.resize(window.innerWidth, window.innerHeight)
      }
    }

    window.addEventListener('resize', handleResize)

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize)
      if (gameEngineRef.current) {
        gameEngineRef.current.destroy()
      }
      app.destroy(true)
    }
  }, [])

  return (
    <div>
      <div ref={gameRef} id="game-canvas" />
      <div className="mobile-controls" id="mobile-joystick" />
    </div>
  )
}
