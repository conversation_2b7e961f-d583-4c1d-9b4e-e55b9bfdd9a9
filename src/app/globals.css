* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  background: #000;
  color: #fff;
}

#game-container {
  width: 100vw;
  height: 100vh;
  position: relative;
  overflow: hidden;
}

#game-canvas {
  display: block;
  width: 100%;
  height: 100%;
}

.mobile-controls {
  position: absolute;
  bottom: 20px;
  left: 20px;
  z-index: 1000;
}

@media (min-width: 768px) {
  .mobile-controls {
    display: none;
  }
}
