import * as PIXI from 'pixi.js'

export class Player {
  public sprite: PIXI.AnimatedSprite
  private runTextures: PIXI.Texture[] = []
  private isMoving: boolean = false
  private direction: 'left' | 'right' = 'right'
  private speed: number = 5
  private groundLevel: number = 0
  private x: number = 0
  private y: number = 0

  constructor() {
    // Create a temporary texture for initialization
    let tempTexture: PIXI.Texture

    if (typeof document !== 'undefined') {
      const canvas = document.createElement('canvas')
      canvas.width = 32
      canvas.height = 32
      const ctx = canvas.getContext('2d')!
      ctx.fillStyle = '#ff0000'
      ctx.fillRect(0, 0, 32, 32)
      tempTexture = PIXI.Texture.from(canvas)
    } else {
      // Create empty texture for SSR
      tempTexture = PIXI.Texture.EMPTY
    }

    // Initialize with temporary sprite, will be replaced in init()
    this.sprite = new PIXI.AnimatedSprite([tempTexture])
  }

  async init() {
    try {
      // Load the run sprite sheet
      const runTexture = await PIXI.Assets.load('/character/Run.png')
      
      // Assuming the sprite sheet has frames arranged horizontally
      // You may need to adjust these values based on your actual sprite sheet
      const frameWidth = runTexture.width / 8 // Assuming 8 frames
      const frameHeight = runTexture.height
      
      // Create textures for each frame
      for (let i = 0; i < 8; i++) {
        const frame = new PIXI.Rectangle(i * frameWidth, 0, frameWidth, frameHeight)
        const frameTexture = new PIXI.Texture({
          source: runTexture.source,
          frame: frame
        })
        this.runTextures.push(frameTexture)
      }

      // Replace the temporary sprite with the animated sprite
      const parent = this.sprite.parent
      if (parent) {
        parent.removeChild(this.sprite)
      }

      this.sprite = new PIXI.AnimatedSprite(this.runTextures)
      this.sprite.animationSpeed = 0.15
      this.sprite.loop = true

      // Set initial properties
      this.sprite.anchor.set(0.5, 1) // Anchor at bottom center
      this.sprite.scale.set(2) // Scale up the character

      // Re-add to parent if it existed
      if (parent) {
        parent.addChild(this.sprite)
      }
      
      // Set initial position
      this.x = 100
      this.y = this.groundLevel
      this.updateSpritePosition()
      
    } catch (error) {
      console.error('Failed to load character sprite:', error)
      
      // Create a fallback rectangle character using canvas
      let fallbackTexture: PIXI.Texture

      if (typeof document !== 'undefined') {
        const canvas = document.createElement('canvas')
        canvas.width = 30
        canvas.height = 30
        const ctx = canvas.getContext('2d')!
        ctx.fillStyle = '#ff0000'
        ctx.fillRect(0, 0, 30, 30)
        fallbackTexture = PIXI.Texture.from(canvas)
      } else {
        fallbackTexture = PIXI.Texture.EMPTY
      }

      // Replace the temporary sprite with the fallback sprite
      const parent = this.sprite.parent
      if (parent) {
        parent.removeChild(this.sprite)
      }

      this.sprite = new PIXI.AnimatedSprite([fallbackTexture])
      this.sprite.anchor.set(0.5, 1)
      this.sprite.scale.set(1)

      // Re-add to parent if it existed
      if (parent) {
        parent.addChild(this.sprite)
      }
      
      this.x = 100
      this.y = this.groundLevel
      this.updateSpritePosition()
    }
  }

  move(direction: 'left' | 'right' | 'stop') {
    if (direction === 'stop') {
      this.isMoving = false
      this.sprite.stop()
    } else {
      this.isMoving = true
      this.direction = direction
      
      // Flip sprite based on direction
      if (direction === 'left') {
        this.sprite.scale.x = -Math.abs(this.sprite.scale.x)
      } else {
        this.sprite.scale.x = Math.abs(this.sprite.scale.x)
      }
      
      // Start animation
      if (!this.sprite.playing) {
        this.sprite.play()
      }
    }
  }

  update() {
    if (this.isMoving) {
      // Move the character
      if (this.direction === 'left') {
        this.x -= this.speed
      } else if (this.direction === 'right') {
        this.x += this.speed
      }
      
      this.updateSpritePosition()
    }
  }

  private updateSpritePosition() {
    this.sprite.x = this.x
    this.sprite.y = this.y
  }

  setGroundLevel(groundLevel: number) {
    this.groundLevel = groundLevel
    this.y = groundLevel
    this.updateSpritePosition()
  }

  setPosition(x: number, y: number) {
    this.x = x
    this.y = y
    this.updateSpritePosition()
  }

  getBounds() {
    return {
      x: this.x - (this.sprite.width / 2),
      y: this.y - this.sprite.height,
      width: this.sprite.width,
      height: this.sprite.height
    }
  }

  resize(width: number, height: number) {
    // Adjust player position if needed when screen resizes
    // Keep player within new bounds
    if (this.x > width) {
      this.x = width - 50
      this.updateSpritePosition()
    }
  }
}
