import * as PIXI from 'pixi.js'
import { Background } from './Background'
import { Player } from './Player'
import { InputManager } from './InputManager'

export class GameEngine {
  private app: PIXI.Application
  private background: Background
  private player: Player
  private inputManager: InputManager
  private gameContainer: PIXI.Container

  constructor(app: PIXI.Application) {
    this.app = app
    this.gameContainer = new PIXI.Container()
    this.app.stage.addChild(this.gameContainer)
    
    this.background = new Background()
    this.player = new Player()
    this.inputManager = new InputManager()
  }

  async init() {
    // Initialize background
    await this.background.init()
    this.gameContainer.addChild(this.background.container)

    // Initialize player
    await this.player.init()
    this.gameContainer.addChild(this.player.sprite)

    // Set up input handling
    this.inputManager.init()
    this.setupInputHandlers()

    // Start game loop
    this.app.ticker.add(this.gameLoop.bind(this))
  }

  private setupInputHandlers() {
    this.inputManager.onMove = (direction: 'left' | 'right' | 'stop') => {
      this.player.move(direction)
    }
  }

  private gameLoop() {
    // Update player
    this.player.update()

    // Keep player within bounds and on the ground
    const groundY = this.background.getGroundLevel()
    this.player.setGroundLevel(groundY)
    
    // Keep player within screen bounds
    const playerBounds = this.player.getBounds()
    if (playerBounds.x < 0) {
      this.player.setPosition(0, playerBounds.y)
    } else if (playerBounds.x + playerBounds.width > this.app.screen.width) {
      this.player.setPosition(this.app.screen.width - playerBounds.width, playerBounds.y)
    }
  }

  resize(width: number, height: number) {
    this.background.resize(width, height)
    this.player.resize(width, height)
  }

  destroy() {
    this.inputManager.destroy()
    this.app.ticker.remove(this.gameLoop.bind(this))
    this.gameContainer.destroy()
  }
}
