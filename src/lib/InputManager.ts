export class InputManager {
  private keys: { [key: string]: boolean } = {}
  private joystick: any = null
  public onMove: ((direction: 'left' | 'right' | 'stop') => void) | null = null

  init() {
    this.setupKeyboardControls()
    this.setupMobileControls()
  }

  private setupKeyboardControls() {
    // Only run on client side
    if (typeof window === 'undefined') {
      return
    }

    // Handle keydown events
    document.addEventListener('keydown', (event) => {
      this.keys[event.code] = true
      this.handleKeyboardInput()
    })

    // Handle keyup events
    document.addEventListener('keyup', (event) => {
      this.keys[event.code] = false
      this.handleKeyboardInput()
    })
  }

  private handleKeyboardInput() {
    const leftPressed = this.keys['KeyA'] || this.keys['ArrowLeft']
    const rightPressed = this.keys['KeyD'] || this.keys['ArrowRight']

    if (leftPressed && !rightPressed) {
      this.onMove?.('left')
    } else if (rightPressed && !leftPressed) {
      this.onMove?.('right')
    } else {
      this.onMove?.('stop')
    }
  }

  private async setupMobileControls() {
    // Only run on client side
    if (typeof window === 'undefined') {
      return
    }

    // Only show mobile controls on touch devices
    if (!('ontouchstart' in window)) {
      return
    }

    const joystickContainer = document.getElementById('mobile-joystick')
    if (!joystickContainer) {
      console.warn('Mobile joystick container not found')
      return
    }

    try {
      // Dynamic import to avoid SSR issues
      const nipplejs = (await import('nipplejs')).default

      // Create virtual joystick
      this.joystick = nipplejs.create({
        zone: joystickContainer,
        mode: 'static',
        position: { left: '50px', bottom: '50px' },
        color: 'white',
        size: 100,
        threshold: 0.1,
      })

      // Handle joystick events
      this.joystick.on('move', (evt: any, data: any) => {
        if (data.direction) {
          if (data.direction.x === 'left') {
            this.onMove?.('left')
          } else if (data.direction.x === 'right') {
            this.onMove?.('right')
          }
        }
      })

      this.joystick.on('end', () => {
        this.onMove?.('stop')
      })
    } catch (error) {
      console.error('Failed to load mobile controls:', error)
    }
  }

  destroy() {
    // Only run on client side
    if (typeof window === 'undefined') {
      return
    }

    // Remove keyboard event listeners
    document.removeEventListener('keydown', this.handleKeyboardInput)
    document.removeEventListener('keyup', this.handleKeyboardInput)

    // Destroy joystick
    if (this.joystick) {
      this.joystick.destroy()
    }
  }
}
