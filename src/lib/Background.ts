import * as PIXI from 'pixi.js'

export class Background {
  public container: PIXI.Container
  private layers: PIXI.Sprite[] = []
  private groundLevel: number = 0

  // Background layer order (back to front)
  private backgroundLayers = [
    { name: 'sky', path: '/background/sky.png', parallax: 0.1 },
    { name: 'jungle_bg', path: '/background/jungle_bg.png', parallax: 0.3 },
    { name: 'trees_bushes', path: '/background/trees&bushes.png', parallax: 0.5 },
    { name: 'lianas', path: '/background/lianas.png', parallax: 0.7 },
    { name: 'grasses', path: '/background/grasses.png', parallax: 0.8 },
    { name: 'grass_road', path: '/background/grass&road.png', parallax: 1.0 }, // Ground layer
  ]

  constructor() {
    this.container = new PIXI.Container()
  }

  async init() {
    // Load all background textures
    const textures: { [key: string]: PIXI.Texture } = {}
    
    for (const layer of this.backgroundLayers) {
      try {
        textures[layer.name] = await PIXI.Assets.load(layer.path)
      } catch (error) {
        console.warn(`Failed to load background layer: ${layer.path}`, error)
        // Create a fallback colored rectangle
        const graphics = new PIXI.Graphics()
        graphics.rect(0, 0, 800, 600)
        graphics.fill(0x333333)
        textures[layer.name] = PIXI.Texture.from(graphics)
      }
    }

    // Create sprites for each layer
    for (let i = 0; i < this.backgroundLayers.length; i++) {
      const layerConfig = this.backgroundLayers[i]
      const texture = textures[layerConfig.name]
      
      if (texture) {
        const sprite = new PIXI.Sprite(texture)
        sprite.name = layerConfig.name
        
        // Scale to fit screen while maintaining aspect ratio
        this.scaleToFit(sprite)
        
        // Position at bottom for ground layers
        if (layerConfig.name === 'grass_road') {
          const screenHeight = typeof window !== 'undefined' ? window.innerHeight : 600
          sprite.y = screenHeight - sprite.height
          this.groundLevel = sprite.y
        }
        
        this.layers.push(sprite)
        this.container.addChild(sprite)
      }
    }
  }

  private scaleToFit(sprite: PIXI.Sprite) {
    const screenWidth = typeof window !== 'undefined' ? window.innerWidth : 800
    const screenHeight = typeof window !== 'undefined' ? window.innerHeight : 600
    
    // Scale to cover the screen width
    const scaleX = screenWidth / sprite.texture.width
    const scaleY = screenHeight / sprite.texture.height
    
    // Use the larger scale to ensure full coverage
    const scale = Math.max(scaleX, scaleY)
    
    sprite.scale.set(scale)
    
    // Center horizontally
    sprite.x = (screenWidth - sprite.width) / 2
  }

  resize(width: number, height: number) {
    // Rescale all layers to new screen size
    this.layers.forEach((sprite, index) => {
      this.scaleToFit(sprite)
      
      // Reposition ground layer
      if (this.backgroundLayers[index].name === 'grass_road') {
        sprite.y = height - sprite.height
        this.groundLevel = sprite.y
      }
    })
  }

  getGroundLevel(): number {
    return this.groundLevel
  }

  // Method to implement parallax scrolling if needed later
  updateParallax(cameraX: number) {
    this.layers.forEach((sprite, index) => {
      const parallaxFactor = this.backgroundLayers[index].parallax
      sprite.x = -cameraX * parallaxFactor
    })
  }
}
