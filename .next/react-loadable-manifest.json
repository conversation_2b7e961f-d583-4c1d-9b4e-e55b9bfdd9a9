{"../node_modules/pixi.js/lib/environment-browser/browserExt.mjs -> ./browserAll.mjs": {"id": "../node_modules/pixi.js/lib/environment-browser/browserExt.mjs -> ./browserAll.mjs", "files": ["static/chunks/_app-pages-browser_node_modules_pixi_js_lib_environment-browser_browserAll_mjs.js"]}, "../node_modules/pixi.js/lib/environment-webworker/webworkerExt.mjs -> ./webworkerAll.mjs": {"id": "../node_modules/pixi.js/lib/environment-webworker/webworkerExt.mjs -> ./webworkerAll.mjs", "files": ["static/chunks/_app-pages-browser_node_modules_pixi_js_lib_environment-webworker_webworkerAll_mjs.js"]}, "../node_modules/pixi.js/lib/rendering/renderers/autoDetectRenderer.mjs -> ./gl/WebGLRenderer.mjs": {"id": "../node_modules/pixi.js/lib/rendering/renderers/autoDetectRenderer.mjs -> ./gl/WebGLRenderer.mjs", "files": []}, "../node_modules/pixi.js/lib/rendering/renderers/autoDetectRenderer.mjs -> ./gpu/WebGPURenderer.mjs": {"id": "../node_modules/pixi.js/lib/rendering/renderers/autoDetectRenderer.mjs -> ./gpu/WebGPURenderer.mjs", "files": []}, "lib/InputManager.ts -> nipplejs": {"id": "lib/InputManager.ts -> nipplejs", "files": ["static/chunks/_app-pages-browser_node_modules_nipplejs_dist_nipplejs_js.js"]}}