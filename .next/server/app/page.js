/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fyusuf%2FDesktop%2Fportfolio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fyusuf%2FDesktop%2Fportfolio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Users/<USER>/Desktop/portfolio/src/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/Desktop/portfolio/src/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Desktop/portfolio/src/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fyusuf%2FDesktop%2Fportfolio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGeXVzdWYlMkZEZXNrdG9wJTJGcG9ydGZvbGlvJTJGc3JjJTJGYXBwJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdKQUFvRiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3l1c3VmL0Rlc2t0b3AvcG9ydGZvbGlvL3NyYy9hcHAvcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"9def4ff7b747\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvVXNlcnMveXVzdWYvRGVza3RvcC9wb3J0Zm9saW8vc3JjL2FwcC9nbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjlkZWY0ZmY3Yjc0N1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\nconst metadata = {\n    title: '2D Game Portfolio',\n    description: 'A 2D game built with Next.js and PixiJS'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/portfolio/src/app/layout.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/portfolio/src/app/layout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQ3NCO0FBRWYsTUFBTUEsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7c0JBQU1IOzs7Ozs7Ozs7OztBQUdiIiwic291cmNlcyI6WyIvVXNlcnMveXVzdWYvRGVza3RvcC9wb3J0Zm9saW8vc3JjL2FwcC9sYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tICduZXh0J1xuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogJzJEIEdhbWUgUG9ydGZvbGlvJyxcbiAgZGVzY3JpcHRpb246ICdBIDJEIGdhbWUgYnVpbHQgd2l0aCBOZXh0LmpzIGFuZCBQaXhpSlMnLFxufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbn0pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cbiAgICAgIDxib2R5PntjaGlsZHJlbn08L2JvZHk+XG4gICAgPC9odG1sPlxuICApXG59XG4iXSwibmFtZXMiOlsibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Desktop/portfolio/src/app/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Desktop/portfolio/src/app/page.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGeXVzdWYlMkZEZXNrdG9wJTJGcG9ydGZvbGlvJTJGc3JjJTJGYXBwJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdKQUFvRiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3l1c3VmL0Rlc2t0b3AvcG9ydGZvbGlvL3NyYy9hcHAvcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Game__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Game */ \"(ssr)/./src/components/Game.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            id: \"game-container\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Game__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/portfolio/src/app/page.tsx\",\n                lineNumber: 10,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/portfolio/src/app/page.tsx\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/portfolio/src/app/page.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBR29DO0FBRXJCLFNBQVNDO0lBQ3RCLHFCQUNFLDhEQUFDQztrQkFDQyw0RUFBQ0M7WUFBSUMsSUFBRztzQkFDTiw0RUFBQ0osd0RBQUlBOzs7Ozs7Ozs7Ozs7Ozs7QUFJYiIsInNvdXJjZXMiOlsiL1VzZXJzL3l1c3VmL0Rlc2t0b3AvcG9ydGZvbGlvL3NyYy9hcHAvcGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlUmVmIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgR2FtZSBmcm9tICdAL2NvbXBvbmVudHMvR2FtZSdcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSG9tZSgpIHtcbiAgcmV0dXJuIChcbiAgICA8bWFpbj5cbiAgICAgIDxkaXYgaWQ9XCJnYW1lLWNvbnRhaW5lclwiPlxuICAgICAgICA8R2FtZSAvPlxuICAgICAgPC9kaXY+XG4gICAgPC9tYWluPlxuICApXG59XG4iXSwibmFtZXMiOlsiR2FtZSIsIkhvbWUiLCJtYWluIiwiZGl2IiwiaWQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Game.tsx":
/*!*********************************!*\
  !*** ./src/components/Game.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Game)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var pixi_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! pixi.js */ \"(ssr)/./node_modules/pixi.js/lib/index.mjs\");\n/* harmony import */ var _lib_GameEngine__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/GameEngine */ \"(ssr)/./src/lib/GameEngine.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Game() {\n    const gameRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const gameEngineRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Game.useEffect\": ()=>{\n            if (!gameRef.current || \"undefined\" === 'undefined') return;\n            // Initialize PIXI Application\n            const app = new pixi_js__WEBPACK_IMPORTED_MODULE_2__.Application();\n            const initGame = {\n                \"Game.useEffect.initGame\": async ()=>{\n                    await app.init({\n                        width: window.innerWidth,\n                        height: window.innerHeight,\n                        backgroundColor: 0x000000,\n                        resizeTo: window\n                    });\n                    // Add canvas to DOM\n                    if (gameRef.current) {\n                        gameRef.current.appendChild(app.canvas);\n                    }\n                    // Initialize game engine\n                    gameEngineRef.current = new _lib_GameEngine__WEBPACK_IMPORTED_MODULE_3__.GameEngine(app);\n                    await gameEngineRef.current.init();\n                }\n            }[\"Game.useEffect.initGame\"];\n            initGame().catch(console.error);\n            // Handle window resize\n            const handleResize = {\n                \"Game.useEffect.handleResize\": ()=>{\n                    if (gameEngineRef.current) {\n                        gameEngineRef.current.resize(window.innerWidth, window.innerHeight);\n                    }\n                }\n            }[\"Game.useEffect.handleResize\"];\n            window.addEventListener('resize', handleResize);\n            // Cleanup\n            return ({\n                \"Game.useEffect\": ()=>{\n                    window.removeEventListener('resize', handleResize);\n                    if (gameEngineRef.current) {\n                        gameEngineRef.current.destroy();\n                    }\n                    app.destroy(true);\n                }\n            })[\"Game.useEffect\"];\n        }\n    }[\"Game.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: gameRef,\n                id: \"game-canvas\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/portfolio/src/components/Game.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mobile-controls\",\n                id: \"mobile-joystick\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/portfolio/src/components/Game.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/portfolio/src/components/Game.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Game.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/Background.ts":
/*!*******************************!*\
  !*** ./src/lib/Background.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Background: () => (/* binding */ Background)\n/* harmony export */ });\n/* harmony import */ var pixi_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pixi.js */ \"(ssr)/./node_modules/pixi.js/lib/index.mjs\");\n\nclass Background {\n    constructor(){\n        this.layers = [];\n        this.groundLevel = 0;\n        // Background layer order (back to front)\n        this.backgroundLayers = [\n            {\n                name: 'sky',\n                path: '/background/sky.png',\n                parallax: 0.1\n            },\n            {\n                name: 'jungle_bg',\n                path: '/background/jungle_bg.png',\n                parallax: 0.3\n            },\n            {\n                name: 'trees_bushes',\n                path: '/background/trees&bushes.png',\n                parallax: 0.5\n            },\n            {\n                name: 'lianas',\n                path: '/background/lianas.png',\n                parallax: 0.7\n            },\n            {\n                name: 'grasses',\n                path: '/background/grasses.png',\n                parallax: 0.8\n            },\n            {\n                name: 'grass_road',\n                path: '/background/grass&road.png',\n                parallax: 1.0\n            }\n        ];\n        this.container = new pixi_js__WEBPACK_IMPORTED_MODULE_0__.Container();\n    }\n    async init() {\n        // Load all background textures\n        const textures = {};\n        for (const layer of this.backgroundLayers){\n            try {\n                textures[layer.name] = await pixi_js__WEBPACK_IMPORTED_MODULE_0__.Assets.load(layer.path);\n            } catch (error) {\n                console.warn(`Failed to load background layer: ${layer.path}`, error);\n                // Create a fallback colored rectangle\n                const graphics = new pixi_js__WEBPACK_IMPORTED_MODULE_0__.Graphics();\n                graphics.rect(0, 0, 800, 600);\n                graphics.fill(0x333333);\n                textures[layer.name] = pixi_js__WEBPACK_IMPORTED_MODULE_0__.Texture.from(graphics);\n            }\n        }\n        // Create sprites for each layer\n        for(let i = 0; i < this.backgroundLayers.length; i++){\n            const layerConfig = this.backgroundLayers[i];\n            const texture = textures[layerConfig.name];\n            if (texture) {\n                const sprite = new pixi_js__WEBPACK_IMPORTED_MODULE_0__.Sprite(texture);\n                sprite.name = layerConfig.name;\n                // Scale to fit screen while maintaining aspect ratio\n                this.scaleToFit(sprite);\n                // Position at bottom for ground layers\n                if (layerConfig.name === 'grass_road') {\n                    const screenHeight =  false ? 0 : 600;\n                    sprite.y = screenHeight - sprite.height;\n                    this.groundLevel = sprite.y;\n                }\n                this.layers.push(sprite);\n                this.container.addChild(sprite);\n            }\n        }\n    }\n    scaleToFit(sprite) {\n        const screenWidth =  false ? 0 : 800;\n        const screenHeight =  false ? 0 : 600;\n        // Scale to cover the screen width\n        const scaleX = screenWidth / sprite.texture.width;\n        const scaleY = screenHeight / sprite.texture.height;\n        // Use the larger scale to ensure full coverage\n        const scale = Math.max(scaleX, scaleY);\n        sprite.scale.set(scale);\n        // Center horizontally\n        sprite.x = (screenWidth - sprite.width) / 2;\n    }\n    resize(width, height) {\n        // Rescale all layers to new screen size\n        this.layers.forEach((sprite, index)=>{\n            this.scaleToFit(sprite);\n            // Reposition ground layer\n            if (this.backgroundLayers[index].name === 'grass_road') {\n                sprite.y = height - sprite.height;\n                this.groundLevel = sprite.y;\n            }\n        });\n    }\n    getGroundLevel() {\n        return this.groundLevel;\n    }\n    // Method to implement parallax scrolling if needed later\n    updateParallax(cameraX) {\n        this.layers.forEach((sprite, index)=>{\n            const parallaxFactor = this.backgroundLayers[index].parallax;\n            sprite.x = -cameraX * parallaxFactor;\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/Background.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/GameEngine.ts":
/*!*******************************!*\
  !*** ./src/lib/GameEngine.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GameEngine: () => (/* binding */ GameEngine)\n/* harmony export */ });\n/* harmony import */ var pixi_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pixi.js */ \"(ssr)/./node_modules/pixi.js/lib/index.mjs\");\n/* harmony import */ var _Background__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Background */ \"(ssr)/./src/lib/Background.ts\");\n/* harmony import */ var _Player__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Player */ \"(ssr)/./src/lib/Player.ts\");\n/* harmony import */ var _InputManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./InputManager */ \"(ssr)/./src/lib/InputManager.ts\");\n\n\n\n\nclass GameEngine {\n    constructor(app){\n        this.app = app;\n        this.gameContainer = new pixi_js__WEBPACK_IMPORTED_MODULE_0__.Container();\n        this.app.stage.addChild(this.gameContainer);\n        this.background = new _Background__WEBPACK_IMPORTED_MODULE_1__.Background();\n        this.player = new _Player__WEBPACK_IMPORTED_MODULE_2__.Player();\n        this.inputManager = new _InputManager__WEBPACK_IMPORTED_MODULE_3__.InputManager();\n    }\n    async init() {\n        // Initialize background\n        await this.background.init();\n        this.gameContainer.addChild(this.background.container);\n        // Initialize player\n        await this.player.init();\n        this.gameContainer.addChild(this.player.sprite);\n        // Set up input handling\n        this.inputManager.init();\n        this.setupInputHandlers();\n        // Start game loop\n        this.app.ticker.add(this.gameLoop.bind(this));\n    }\n    setupInputHandlers() {\n        this.inputManager.onMove = (direction)=>{\n            this.player.move(direction);\n        };\n    }\n    gameLoop() {\n        // Update player\n        this.player.update();\n        // Keep player within bounds and on the ground\n        const groundY = this.background.getGroundLevel();\n        this.player.setGroundLevel(groundY);\n        // Keep player within screen bounds\n        const playerBounds = this.player.getBounds();\n        if (playerBounds.x < 0) {\n            this.player.setPosition(0, playerBounds.y);\n        } else if (playerBounds.x + playerBounds.width > this.app.screen.width) {\n            this.player.setPosition(this.app.screen.width - playerBounds.width, playerBounds.y);\n        }\n    }\n    resize(width, height) {\n        this.background.resize(width, height);\n        this.player.resize(width, height);\n    }\n    destroy() {\n        this.inputManager.destroy();\n        this.app.ticker.remove(this.gameLoop.bind(this));\n        this.gameContainer.destroy();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/GameEngine.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/InputManager.ts":
/*!*********************************!*\
  !*** ./src/lib/InputManager.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InputManager: () => (/* binding */ InputManager)\n/* harmony export */ });\nclass InputManager {\n    init() {\n        this.setupKeyboardControls();\n        this.setupMobileControls();\n    }\n    setupKeyboardControls() {\n        // Only run on client side\n        if (true) {\n            return;\n        }\n        // Handle keydown events\n        document.addEventListener('keydown', (event)=>{\n            this.keys[event.code] = true;\n            this.handleKeyboardInput();\n        });\n        // Handle keyup events\n        document.addEventListener('keyup', (event)=>{\n            this.keys[event.code] = false;\n            this.handleKeyboardInput();\n        });\n    }\n    handleKeyboardInput() {\n        const leftPressed = this.keys['KeyA'] || this.keys['ArrowLeft'];\n        const rightPressed = this.keys['KeyD'] || this.keys['ArrowRight'];\n        if (leftPressed && !rightPressed) {\n            this.onMove?.('left');\n        } else if (rightPressed && !leftPressed) {\n            this.onMove?.('right');\n        } else {\n            this.onMove?.('stop');\n        }\n    }\n    async setupMobileControls() {\n        // Only run on client side\n        if (true) {\n            return;\n        }\n        // Only show mobile controls on touch devices\n        if (!('ontouchstart' in window)) {\n            return;\n        }\n        const joystickContainer = document.getElementById('mobile-joystick');\n        if (!joystickContainer) {\n            console.warn('Mobile joystick container not found');\n            return;\n        }\n        try {\n            // Dynamic import to avoid SSR issues\n            const nipplejs = (await __webpack_require__.e(/*! import() */ \"vendor-chunks/nipplejs\").then(__webpack_require__.bind(__webpack_require__, /*! nipplejs */ \"(ssr)/./node_modules/nipplejs/src/index.js\"))).default;\n            // Create virtual joystick\n            this.joystick = nipplejs.create({\n                zone: joystickContainer,\n                mode: 'static',\n                position: {\n                    left: '50px',\n                    bottom: '50px'\n                },\n                color: 'white',\n                size: 100,\n                threshold: 0.1\n            });\n            // Handle joystick events\n            this.joystick.on('move', (evt, data)=>{\n                if (data.direction) {\n                    if (data.direction.x === 'left') {\n                        this.onMove?.('left');\n                    } else if (data.direction.x === 'right') {\n                        this.onMove?.('right');\n                    }\n                }\n            });\n            this.joystick.on('end', ()=>{\n                this.onMove?.('stop');\n            });\n        } catch (error) {\n            console.error('Failed to load mobile controls:', error);\n        }\n    }\n    destroy() {\n        // Only run on client side\n        if (true) {\n            return;\n        }\n        // Remove keyboard event listeners\n        document.removeEventListener('keydown', this.handleKeyboardInput);\n        document.removeEventListener('keyup', this.handleKeyboardInput);\n        // Destroy joystick\n        if (this.joystick) {\n            this.joystick.destroy();\n        }\n    }\n    constructor(){\n        this.keys = {};\n        this.joystick = null;\n        this.onMove = null;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/InputManager.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/Player.ts":
/*!***************************!*\
  !*** ./src/lib/Player.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Player: () => (/* binding */ Player)\n/* harmony export */ });\n/* harmony import */ var pixi_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pixi.js */ \"(ssr)/./node_modules/pixi.js/lib/index.mjs\");\n\nclass Player {\n    constructor(){\n        this.runTextures = [];\n        this.isMoving = false;\n        this.direction = 'right';\n        this.speed = 5;\n        this.groundLevel = 0;\n        this.x = 0;\n        this.y = 0;\n        // Initialize with empty sprite, will be set up in init()\n        this.sprite = new pixi_js__WEBPACK_IMPORTED_MODULE_0__.AnimatedSprite([]);\n    }\n    async init() {\n        try {\n            // Load the run sprite sheet\n            const runTexture = await pixi_js__WEBPACK_IMPORTED_MODULE_0__.Assets.load('/character/Run.png');\n            // Assuming the sprite sheet has frames arranged horizontally\n            // You may need to adjust these values based on your actual sprite sheet\n            const frameWidth = runTexture.width / 8 // Assuming 8 frames\n            ;\n            const frameHeight = runTexture.height;\n            // Create textures for each frame\n            for(let i = 0; i < 8; i++){\n                const frame = new pixi_js__WEBPACK_IMPORTED_MODULE_0__.Rectangle(i * frameWidth, 0, frameWidth, frameHeight);\n                const frameTexture = new pixi_js__WEBPACK_IMPORTED_MODULE_0__.Texture({\n                    source: runTexture.source,\n                    frame: frame\n                });\n                this.runTextures.push(frameTexture);\n            }\n            // Create animated sprite\n            this.sprite = new pixi_js__WEBPACK_IMPORTED_MODULE_0__.AnimatedSprite(this.runTextures);\n            this.sprite.animationSpeed = 0.15;\n            this.sprite.loop = true;\n            // Set initial properties\n            this.sprite.anchor.set(0.5, 1) // Anchor at bottom center\n            ;\n            this.sprite.scale.set(2) // Scale up the character\n            ;\n            // Set initial position\n            this.x = 100;\n            this.y = this.groundLevel;\n            this.updateSpritePosition();\n        } catch (error) {\n            console.error('Failed to load character sprite:', error);\n            // Create a fallback rectangle character\n            const graphics = new pixi_js__WEBPACK_IMPORTED_MODULE_0__.Graphics();\n            graphics.rect(-15, -30, 30, 30);\n            graphics.fill(0xff0000);\n            const fallbackTexture = pixi_js__WEBPACK_IMPORTED_MODULE_0__.Texture.from(graphics);\n            this.sprite = new pixi_js__WEBPACK_IMPORTED_MODULE_0__.AnimatedSprite([\n                fallbackTexture\n            ]);\n            this.sprite.anchor.set(0.5, 1);\n            this.sprite.scale.set(1);\n            this.x = 100;\n            this.y = this.groundLevel;\n            this.updateSpritePosition();\n        }\n    }\n    move(direction) {\n        if (direction === 'stop') {\n            this.isMoving = false;\n            this.sprite.stop();\n        } else {\n            this.isMoving = true;\n            this.direction = direction;\n            // Flip sprite based on direction\n            if (direction === 'left') {\n                this.sprite.scale.x = -Math.abs(this.sprite.scale.x);\n            } else {\n                this.sprite.scale.x = Math.abs(this.sprite.scale.x);\n            }\n            // Start animation\n            if (!this.sprite.playing) {\n                this.sprite.play();\n            }\n        }\n    }\n    update() {\n        if (this.isMoving) {\n            // Move the character\n            if (this.direction === 'left') {\n                this.x -= this.speed;\n            } else if (this.direction === 'right') {\n                this.x += this.speed;\n            }\n            this.updateSpritePosition();\n        }\n    }\n    updateSpritePosition() {\n        this.sprite.x = this.x;\n        this.sprite.y = this.y;\n    }\n    setGroundLevel(groundLevel) {\n        this.groundLevel = groundLevel;\n        this.y = groundLevel;\n        this.updateSpritePosition();\n    }\n    setPosition(x, y) {\n        this.x = x;\n        this.y = y;\n        this.updateSpritePosition();\n    }\n    getBounds() {\n        return {\n            x: this.x - this.sprite.width / 2,\n            y: this.y - this.sprite.height,\n            width: this.sprite.width,\n            height: this.sprite.height\n        };\n    }\n    resize(width, height) {\n        // Adjust player position if needed when screen resizes\n        // Keep player within new bounds\n        if (this.x > width) {\n            this.x = width - 50;\n            this.updateSpritePosition();\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/Player.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/pixi.js","vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@xmldom","vendor-chunks/@pixi","vendor-chunks/ismobilejs","vendor-chunks/eventemitter3","vendor-chunks/earcut","vendor-chunks/parse-svg-path"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fyusuf%2FDesktop%2Fportfolio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();