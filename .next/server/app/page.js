/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fyusuf%2FDesktop%2Fportfolio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fyusuf%2FDesktop%2Fportfolio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Users/<USER>/Desktop/portfolio/src/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/Desktop/portfolio/src/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Desktop/portfolio/src/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fyusuf%2FDesktop%2Fportfolio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGeXVzdWYlMkZEZXNrdG9wJTJGcG9ydGZvbGlvJTJGc3JjJTJGYXBwJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdKQUFvRiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3l1c3VmL0Rlc2t0b3AvcG9ydGZvbGlvL3NyYy9hcHAvcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"9def4ff7b747\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvVXNlcnMveXVzdWYvRGVza3RvcC9wb3J0Zm9saW8vc3JjL2FwcC9nbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjlkZWY0ZmY3Yjc0N1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\nconst metadata = {\n    title: '2D Game Portfolio',\n    description: 'A 2D game built with Next.js and PixiJS'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/portfolio/src/app/layout.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/portfolio/src/app/layout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQ3NCO0FBRWYsTUFBTUEsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7c0JBQU1IOzs7Ozs7Ozs7OztBQUdiIiwic291cmNlcyI6WyIvVXNlcnMveXVzdWYvRGVza3RvcC9wb3J0Zm9saW8vc3JjL2FwcC9sYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tICduZXh0J1xuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogJzJEIEdhbWUgUG9ydGZvbGlvJyxcbiAgZGVzY3JpcHRpb246ICdBIDJEIGdhbWUgYnVpbHQgd2l0aCBOZXh0LmpzIGFuZCBQaXhpSlMnLFxufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbn0pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cbiAgICAgIDxib2R5PntjaGlsZHJlbn08L2JvZHk+XG4gICAgPC9odG1sPlxuICApXG59XG4iXSwibmFtZXMiOlsibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Desktop/portfolio/src/app/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Desktop/portfolio/src/app/page.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGeXVzdWYlMkZEZXNrdG9wJTJGcG9ydGZvbGlvJTJGc3JjJTJGYXBwJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdKQUFvRiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3l1c3VmL0Rlc2t0b3AvcG9ydGZvbGlvL3NyYy9hcHAvcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Game__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Game */ \"(ssr)/./src/components/Game.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            id: \"game-container\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Game__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/portfolio/src/app/page.tsx\",\n                lineNumber: 10,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/portfolio/src/app/page.tsx\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/portfolio/src/app/page.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBR29DO0FBRXJCLFNBQVNDO0lBQ3RCLHFCQUNFLDhEQUFDQztrQkFDQyw0RUFBQ0M7WUFBSUMsSUFBRztzQkFDTiw0RUFBQ0osd0RBQUlBOzs7Ozs7Ozs7Ozs7Ozs7QUFJYiIsInNvdXJjZXMiOlsiL1VzZXJzL3l1c3VmL0Rlc2t0b3AvcG9ydGZvbGlvL3NyYy9hcHAvcGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlUmVmIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgR2FtZSBmcm9tICdAL2NvbXBvbmVudHMvR2FtZSdcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSG9tZSgpIHtcbiAgcmV0dXJuIChcbiAgICA8bWFpbj5cbiAgICAgIDxkaXYgaWQ9XCJnYW1lLWNvbnRhaW5lclwiPlxuICAgICAgICA8R2FtZSAvPlxuICAgICAgPC9kaXY+XG4gICAgPC9tYWluPlxuICApXG59XG4iXSwibmFtZXMiOlsiR2FtZSIsIkhvbWUiLCJtYWluIiwiZGl2IiwiaWQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Game.tsx":
/*!*********************************!*\
  !*** ./src/components/Game.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Game)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var pixi_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! pixi.js */ \"(ssr)/./node_modules/pixi.js/lib/index.mjs\");\n/* harmony import */ var _lib_GameEngine__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/GameEngine */ \"(ssr)/./src/lib/GameEngine.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Game() {\n    const gameRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const gameEngineRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Game.useEffect\": ()=>{\n            if (!gameRef.current || \"undefined\" === 'undefined') return;\n            // Initialize PIXI Application\n            const app = new pixi_js__WEBPACK_IMPORTED_MODULE_2__.Application();\n            const initGame = {\n                \"Game.useEffect.initGame\": async ()=>{\n                    await app.init({\n                        width: window.innerWidth,\n                        height: window.innerHeight,\n                        backgroundColor: 0x000000,\n                        resizeTo: window\n                    });\n                    // Add canvas to DOM\n                    if (gameRef.current) {\n                        gameRef.current.appendChild(app.canvas);\n                    }\n                    // Initialize game engine\n                    gameEngineRef.current = new _lib_GameEngine__WEBPACK_IMPORTED_MODULE_3__.GameEngine(app);\n                    await gameEngineRef.current.init();\n                }\n            }[\"Game.useEffect.initGame\"];\n            initGame().catch(console.error);\n            // Handle window resize\n            const handleResize = {\n                \"Game.useEffect.handleResize\": ()=>{\n                    if (gameEngineRef.current) {\n                        gameEngineRef.current.resize(window.innerWidth, window.innerHeight);\n                    }\n                }\n            }[\"Game.useEffect.handleResize\"];\n            window.addEventListener('resize', handleResize);\n            // Cleanup\n            return ({\n                \"Game.useEffect\": ()=>{\n                    window.removeEventListener('resize', handleResize);\n                    if (gameEngineRef.current) {\n                        gameEngineRef.current.destroy();\n                    }\n                    app.destroy(true);\n                }\n            })[\"Game.useEffect\"];\n        }\n    }[\"Game.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: gameRef,\n                id: \"game-canvas\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/portfolio/src/components/Game.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mobile-controls\",\n                id: \"mobile-joystick\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/portfolio/src/components/Game.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/portfolio/src/components/Game.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Game.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/Background.ts":
/*!*******************************!*\
  !*** ./src/lib/Background.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Background: () => (/* binding */ Background)\n/* harmony export */ });\n/* harmony import */ var pixi_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pixi.js */ \"(ssr)/./node_modules/pixi.js/lib/index.mjs\");\n\nclass Background {\n    constructor(){\n        this.layers = [];\n        this.groundLevel = 0;\n        // Background layer order (back to front)\n        this.backgroundLayers = [\n            {\n                name: 'sky',\n                path: '/background/sky.png',\n                parallax: 0.1\n            },\n            {\n                name: 'jungle_bg',\n                path: '/background/jungle_bg.png',\n                parallax: 0.3\n            },\n            {\n                name: 'trees_bushes',\n                path: '/background/trees&bushes.png',\n                parallax: 0.5\n            },\n            {\n                name: 'lianas',\n                path: '/background/lianas.png',\n                parallax: 0.7\n            },\n            {\n                name: 'grasses',\n                path: '/background/grasses.png',\n                parallax: 0.8\n            },\n            {\n                name: 'grass_road',\n                path: '/background/grass&road.png',\n                parallax: 1.0\n            }\n        ];\n        this.container = new pixi_js__WEBPACK_IMPORTED_MODULE_0__.Container();\n    }\n    async init() {\n        // Load all background textures\n        const textures = {};\n        for (const layer of this.backgroundLayers){\n            try {\n                textures[layer.name] = await pixi_js__WEBPACK_IMPORTED_MODULE_0__.Assets.load(layer.path);\n            } catch (error) {\n                console.warn(`Failed to load background layer: ${layer.path}`, error);\n                // Create a fallback colored rectangle\n                if (typeof document !== 'undefined') {\n                    const canvas = document.createElement('canvas');\n                    canvas.width = 800;\n                    canvas.height = 600;\n                    const ctx = canvas.getContext('2d');\n                    ctx.fillStyle = '#333333';\n                    ctx.fillRect(0, 0, 800, 600);\n                    textures[layer.name] = pixi_js__WEBPACK_IMPORTED_MODULE_0__.Texture.from(canvas);\n                } else {\n                    continue;\n                }\n            }\n        }\n        // Create sprites for each layer\n        for(let i = 0; i < this.backgroundLayers.length; i++){\n            const layerConfig = this.backgroundLayers[i];\n            const texture = textures[layerConfig.name];\n            if (texture) {\n                const sprite = new pixi_js__WEBPACK_IMPORTED_MODULE_0__.Sprite(texture);\n                sprite.name = layerConfig.name;\n                // Scale to fit screen while maintaining aspect ratio\n                this.scaleToFit(sprite);\n                // Position at bottom for ground layers\n                if (layerConfig.name === 'grass_road') {\n                    const screenHeight =  false ? 0 : 600;\n                    sprite.y = screenHeight - sprite.height;\n                    this.groundLevel = sprite.y;\n                }\n                this.layers.push(sprite);\n                this.container.addChild(sprite);\n            }\n        }\n    }\n    scaleToFit(sprite) {\n        const screenWidth =  false ? 0 : 800;\n        const screenHeight =  false ? 0 : 600;\n        // Scale to cover the screen width\n        const scaleX = screenWidth / sprite.texture.width;\n        const scaleY = screenHeight / sprite.texture.height;\n        // Use the larger scale to ensure full coverage\n        const scale = Math.max(scaleX, scaleY);\n        sprite.scale.set(scale);\n        // Center horizontally\n        sprite.x = (screenWidth - sprite.width) / 2;\n    }\n    resize(width, height) {\n        // Rescale all layers to new screen size\n        this.layers.forEach((sprite, index)=>{\n            this.scaleToFit(sprite);\n            // Reposition ground layer\n            if (this.backgroundLayers[index].name === 'grass_road') {\n                sprite.y = height - sprite.height;\n                this.groundLevel = sprite.y;\n            }\n        });\n    }\n    getGroundLevel() {\n        return this.groundLevel;\n    }\n    // Method to implement parallax scrolling if needed later\n    updateParallax(cameraX) {\n        this.layers.forEach((sprite, index)=>{\n            const parallaxFactor = this.backgroundLayers[index].parallax;\n            sprite.x = -cameraX * parallaxFactor;\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/Background.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/GameEngine.ts":
/*!*******************************!*\
  !*** ./src/lib/GameEngine.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GameEngine: () => (/* binding */ GameEngine)\n/* harmony export */ });\n/* harmony import */ var pixi_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pixi.js */ \"(ssr)/./node_modules/pixi.js/lib/index.mjs\");\n/* harmony import */ var _Background__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Background */ \"(ssr)/./src/lib/Background.ts\");\n/* harmony import */ var _Player__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Player */ \"(ssr)/./src/lib/Player.ts\");\n/* harmony import */ var _InputManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./InputManager */ \"(ssr)/./src/lib/InputManager.ts\");\n\n\n\n\nclass GameEngine {\n    constructor(app){\n        this.app = app;\n        this.gameContainer = new pixi_js__WEBPACK_IMPORTED_MODULE_0__.Container();\n        this.app.stage.addChild(this.gameContainer);\n        this.background = new _Background__WEBPACK_IMPORTED_MODULE_1__.Background();\n        this.player = new _Player__WEBPACK_IMPORTED_MODULE_2__.Player();\n        this.inputManager = new _InputManager__WEBPACK_IMPORTED_MODULE_3__.InputManager();\n    }\n    async init() {\n        // Initialize background\n        await this.background.init();\n        this.gameContainer.addChild(this.background.container);\n        // Initialize player\n        await this.player.init();\n        this.gameContainer.addChild(this.player.sprite);\n        // Set up input handling\n        this.inputManager.init();\n        this.setupInputHandlers();\n        // Start game loop\n        this.app.ticker.add(this.gameLoop.bind(this));\n    }\n    setupInputHandlers() {\n        this.inputManager.onMove = (direction)=>{\n            this.player.move(direction);\n        };\n    }\n    gameLoop() {\n        // Update player\n        this.player.update();\n        // Keep player within bounds and on the ground\n        const groundY = this.background.getGroundLevel();\n        this.player.setGroundLevel(groundY);\n        // Keep player within screen bounds\n        const playerBounds = this.player.getBounds();\n        if (playerBounds.x < 0) {\n            this.player.setPosition(0, playerBounds.y);\n        } else if (playerBounds.x + playerBounds.width > this.app.screen.width) {\n            this.player.setPosition(this.app.screen.width - playerBounds.width, playerBounds.y);\n        }\n    }\n    resize(width, height) {\n        this.background.resize(width, height);\n        this.player.resize(width, height);\n    }\n    destroy() {\n        this.inputManager.destroy();\n        this.app.ticker.remove(this.gameLoop.bind(this));\n        this.gameContainer.destroy();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/GameEngine.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/InputManager.ts":
/*!*********************************!*\
  !*** ./src/lib/InputManager.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InputManager: () => (/* binding */ InputManager)\n/* harmony export */ });\nclass InputManager {\n    init() {\n        this.setupKeyboardControls();\n        this.setupMobileControls();\n    }\n    setupKeyboardControls() {\n        // Only run on client side\n        if (true) {\n            return;\n        }\n        // Handle keydown events\n        document.addEventListener('keydown', (event)=>{\n            this.keys[event.code] = true;\n            this.handleKeyboardInput();\n        });\n        // Handle keyup events\n        document.addEventListener('keyup', (event)=>{\n            this.keys[event.code] = false;\n            this.handleKeyboardInput();\n        });\n    }\n    handleKeyboardInput() {\n        const leftPressed = this.keys['KeyA'] || this.keys['ArrowLeft'];\n        const rightPressed = this.keys['KeyD'] || this.keys['ArrowRight'];\n        if (leftPressed && !rightPressed) {\n            this.onMove?.('left');\n        } else if (rightPressed && !leftPressed) {\n            this.onMove?.('right');\n        } else {\n            this.onMove?.('stop');\n        }\n    }\n    async setupMobileControls() {\n        // Only run on client side\n        if (true) {\n            return;\n        }\n        // Only show mobile controls on touch devices\n        if (!('ontouchstart' in window)) {\n            return;\n        }\n        const joystickContainer = document.getElementById('mobile-joystick');\n        if (!joystickContainer) {\n            console.warn('Mobile joystick container not found');\n            return;\n        }\n        try {\n            // Dynamic import to avoid SSR issues\n            const nipplejs = (await __webpack_require__.e(/*! import() */ \"vendor-chunks/nipplejs\").then(__webpack_require__.bind(__webpack_require__, /*! nipplejs */ \"(ssr)/./node_modules/nipplejs/src/index.js\"))).default;\n            // Create virtual joystick\n            this.joystick = nipplejs.create({\n                zone: joystickContainer,\n                mode: 'static',\n                position: {\n                    left: '50px',\n                    bottom: '50px'\n                },\n                color: 'white',\n                size: 100,\n                threshold: 0.1\n            });\n            // Handle joystick events\n            this.joystick.on('move', (evt, data)=>{\n                if (data.direction) {\n                    if (data.direction.x === 'left') {\n                        this.onMove?.('left');\n                    } else if (data.direction.x === 'right') {\n                        this.onMove?.('right');\n                    }\n                }\n            });\n            this.joystick.on('end', ()=>{\n                this.onMove?.('stop');\n            });\n        } catch (error) {\n            console.error('Failed to load mobile controls:', error);\n        }\n    }\n    destroy() {\n        // Only run on client side\n        if (true) {\n            return;\n        }\n        // Remove keyboard event listeners\n        document.removeEventListener('keydown', this.handleKeyboardInput);\n        document.removeEventListener('keyup', this.handleKeyboardInput);\n        // Destroy joystick\n        if (this.joystick) {\n            this.joystick.destroy();\n        }\n    }\n    constructor(){\n        this.keys = {};\n        this.joystick = null;\n        this.onMove = null;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/InputManager.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/Player.ts":
/*!***************************!*\
  !*** ./src/lib/Player.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Player: () => (/* binding */ Player)\n/* harmony export */ });\n/* harmony import */ var pixi_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pixi.js */ \"(ssr)/./node_modules/pixi.js/lib/index.mjs\");\n\nclass Player {\n    constructor(){\n        this.runTextures = [];\n        this.isMoving = false;\n        this.direction = 'right';\n        this.speed = 5;\n        this.groundLevel = 0;\n        this.x = 0;\n        this.y = 0;\n        // Create a temporary texture for initialization\n        let tempTexture;\n        if (typeof document !== 'undefined') {\n            const canvas = document.createElement('canvas');\n            canvas.width = 32;\n            canvas.height = 32;\n            const ctx = canvas.getContext('2d');\n            ctx.fillStyle = '#ff0000';\n            ctx.fillRect(0, 0, 32, 32);\n            tempTexture = pixi_js__WEBPACK_IMPORTED_MODULE_0__.Texture.from(canvas);\n        } else {\n            // Create empty texture for SSR\n            tempTexture = pixi_js__WEBPACK_IMPORTED_MODULE_0__.Texture.EMPTY;\n        }\n        // Initialize with temporary sprite, will be replaced in init()\n        this.sprite = new pixi_js__WEBPACK_IMPORTED_MODULE_0__.AnimatedSprite([\n            tempTexture\n        ]);\n    }\n    async init() {\n        try {\n            // Load the run sprite sheet\n            const runTexture = await pixi_js__WEBPACK_IMPORTED_MODULE_0__.Assets.load('/character/Run.png');\n            // Assuming the sprite sheet has frames arranged horizontally\n            // You may need to adjust these values based on your actual sprite sheet\n            const frameWidth = runTexture.width / 8 // Assuming 8 frames\n            ;\n            const frameHeight = runTexture.height;\n            // Create textures for each frame\n            for(let i = 0; i < 8; i++){\n                const frame = new pixi_js__WEBPACK_IMPORTED_MODULE_0__.Rectangle(i * frameWidth, 0, frameWidth, frameHeight);\n                const frameTexture = new pixi_js__WEBPACK_IMPORTED_MODULE_0__.Texture({\n                    source: runTexture.source,\n                    frame: frame\n                });\n                this.runTextures.push(frameTexture);\n            }\n            // Replace the temporary sprite with the animated sprite\n            const parent = this.sprite.parent;\n            if (parent) {\n                parent.removeChild(this.sprite);\n            }\n            this.sprite = new pixi_js__WEBPACK_IMPORTED_MODULE_0__.AnimatedSprite(this.runTextures);\n            this.sprite.animationSpeed = 0.15;\n            this.sprite.loop = true;\n            // Set initial properties\n            this.sprite.anchor.set(0.5, 1) // Anchor at bottom center\n            ;\n            this.sprite.scale.set(2) // Scale up the character\n            ;\n            // Re-add to parent if it existed\n            if (parent) {\n                parent.addChild(this.sprite);\n            }\n            // Set initial position\n            this.x = 100;\n            this.y = this.groundLevel;\n            this.updateSpritePosition();\n        } catch (error) {\n            console.error('Failed to load character sprite:', error);\n            // Create a fallback rectangle character using canvas\n            let fallbackTexture;\n            if (typeof document !== 'undefined') {\n                const canvas = document.createElement('canvas');\n                canvas.width = 30;\n                canvas.height = 30;\n                const ctx = canvas.getContext('2d');\n                ctx.fillStyle = '#ff0000';\n                ctx.fillRect(0, 0, 30, 30);\n                fallbackTexture = pixi_js__WEBPACK_IMPORTED_MODULE_0__.Texture.from(canvas);\n            } else {\n                fallbackTexture = pixi_js__WEBPACK_IMPORTED_MODULE_0__.Texture.EMPTY;\n            }\n            // Replace the temporary sprite with the fallback sprite\n            const parent = this.sprite.parent;\n            if (parent) {\n                parent.removeChild(this.sprite);\n            }\n            this.sprite = new pixi_js__WEBPACK_IMPORTED_MODULE_0__.AnimatedSprite([\n                fallbackTexture\n            ]);\n            this.sprite.anchor.set(0.5, 1);\n            this.sprite.scale.set(1);\n            // Re-add to parent if it existed\n            if (parent) {\n                parent.addChild(this.sprite);\n            }\n            this.x = 100;\n            this.y = this.groundLevel;\n            this.updateSpritePosition();\n        }\n    }\n    move(direction) {\n        if (direction === 'stop') {\n            this.isMoving = false;\n            this.sprite.stop();\n        } else {\n            this.isMoving = true;\n            this.direction = direction;\n            // Flip sprite based on direction\n            if (direction === 'left') {\n                this.sprite.scale.x = -Math.abs(this.sprite.scale.x);\n            } else {\n                this.sprite.scale.x = Math.abs(this.sprite.scale.x);\n            }\n            // Start animation\n            if (!this.sprite.playing) {\n                this.sprite.play();\n            }\n        }\n    }\n    update() {\n        if (this.isMoving) {\n            // Move the character\n            if (this.direction === 'left') {\n                this.x -= this.speed;\n            } else if (this.direction === 'right') {\n                this.x += this.speed;\n            }\n            this.updateSpritePosition();\n        }\n    }\n    updateSpritePosition() {\n        this.sprite.x = this.x;\n        this.sprite.y = this.y;\n    }\n    setGroundLevel(groundLevel) {\n        this.groundLevel = groundLevel;\n        this.y = groundLevel;\n        this.updateSpritePosition();\n    }\n    setPosition(x, y) {\n        this.x = x;\n        this.y = y;\n        this.updateSpritePosition();\n    }\n    getBounds() {\n        return {\n            x: this.x - this.sprite.width / 2,\n            y: this.y - this.sprite.height,\n            width: this.sprite.width,\n            height: this.sprite.height\n        };\n    }\n    resize(width, height) {\n        // Adjust player position if needed when screen resizes\n        // Keep player within new bounds\n        if (this.x > width) {\n            this.x = width - 50;\n            this.updateSpritePosition();\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/Player.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/pixi.js","vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@xmldom","vendor-chunks/@pixi","vendor-chunks/ismobilejs","vendor-chunks/eventemitter3","vendor-chunks/earcut","vendor-chunks/parse-svg-path"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fyusuf%2FDesktop%2Fportfolio%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fyusuf%2FDesktop%2Fportfolio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();