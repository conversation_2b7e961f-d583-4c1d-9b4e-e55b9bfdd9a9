"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/ismobilejs";
exports.ids = ["vendor-chunks/ismobilejs"];
exports.modules = {

/***/ "(ssr)/./node_modules/ismobilejs/esm/index.js":
/*!**********************************************!*\
  !*** ./node_modules/ismobilejs/esm/index.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport safe */ _isMobile__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _isMobile__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./isMobile */ \"(ssr)/./node_modules/ismobilejs/esm/isMobile.js\");\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaXNtb2JpbGVqcy9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBMkI7QUFDVTtBQUNyQyIsInNvdXJjZXMiOlsiL1VzZXJzL3l1c3VmL0Rlc2t0b3AvcG9ydGZvbGlvL25vZGVfbW9kdWxlcy9pc21vYmlsZWpzL2VzbS9pbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tICcuL2lzTW9iaWxlJztcbmV4cG9ydCB7IGRlZmF1bHQgfSBmcm9tICcuL2lzTW9iaWxlJztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ismobilejs/esm/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ismobilejs/esm/isMobile.js":
/*!*************************************************!*\
  !*** ./node_modules/ismobilejs/esm/isMobile.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ isMobile)\n/* harmony export */ });\nvar appleIphone = /iPhone/i;\nvar appleIpod = /iPod/i;\nvar appleTablet = /iPad/i;\nvar appleUniversal = /\\biOS-universal(?:.+)Mac\\b/i;\nvar androidPhone = /\\bAndroid(?:.+)Mobile\\b/i;\nvar androidTablet = /Android/i;\nvar amazonPhone = /(?:SD4930UR|\\bSilk(?:.+)Mobile\\b)/i;\nvar amazonTablet = /Silk/i;\nvar windowsPhone = /Windows Phone/i;\nvar windowsTablet = /\\bWindows(?:.+)ARM\\b/i;\nvar otherBlackBerry = /BlackBerry/i;\nvar otherBlackBerry10 = /BB10/i;\nvar otherOpera = /Opera Mini/i;\nvar otherChrome = /\\b(CriOS|Chrome)(?:.+)Mobile/i;\nvar otherFirefox = /Mobile(?:.+)Firefox\\b/i;\nvar isAppleTabletOnIos13 = function (navigator) {\n    return (typeof navigator !== 'undefined' &&\n        navigator.platform === 'MacIntel' &&\n        typeof navigator.maxTouchPoints === 'number' &&\n        navigator.maxTouchPoints > 1 &&\n        typeof MSStream === 'undefined');\n};\nfunction createMatch(userAgent) {\n    return function (regex) { return regex.test(userAgent); };\n}\nfunction isMobile(param) {\n    var nav = {\n        userAgent: '',\n        platform: '',\n        maxTouchPoints: 0\n    };\n    if (!param && typeof navigator !== 'undefined') {\n        nav = {\n            userAgent: navigator.userAgent,\n            platform: navigator.platform,\n            maxTouchPoints: navigator.maxTouchPoints || 0\n        };\n    }\n    else if (typeof param === 'string') {\n        nav.userAgent = param;\n    }\n    else if (param && param.userAgent) {\n        nav = {\n            userAgent: param.userAgent,\n            platform: param.platform,\n            maxTouchPoints: param.maxTouchPoints || 0\n        };\n    }\n    var userAgent = nav.userAgent;\n    var tmp = userAgent.split('[FBAN');\n    if (typeof tmp[1] !== 'undefined') {\n        userAgent = tmp[0];\n    }\n    tmp = userAgent.split('Twitter');\n    if (typeof tmp[1] !== 'undefined') {\n        userAgent = tmp[0];\n    }\n    var match = createMatch(userAgent);\n    var result = {\n        apple: {\n            phone: match(appleIphone) && !match(windowsPhone),\n            ipod: match(appleIpod),\n            tablet: !match(appleIphone) &&\n                (match(appleTablet) || isAppleTabletOnIos13(nav)) &&\n                !match(windowsPhone),\n            universal: match(appleUniversal),\n            device: (match(appleIphone) ||\n                match(appleIpod) ||\n                match(appleTablet) ||\n                match(appleUniversal) ||\n                isAppleTabletOnIos13(nav)) &&\n                !match(windowsPhone)\n        },\n        amazon: {\n            phone: match(amazonPhone),\n            tablet: !match(amazonPhone) && match(amazonTablet),\n            device: match(amazonPhone) || match(amazonTablet)\n        },\n        android: {\n            phone: (!match(windowsPhone) && match(amazonPhone)) ||\n                (!match(windowsPhone) && match(androidPhone)),\n            tablet: !match(windowsPhone) &&\n                !match(amazonPhone) &&\n                !match(androidPhone) &&\n                (match(amazonTablet) || match(androidTablet)),\n            device: (!match(windowsPhone) &&\n                (match(amazonPhone) ||\n                    match(amazonTablet) ||\n                    match(androidPhone) ||\n                    match(androidTablet))) ||\n                match(/\\bokhttp\\b/i)\n        },\n        windows: {\n            phone: match(windowsPhone),\n            tablet: match(windowsTablet),\n            device: match(windowsPhone) || match(windowsTablet)\n        },\n        other: {\n            blackberry: match(otherBlackBerry),\n            blackberry10: match(otherBlackBerry10),\n            opera: match(otherOpera),\n            firefox: match(otherFirefox),\n            chrome: match(otherChrome),\n            device: match(otherBlackBerry) ||\n                match(otherBlackBerry10) ||\n                match(otherOpera) ||\n                match(otherFirefox) ||\n                match(otherChrome)\n        },\n        any: false,\n        phone: false,\n        tablet: false\n    };\n    result.any =\n        result.apple.device ||\n            result.android.device ||\n            result.windows.device ||\n            result.other.device;\n    result.phone =\n        result.apple.phone || result.android.phone || result.windows.phone;\n    result.tablet =\n        result.apple.tablet || result.android.tablet || result.windows.tablet;\n    return result;\n}\n//# sourceMappingURL=isMobile.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ismobilejs/esm/isMobile.js\n");

/***/ })

};
;