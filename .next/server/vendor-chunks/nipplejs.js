"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/nipplejs";
exports.ids = ["vendor-chunks/nipplejs"];
exports.modules = {

/***/ "(ssr)/./node_modules/nipplejs/src/collection.js":
/*!*************************************************!*\
  !*** ./node_modules/nipplejs/src/collection.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _nipple__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./nipple */ \"(ssr)/./node_modules/nipplejs/src/nipple.js\");\n/* harmony import */ var _super__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./super */ \"(ssr)/./node_modules/nipplejs/src/super.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/nipplejs/src/utils.js\");\n\n\n\n\n///////////////////////////\n///   THE COLLECTION    ///\n///////////////////////////\n\nfunction Collection (manager, options) {\n    var self = this;\n    self.nipples = [];\n    self.idles = [];\n    self.actives = [];\n    self.ids = [];\n    self.pressureIntervals = {};\n    self.manager = manager;\n    self.id = Collection.id;\n    Collection.id += 1;\n\n    // Defaults\n    self.defaults = {\n        zone: document.body,\n        multitouch: false,\n        maxNumberOfNipples: 10,\n        mode: 'dynamic',\n        position: {top: 0, left: 0},\n        catchDistance: 200,\n        size: 100,\n        threshold: 0.1,\n        color: 'white',\n        fadeTime: 250,\n        dataOnly: false,\n        restJoystick: true,\n        restOpacity: 0.5,\n        lockX: false,\n        lockY: false,\n        shape: 'circle',\n        dynamicPage: false,\n        follow: false\n    };\n\n    self.config(options);\n\n    // Overwrites\n    if (self.options.mode === 'static' || self.options.mode === 'semi') {\n        self.options.multitouch = false;\n    }\n\n    if (!self.options.multitouch) {\n        self.options.maxNumberOfNipples = 1;\n    }\n    const computedStyle = getComputedStyle(self.options.zone.parentElement);\n    if (computedStyle && computedStyle.display === 'flex') {\n        self.parentIsFlex = true;\n    }\n\n    self.updateBox();\n    self.prepareNipples();\n    self.bindings();\n    self.begin();\n\n    return self.nipples;\n}\n\nCollection.prototype = new _super__WEBPACK_IMPORTED_MODULE_1__[\"default\"]();\nCollection.constructor = Collection;\nCollection.id = 0;\n\nCollection.prototype.prepareNipples = function () {\n    var self = this;\n    var nips = self.nipples;\n\n    // Public API Preparation.\n    nips.on = self.on.bind(self);\n    nips.off = self.off.bind(self);\n    nips.options = self.options;\n    nips.destroy = self.destroy.bind(self);\n    nips.ids = self.ids;\n    nips.id = self.id;\n    nips.processOnMove = self.processOnMove.bind(self);\n    nips.processOnEnd = self.processOnEnd.bind(self);\n    nips.get = function (id) {\n        if (id === undefined) {\n            return nips[0];\n        }\n        for (var i = 0, max = nips.length; i < max; i += 1) {\n            if (nips[i].identifier === id) {\n                return nips[i];\n            }\n        }\n        return false;\n    };\n};\n\nCollection.prototype.bindings = function () {\n    var self = this;\n    // Touch start event.\n    self.bindEvt(self.options.zone, 'start');\n    // Avoid native touch actions (scroll, zoom etc...) on the zone.\n    self.options.zone.style.touchAction = 'none';\n    self.options.zone.style.msTouchAction = 'none';\n};\n\nCollection.prototype.begin = function () {\n    var self = this;\n    var opts = self.options;\n\n    // We place our static nipple\n    // if needed.\n    if (opts.mode === 'static') {\n        var nipple = self.createNipple(\n            opts.position,\n            self.manager.getIdentifier()\n        );\n        // Add it to the dom.\n        nipple.add();\n        // Store it in idles.\n        self.idles.push(nipple);\n    }\n};\n\n// Nipple Factory\nCollection.prototype.createNipple = function (position, identifier) {\n    var self = this;\n    var scroll = self.manager.scroll;\n    var toPutOn = {};\n    var opts = self.options;\n    var offset = {\n        x: self.parentIsFlex ? scroll.x : (scroll.x + self.box.left),\n        y: self.parentIsFlex ? scroll.y : (scroll.y + self.box.top)\n    };\n\n    if (position.x && position.y) {\n        toPutOn = {\n            x: position.x - offset.x,\n            y: position.y - offset.y\n        };\n    } else if (\n        position.top ||\n        position.right ||\n        position.bottom ||\n        position.left\n    ) {\n\n        // We need to compute the position X / Y of the joystick.\n        var dumb = document.createElement('DIV');\n        dumb.style.display = 'hidden';\n        dumb.style.top = position.top;\n        dumb.style.right = position.right;\n        dumb.style.bottom = position.bottom;\n        dumb.style.left = position.left;\n        dumb.style.position = 'absolute';\n\n        opts.zone.appendChild(dumb);\n        var dumbBox = dumb.getBoundingClientRect();\n        opts.zone.removeChild(dumb);\n\n        toPutOn = position;\n        position = {\n            x: dumbBox.left + scroll.x,\n            y: dumbBox.top + scroll.y\n        };\n    }\n\n    var nipple = new _nipple__WEBPACK_IMPORTED_MODULE_0__[\"default\"](self, {\n        color: opts.color,\n        size: opts.size,\n        threshold: opts.threshold,\n        fadeTime: opts.fadeTime,\n        dataOnly: opts.dataOnly,\n        restJoystick: opts.restJoystick,\n        restOpacity: opts.restOpacity,\n        mode: opts.mode,\n        identifier: identifier,\n        position: position,\n        zone: opts.zone,\n        frontPosition: {\n            x: 0,\n            y: 0\n        },\n        shape: opts.shape\n    });\n\n    if (!opts.dataOnly) {\n        _utils__WEBPACK_IMPORTED_MODULE_2__.applyPosition(nipple.ui.el, toPutOn);\n        _utils__WEBPACK_IMPORTED_MODULE_2__.applyPosition(nipple.ui.front, nipple.frontPosition);\n    }\n    self.nipples.push(nipple);\n    self.trigger('added ' + nipple.identifier + ':added', nipple);\n    self.manager.trigger('added ' + nipple.identifier + ':added', nipple);\n\n    self.bindNipple(nipple);\n\n    return nipple;\n};\n\nCollection.prototype.updateBox = function () {\n    var self = this;\n    self.box = self.options.zone.getBoundingClientRect();\n};\n\nCollection.prototype.bindNipple = function (nipple) {\n    var self = this;\n    var type;\n    // Bubble up identified events.\n    var handler = function (evt, data) {\n        // Identify the event type with the nipple's id.\n        type = evt.type + ' ' + data.id + ':' + evt.type;\n        self.trigger(type, data);\n    };\n\n    // When it gets destroyed.\n    nipple.on('destroyed', self.onDestroyed.bind(self));\n\n    // Other events that will get bubbled up.\n    nipple.on('shown hidden rested dir plain', handler);\n    nipple.on('dir:up dir:right dir:down dir:left', handler);\n    nipple.on('plain:up plain:right plain:down plain:left', handler);\n};\n\nCollection.prototype.pressureFn = function (touch, nipple, identifier) {\n    var self = this;\n    var previousPressure = 0;\n    clearInterval(self.pressureIntervals[identifier]);\n    // Create an interval that will read the pressure every 100ms\n    self.pressureIntervals[identifier] = setInterval(function () {\n        var pressure = touch.force || touch.pressure ||\n            touch.webkitForce || 0;\n        if (pressure !== previousPressure) {\n            nipple.trigger('pressure', pressure);\n            self.trigger('pressure ' +\n                nipple.identifier + ':pressure', pressure);\n            previousPressure = pressure;\n        }\n    }.bind(self), 100);\n};\n\nCollection.prototype.onstart = function (evt) {\n    var self = this;\n    var opts = self.options;\n    var origEvt = evt;\n    evt = _utils__WEBPACK_IMPORTED_MODULE_2__.prepareEvent(evt);\n\n    // Update the box position\n    self.updateBox();\n\n    var process = function (touch) {\n        // If we can create new nipples\n        // meaning we don't have more active nipples than we should.\n        if (self.actives.length < opts.maxNumberOfNipples) {\n            self.processOnStart(touch);\n        }\n        else if(origEvt.type.match(/^touch/)){\n            // zombies occur when end event is not received on Safari\n            // first touch removed before second touch, we need to catch up...\n            // so remove where touches in manager that no longer exist\n            Object.keys(self.manager.ids).forEach(function(k){\n                if(Object.values(origEvt.touches).findIndex(function(t){return t.identifier===k;}) < 0){\n                    // manager has id that doesn't exist in touches\n                    var e = [evt[0]];\n                    e.identifier = k;\n                    self.processOnEnd(e);\n                }\n            });\n            if(self.actives.length < opts.maxNumberOfNipples){\n                self.processOnStart(touch);\n            }\n        }\n    };\n\n    _utils__WEBPACK_IMPORTED_MODULE_2__.map(evt, process);\n\n    // We ask upstream to bind the document\n    // on 'move' and 'end'\n    self.manager.bindDocument();\n    return false;\n};\n\nCollection.prototype.processOnStart = function (evt) {\n    var self = this;\n    var opts = self.options;\n    var indexInIdles;\n    var identifier = self.manager.getIdentifier(evt);\n    var pressure = evt.force || evt.pressure || evt.webkitForce || 0;\n    var position = {\n        x: evt.pageX,\n        y: evt.pageY\n    };\n\n    var nipple = self.getOrCreate(identifier, position);\n\n    // Update its touch identifier\n    if (nipple.identifier !== identifier) {\n        self.manager.removeIdentifier(nipple.identifier);\n    }\n    nipple.identifier = identifier;\n\n    var process = function (nip) {\n        // Trigger the start.\n        nip.trigger('start', nip);\n        self.trigger('start ' + nip.id + ':start', nip);\n\n        nip.show();\n        if (pressure > 0) {\n            self.pressureFn(evt, nip, nip.identifier);\n        }\n        // Trigger the first move event.\n        self.processOnMove(evt);\n    };\n\n    // Transfer it from idles to actives.\n    if ((indexInIdles = self.idles.indexOf(nipple)) >= 0) {\n        self.idles.splice(indexInIdles, 1);\n    }\n\n    // Store the nipple in the actives array\n    self.actives.push(nipple);\n    self.ids.push(nipple.identifier);\n\n    if (opts.mode !== 'semi') {\n        process(nipple);\n    } else {\n        // In semi we check the distance of the touch\n        // to decide if we have to reset the nipple\n        var distance = _utils__WEBPACK_IMPORTED_MODULE_2__.distance(position, nipple.position);\n        if (distance <= opts.catchDistance) {\n            process(nipple);\n        } else {\n            nipple.destroy();\n            self.processOnStart(evt);\n            return;\n        }\n    }\n\n    return nipple;\n};\n\nCollection.prototype.getOrCreate = function (identifier, position) {\n    var self = this;\n    var opts = self.options;\n    var nipple;\n\n    // If we're in static or semi, we might already have an active.\n    if (/(semi|static)/.test(opts.mode)) {\n        // Get the active one.\n        // TODO: Multi-touche for semi and static will start here.\n        // Return the nearest one.\n        nipple = self.idles[0];\n        if (nipple) {\n            self.idles.splice(0, 1);\n            return nipple;\n        }\n\n        if (opts.mode === 'semi') {\n            // If we're in semi mode, we need to create one.\n            return self.createNipple(position, identifier);\n        }\n\n        // eslint-disable-next-line no-console\n        console.warn('Coudln\\'t find the needed nipple.');\n        return false;\n    }\n    // In dynamic, we create a new one.\n    nipple = self.createNipple(position, identifier);\n    return nipple;\n};\n\nCollection.prototype.processOnMove = function (evt) {\n    var self = this;\n    var opts = self.options;\n    var identifier = self.manager.getIdentifier(evt);\n    var nipple = self.nipples.get(identifier);\n    var scroll = self.manager.scroll;\n\n    // If we're moving without pressing\n    // it's that we went out the active zone\n    if (!_utils__WEBPACK_IMPORTED_MODULE_2__.isPressed(evt)) {\n        this.processOnEnd(evt);\n        return;\n    }\n\n    if (!nipple) {\n        // This is here just for safety.\n        // It shouldn't happen.\n        // eslint-disable-next-line no-console\n        console.error('Found zombie joystick with ID ' + identifier);\n        self.manager.removeIdentifier(identifier);\n        return;\n    }\n\n    if (opts.dynamicPage) {\n        var elBox = nipple.el.getBoundingClientRect();\n        nipple.position = {\n            x: scroll.x + elBox.left,\n            y: scroll.y + elBox.top\n        };\n    }\n\n    nipple.identifier = identifier;\n\n    var size = nipple.options.size / 2;\n    var pos = {\n        x: evt.pageX,\n        y: evt.pageY\n    };\n\n    if (opts.lockX){\n        pos.y = nipple.position.y;\n    }\n    if (opts.lockY) {\n        pos.x = nipple.position.x;\n    }\n\n    var dist = _utils__WEBPACK_IMPORTED_MODULE_2__.distance(pos, nipple.position);\n    var angle = _utils__WEBPACK_IMPORTED_MODULE_2__.angle(pos, nipple.position);\n    var rAngle = _utils__WEBPACK_IMPORTED_MODULE_2__.radians(angle);\n    var force = dist / size;\n\n    var raw = {\n        distance: dist,\n        position: pos\n    };\n\n    // Clamp the position\n    var clamped_dist;\n    var clamped_pos;\n    if (nipple.options.shape === 'circle') {\n        // Clamp to a circle\n        clamped_dist = Math.min(dist, size);\n        clamped_pos = _utils__WEBPACK_IMPORTED_MODULE_2__.findCoord(nipple.position, clamped_dist, angle);\n    } else {\n        // Clamp to a square\n        clamped_pos = _utils__WEBPACK_IMPORTED_MODULE_2__.clamp(pos, nipple.position, size);\n        clamped_dist = _utils__WEBPACK_IMPORTED_MODULE_2__.distance(clamped_pos, nipple.position);\n    }\n\n    if (opts.follow) {\n        // follow behaviour\n        if (dist > size) {\n            let delta_x = pos.x - clamped_pos.x;\n            let delta_y = pos.y - clamped_pos.y;\n            nipple.position.x += delta_x;\n            nipple.position.y += delta_y;\n            nipple.el.style.top = (nipple.position.y - (self.box.top + scroll.y)) + 'px';\n            nipple.el.style.left = (nipple.position.x - (self.box.left + scroll.x)) + 'px';\n\n            dist = _utils__WEBPACK_IMPORTED_MODULE_2__.distance(pos, nipple.position);\n        }\n    } else {\n        // clamp behaviour\n        pos = clamped_pos;\n        dist = clamped_dist;\n    }\n\n    var xPosition = pos.x - nipple.position.x;\n    var yPosition = pos.y - nipple.position.y;\n\n    nipple.frontPosition = {\n        x: xPosition,\n        y: yPosition\n    };\n\n    if (!opts.dataOnly) {\n        nipple.ui.front.style.transform = 'translate(' + xPosition + 'px,' + yPosition + 'px)';\n    }\n\n    // Prepare event's datas.\n    var toSend = {\n        identifier: nipple.identifier,\n        position: pos,\n        force: force,\n        pressure: evt.force || evt.pressure || evt.webkitForce || 0,\n        distance: dist,\n        angle: {\n            radian: rAngle,\n            degree: angle\n        },\n        vector: {\n            x: xPosition / size,\n            y: - yPosition / size\n        },\n        raw: raw,\n        instance: nipple,\n        lockX: opts.lockX,\n        lockY: opts.lockY\n    };\n\n    // Compute the direction's datas.\n    toSend = nipple.computeDirection(toSend);\n\n    // Offset angles to follow units circle.\n    toSend.angle = {\n        radian: _utils__WEBPACK_IMPORTED_MODULE_2__.radians(180 - angle),\n        degree: 180 - angle\n    };\n\n    // Send everything to everyone.\n    nipple.trigger('move', toSend);\n    self.trigger('move ' + nipple.id + ':move', toSend);\n};\n\nCollection.prototype.processOnEnd = function (evt) {\n    var self = this;\n    var opts = self.options;\n    var identifier = self.manager.getIdentifier(evt);\n    var nipple = self.nipples.get(identifier);\n    var removedIdentifier = self.manager.removeIdentifier(nipple.identifier);\n\n    if (!nipple) {\n        return;\n    }\n\n    if (!opts.dataOnly) {\n        nipple.hide(function () {\n            if (opts.mode === 'dynamic') {\n                nipple.trigger('removed', nipple);\n                self.trigger('removed ' + nipple.id + ':removed', nipple);\n                self.manager\n                    .trigger('removed ' + nipple.id + ':removed', nipple);\n                nipple.destroy();\n            }\n        });\n    }\n\n    // Clear the pressure interval reader\n    clearInterval(self.pressureIntervals[nipple.identifier]);\n\n    // Reset the direciton of the nipple, to be able to trigger a new direction\n    // on start.\n    nipple.resetDirection();\n\n    nipple.trigger('end', nipple);\n    self.trigger('end ' + nipple.id + ':end', nipple);\n\n    // Remove identifier from our bank.\n    if (self.ids.indexOf(nipple.identifier) >= 0) {\n        self.ids.splice(self.ids.indexOf(nipple.identifier), 1);\n    }\n\n    // Clean our actives array.\n    if (self.actives.indexOf(nipple) >= 0) {\n        self.actives.splice(self.actives.indexOf(nipple), 1);\n    }\n\n    if (/(semi|static)/.test(opts.mode)) {\n        // Transfer nipple from actives to idles\n        // if we're in semi or static mode.\n        self.idles.push(nipple);\n    } else if (self.nipples.indexOf(nipple) >= 0) {\n        // Only if we're not in semi or static mode\n        // we can remove the instance.\n        self.nipples.splice(self.nipples.indexOf(nipple), 1);\n    }\n\n    // We unbind move and end.\n    self.manager.unbindDocument();\n\n    // We add back the identifier of the idle nipple;\n    if (/(semi|static)/.test(opts.mode)) {\n        self.manager.ids[removedIdentifier.id] = removedIdentifier.identifier;\n    }\n};\n\n// Remove destroyed nipple from the lists\nCollection.prototype.onDestroyed = function(evt, nipple) {\n    var self = this;\n    if (self.nipples.indexOf(nipple) >= 0) {\n        self.nipples.splice(self.nipples.indexOf(nipple), 1);\n    }\n    if (self.actives.indexOf(nipple) >= 0) {\n        self.actives.splice(self.actives.indexOf(nipple), 1);\n    }\n    if (self.idles.indexOf(nipple) >= 0) {\n        self.idles.splice(self.idles.indexOf(nipple), 1);\n    }\n    if (self.ids.indexOf(nipple.identifier) >= 0) {\n        self.ids.splice(self.ids.indexOf(nipple.identifier), 1);\n    }\n\n    // Remove the identifier from our bank\n    self.manager.removeIdentifier(nipple.identifier);\n\n    // We unbind move and end.\n    self.manager.unbindDocument();\n};\n\n// Cleanly destroy the manager\nCollection.prototype.destroy = function () {\n    var self = this;\n    self.unbindEvt(self.options.zone, 'start');\n\n    // Destroy nipples.\n    self.nipples.forEach(function(nipple) {\n        nipple.destroy();\n    });\n\n    // Clean 3DTouch intervals.\n    for (var i in self.pressureIntervals) {\n        if (self.pressureIntervals.hasOwnProperty(i)) {\n            clearInterval(self.pressureIntervals[i]);\n        }\n    }\n\n    // Notify the manager passing the instance\n    self.trigger('destroyed', self.nipples);\n    // We unbind move and end.\n    self.manager.unbindDocument();\n    // Unbind everything.\n    self.off();\n};\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Collection);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/nipplejs/src/collection.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/nipplejs/src/index.js":
/*!********************************************!*\
  !*** ./node_modules/nipplejs/src/index.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _manager__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./manager */ \"(ssr)/./node_modules/nipplejs/src/manager.js\");\n\n\nconst factory = new _manager__WEBPACK_IMPORTED_MODULE_0__[\"default\"]();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n    create: function (options) {\n        return factory.create(options);\n    },\n    factory: factory\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmlwcGxlanMvc3JjL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWdDOztBQUVoQyxvQkFBb0IsZ0RBQU87QUFDM0IsaUVBQWU7QUFDZjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0EsQ0FBQyxFQUFDIiwic291cmNlcyI6WyIvVXNlcnMveXVzdWYvRGVza3RvcC9wb3J0Zm9saW8vbm9kZV9tb2R1bGVzL25pcHBsZWpzL3NyYy9pbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgTWFuYWdlciBmcm9tICcuL21hbmFnZXInO1xuXG5jb25zdCBmYWN0b3J5ID0gbmV3IE1hbmFnZXIoKTtcbmV4cG9ydCBkZWZhdWx0IHtcbiAgICBjcmVhdGU6IGZ1bmN0aW9uIChvcHRpb25zKSB7XG4gICAgICAgIHJldHVybiBmYWN0b3J5LmNyZWF0ZShvcHRpb25zKTtcbiAgICB9LFxuICAgIGZhY3Rvcnk6IGZhY3Rvcnlcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/nipplejs/src/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/nipplejs/src/manager.js":
/*!**********************************************!*\
  !*** ./node_modules/nipplejs/src/manager.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _collection__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./collection */ \"(ssr)/./node_modules/nipplejs/src/collection.js\");\n/* harmony import */ var _super__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./super */ \"(ssr)/./node_modules/nipplejs/src/super.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/nipplejs/src/utils.js\");\n\n\n\n\n///////////////////////\n///     MANAGER     ///\n///////////////////////\n\nfunction Manager (options) {\n    var self = this;\n    self.ids = {};\n    self.index = 0;\n    self.collections = [];\n    self.scroll = _utils__WEBPACK_IMPORTED_MODULE_2__.getScroll();\n\n    self.config(options);\n    self.prepareCollections();\n\n    // Listen for resize, to reposition every joysticks\n    var resizeHandler = function () {\n        var pos;\n        self.collections.forEach(function (collection) {\n            collection.forEach(function (nipple) {\n                pos = nipple.el.getBoundingClientRect();\n                nipple.position = {\n                    x: self.scroll.x + pos.left,\n                    y: self.scroll.y + pos.top\n                };\n            });\n        });\n    };\n    _utils__WEBPACK_IMPORTED_MODULE_2__.bindEvt(window, 'resize', function () {\n        _utils__WEBPACK_IMPORTED_MODULE_2__.throttle(resizeHandler);\n    });\n\n    // Listen for scrolls, so we have a global scroll value\n    // without having to request it all the time.\n    var scrollHandler = function () {\n        self.scroll = _utils__WEBPACK_IMPORTED_MODULE_2__.getScroll();\n    };\n    _utils__WEBPACK_IMPORTED_MODULE_2__.bindEvt(window, 'scroll', function () {\n        _utils__WEBPACK_IMPORTED_MODULE_2__.throttle(scrollHandler);\n    });\n\n    return self.collections;\n}\n\nManager.prototype = new _super__WEBPACK_IMPORTED_MODULE_1__[\"default\"]();\nManager.constructor = Manager;\n\nManager.prototype.prepareCollections = function () {\n    var self = this;\n    // Public API Preparation.\n    self.collections.create = self.create.bind(self);\n    // Listen to anything\n    self.collections.on = self.on.bind(self);\n    // Unbind general events\n    self.collections.off = self.off.bind(self);\n    // Destroy everything\n    self.collections.destroy = self.destroy.bind(self);\n    // Get any nipple\n    self.collections.get = function (id) {\n        var nipple;\n        // Use .every() to break the loop as soon as found.\n        self.collections.every(function (collection) {\n            nipple = collection.get(id);\n            return nipple ? false : true;\n        });\n        return nipple;\n    };\n};\n\nManager.prototype.create = function (options) {\n    return this.createCollection(options);\n};\n\n// Collection Factory\nManager.prototype.createCollection = function (options) {\n    var self = this;\n    var collection = new _collection__WEBPACK_IMPORTED_MODULE_0__[\"default\"](self, options);\n\n    self.bindCollection(collection);\n    self.collections.push(collection);\n\n    return collection;\n};\n\nManager.prototype.bindCollection = function (collection) {\n    var self = this;\n    var type;\n    // Bubble up identified events.\n    var handler = function (evt, data) {\n        // Identify the event type with the nipple's identifier.\n        type = evt.type + ' ' + data.id + ':' + evt.type;\n        self.trigger(type, data);\n    };\n\n    // When it gets destroyed we clean.\n    collection.on('destroyed', self.onDestroyed.bind(self));\n\n    // Other events that will get bubbled up.\n    collection.on('shown hidden rested dir plain', handler);\n    collection.on('dir:up dir:right dir:down dir:left', handler);\n    collection.on('plain:up plain:right plain:down plain:left', handler);\n};\n\nManager.prototype.bindDocument = function () {\n    var self = this;\n    // Bind only if not already binded\n    if (!self.binded) {\n        self.bindEvt(document, 'move')\n            .bindEvt(document, 'end');\n        self.binded = true;\n    }\n};\n\nManager.prototype.unbindDocument = function (force) {\n    var self = this;\n    // If there are no touch left\n    // unbind the document.\n    if (!Object.keys(self.ids).length || force === true) {\n        self.unbindEvt(document, 'move')\n            .unbindEvt(document, 'end');\n        self.binded = false;\n    }\n};\n\nManager.prototype.getIdentifier = function (evt) {\n    var id;\n    // If no event, simple increment\n    if (!evt) {\n        id = this.index;\n    } else {\n        // Extract identifier from event object.\n        // Unavailable in mouse events so replaced by latest increment.\n        id = evt.identifier === undefined ? evt.pointerId : evt.identifier;\n        if (id === undefined) {\n            id = this.latest || 0;\n        }\n    }\n\n    if (this.ids[id] === undefined) {\n        this.ids[id] = this.index;\n        this.index += 1;\n    }\n\n    // Keep the latest id used in case we're using an unidentified mouseEvent\n    this.latest = id;\n    return this.ids[id];\n};\n\nManager.prototype.removeIdentifier = function (identifier) {\n    var removed = {};\n    for (var id in this.ids) {\n        if (this.ids[id] === identifier) {\n            removed.id = id;\n            removed.identifier = this.ids[id];\n            delete this.ids[id];\n            break;\n        }\n    }\n    return removed;\n};\n\nManager.prototype.onmove = function (evt) {\n    var self = this;\n    self.onAny('move', evt);\n    return false;\n};\n\nManager.prototype.onend = function (evt) {\n    var self = this;\n    self.onAny('end', evt);\n    return false;\n};\n\nManager.prototype.oncancel = function (evt) {\n    var self = this;\n    self.onAny('end', evt);\n    return false;\n};\n\nManager.prototype.onAny = function (which, evt) {\n    var self = this;\n    var id;\n    var processFn = 'processOn' + which.charAt(0).toUpperCase() +\n        which.slice(1);\n    evt = _utils__WEBPACK_IMPORTED_MODULE_2__.prepareEvent(evt);\n    var processColl = function (e, id, coll) {\n        if (coll.ids.indexOf(id) >= 0) {\n            coll[processFn](e);\n            // Mark the event to avoid cleaning it later.\n            e._found_ = true;\n        }\n    };\n    var processEvt = function (e) {\n        id = self.getIdentifier(e);\n        _utils__WEBPACK_IMPORTED_MODULE_2__.map(self.collections, processColl.bind(null, e, id));\n        // If the event isn't handled by any collection,\n        // we need to clean its identifier.\n        if (!e._found_) {\n            self.removeIdentifier(id);\n        }\n    };\n\n    _utils__WEBPACK_IMPORTED_MODULE_2__.map(evt, processEvt);\n\n    return false;\n};\n\n// Cleanly destroy the manager\nManager.prototype.destroy = function () {\n    var self = this;\n    self.unbindDocument(true);\n    self.ids = {};\n    self.index = 0;\n    self.collections.forEach(function(collection) {\n        collection.destroy();\n    });\n    self.off();\n};\n\n// When a collection gets destroyed\n// we clean behind.\nManager.prototype.onDestroyed = function (evt, coll) {\n    var self = this;\n    if (self.collections.indexOf(coll) < 0) {\n        return false;\n    }\n    self.collections.splice(self.collections.indexOf(coll), 1);\n};\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Manager);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/nipplejs/src/manager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/nipplejs/src/nipple.js":
/*!*********************************************!*\
  !*** ./node_modules/nipplejs/src/nipple.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _super__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./super */ \"(ssr)/./node_modules/nipplejs/src/super.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/nipplejs/src/utils.js\");\n\n\n\n///////////////////////\n///   THE NIPPLE    ///\n///////////////////////\n\nfunction Nipple (collection, options) {\n    this.identifier = options.identifier;\n    this.position = options.position;\n    this.frontPosition = options.frontPosition;\n    this.collection = collection;\n\n    // Defaults\n    this.defaults = {\n        size: 100,\n        threshold: 0.1,\n        color: 'white',\n        fadeTime: 250,\n        dataOnly: false,\n        restJoystick: true,\n        restOpacity: 0.5,\n        mode: 'dynamic',\n        zone: document.body,\n        lockX: false,\n        lockY: false,\n        shape: 'circle'\n    };\n\n    this.config(options);\n\n    // Overwrites\n    if (this.options.mode === 'dynamic') {\n        this.options.restOpacity = 0;\n    }\n\n    this.id = Nipple.id;\n    Nipple.id += 1;\n    this.buildEl()\n        .stylize();\n\n    // Nipple's API.\n    this.instance = {\n        el: this.ui.el,\n        on: this.on.bind(this),\n        off: this.off.bind(this),\n        show: this.show.bind(this),\n        hide: this.hide.bind(this),\n        add: this.addToDom.bind(this),\n        remove: this.removeFromDom.bind(this),\n        destroy: this.destroy.bind(this),\n        setPosition:this.setPosition.bind(this),\n        resetDirection: this.resetDirection.bind(this),\n        computeDirection: this.computeDirection.bind(this),\n        trigger: this.trigger.bind(this),\n        position: this.position,\n        frontPosition: this.frontPosition,\n        ui: this.ui,\n        identifier: this.identifier,\n        id: this.id,\n        options: this.options\n    };\n\n    return this.instance;\n}\n\nNipple.prototype = new _super__WEBPACK_IMPORTED_MODULE_0__[\"default\"]();\nNipple.constructor = Nipple;\nNipple.id = 0;\n\n// Build the dom element of the Nipple instance.\nNipple.prototype.buildEl = function (options) {\n    this.ui = {};\n\n    if (this.options.dataOnly) {\n        return this;\n    }\n\n    this.ui.el = document.createElement('div');\n    this.ui.back = document.createElement('div');\n    this.ui.front = document.createElement('div');\n\n    this.ui.el.className = 'nipple collection_' + this.collection.id;\n    this.ui.back.className = 'back';\n    this.ui.front.className = 'front';\n\n    this.ui.el.setAttribute('id', 'nipple_' + this.collection.id +\n        '_' + this.id);\n\n    this.ui.el.appendChild(this.ui.back);\n    this.ui.el.appendChild(this.ui.front);\n\n    return this;\n};\n\n// Apply CSS to the Nipple instance.\nNipple.prototype.stylize = function () {\n    if (this.options.dataOnly) {\n        return this;\n    }\n    var animTime = this.options.fadeTime + 'ms';\n    var borderStyle = _utils__WEBPACK_IMPORTED_MODULE_1__.getVendorStyle('borderRadius', '50%');\n    var transitStyle = _utils__WEBPACK_IMPORTED_MODULE_1__.getTransitionStyle('transition', 'opacity', animTime);\n    var styles = {};\n    styles.el = {\n        position: 'absolute',\n        opacity: this.options.restOpacity,\n        display: 'block',\n        'zIndex': 999\n    };\n\n    styles.back = {\n        position: 'absolute',\n        display: 'block',\n        width: this.options.size + 'px',\n        height: this.options.size + 'px',\n        left: 0,\n        marginLeft: -this.options.size / 2 + 'px',\n        marginTop: -this.options.size / 2 + 'px',\n        background: this.options.color,\n        'opacity': '.5'\n    };\n\n    styles.front = {\n        width: this.options.size / 2 + 'px',\n        height: this.options.size / 2 + 'px',\n        position: 'absolute',\n        display: 'block',\n        left: 0,\n        marginLeft: -this.options.size / 4 + 'px',\n        marginTop: -this.options.size / 4 + 'px',\n        background: this.options.color,\n        'opacity': '.5',\n        transform: 'translate(0px, 0px)'\n    };\n\n    _utils__WEBPACK_IMPORTED_MODULE_1__.extend(styles.el, transitStyle);\n    if(this.options.shape === 'circle'){\n        _utils__WEBPACK_IMPORTED_MODULE_1__.extend(styles.back, borderStyle);\n    }\n    _utils__WEBPACK_IMPORTED_MODULE_1__.extend(styles.front, borderStyle);\n\n    this.applyStyles(styles);\n\n    return this;\n};\n\nNipple.prototype.applyStyles = function (styles) {\n    // Apply styles\n    for (var i in this.ui) {\n        if (this.ui.hasOwnProperty(i)) {\n            for (var j in styles[i]) {\n                this.ui[i].style[j] = styles[i][j];\n            }\n        }\n    }\n\n    return this;\n};\n\n// Inject the Nipple instance into DOM.\nNipple.prototype.addToDom = function () {\n    // We're not adding it if we're dataOnly or already in dom.\n    if (this.options.dataOnly || document.body.contains(this.ui.el)) {\n        return this;\n    }\n    this.options.zone.appendChild(this.ui.el);\n    return this;\n};\n\n// Remove the Nipple instance from DOM.\nNipple.prototype.removeFromDom = function () {\n    if (this.options.dataOnly || !document.body.contains(this.ui.el)) {\n        return this;\n    }\n    this.options.zone.removeChild(this.ui.el);\n    return this;\n};\n\n// Entirely destroy this nipple\nNipple.prototype.destroy = function () {\n    clearTimeout(this.removeTimeout);\n    clearTimeout(this.showTimeout);\n    clearTimeout(this.restTimeout);\n    this.trigger('destroyed', this.instance);\n    this.removeFromDom();\n    this.off();\n};\n\n// Fade in the Nipple instance.\nNipple.prototype.show = function (cb) {\n    var self = this;\n\n    if (self.options.dataOnly) {\n        return self;\n    }\n\n    clearTimeout(self.removeTimeout);\n    clearTimeout(self.showTimeout);\n    clearTimeout(self.restTimeout);\n\n    self.addToDom();\n\n    self.restCallback();\n\n    setTimeout(function () {\n        self.ui.el.style.opacity = 1;\n    }, 0);\n\n    self.showTimeout = setTimeout(function () {\n        self.trigger('shown', self.instance);\n        if (typeof cb === 'function') {\n            cb.call(this);\n        }\n    }, self.options.fadeTime);\n\n    return self;\n};\n\n// Fade out the Nipple instance.\nNipple.prototype.hide = function (cb) {\n    var self = this;\n\n    if (self.options.dataOnly) {\n        return self;\n    }\n\n    self.ui.el.style.opacity = self.options.restOpacity;\n\n    clearTimeout(self.removeTimeout);\n    clearTimeout(self.showTimeout);\n    clearTimeout(self.restTimeout);\n\n    self.removeTimeout = setTimeout(\n        function () {\n            var display = self.options.mode === 'dynamic' ? 'none' : 'block';\n            self.ui.el.style.display = display;\n            if (typeof cb === 'function') {\n                cb.call(self);\n            }\n\n            self.trigger('hidden', self.instance);\n        },\n        self.options.fadeTime\n    );\n\n    if (self.options.restJoystick) {\n        const rest = self.options.restJoystick;\n        const newPosition = {};\n\n        newPosition.x = rest === true || rest.x !== false ? 0 : self.instance.frontPosition.x;\n        newPosition.y = rest === true || rest.y !== false ? 0 : self.instance.frontPosition.y;\n\n        self.setPosition(cb, newPosition);\n    }\n\n    return self;\n};\n\n// Set the nipple to the specified position\nNipple.prototype.setPosition = function (cb, position) {\n    var self = this;\n    self.frontPosition = {\n        x: position.x,\n        y: position.y\n    };\n    var animTime = self.options.fadeTime + 'ms';\n\n    var transitStyle = {};\n    transitStyle.front = _utils__WEBPACK_IMPORTED_MODULE_1__.getTransitionStyle('transition',\n        ['transform'], animTime);\n\n    var styles = {front: {}};\n    styles.front = {\n        transform: 'translate(' + self.frontPosition.x + 'px,' + self.frontPosition.y + 'px)'\n    };\n\n    self.applyStyles(transitStyle);\n    self.applyStyles(styles);\n\n    self.restTimeout = setTimeout(\n        function () {\n            if (typeof cb === 'function') {\n                cb.call(self);\n            }\n            self.restCallback();\n        },\n        self.options.fadeTime\n    );\n};\n\nNipple.prototype.restCallback = function () {\n    var self = this;\n    var transitStyle = {};\n    transitStyle.front = _utils__WEBPACK_IMPORTED_MODULE_1__.getTransitionStyle('transition', 'none', '');\n    self.applyStyles(transitStyle);\n    self.trigger('rested', self.instance);\n};\n\nNipple.prototype.resetDirection = function () {\n    // Fully rebuild the object to let the iteration possible.\n    this.direction = {\n        x: false,\n        y: false,\n        angle: false\n    };\n};\n\nNipple.prototype.computeDirection = function (obj) {\n    var rAngle = obj.angle.radian;\n    var angle45 = Math.PI / 4;\n    var angle90 = Math.PI / 2;\n    var direction, directionX, directionY;\n\n    // Angular direction\n    //     \\  UP /\n    //      \\   /\n    // LEFT       RIGHT\n    //      /   \\\n    //     /DOWN \\\n    //\n    if (\n        rAngle > angle45 &&\n        rAngle < (angle45 * 3) &&\n        !obj.lockX\n    ) {\n        direction = 'up';\n    } else if (\n        rAngle > -angle45 &&\n        rAngle <= angle45 &&\n        !obj.lockY\n    ) {\n        direction = 'left';\n    } else if (\n        rAngle > (-angle45 * 3) &&\n        rAngle <= -angle45 &&\n        !obj.lockX\n    ) {\n        direction = 'down';\n    } else if (!obj.lockY) {\n        direction = 'right';\n    }\n\n    // Plain direction\n    //    UP                 |\n    // _______               | RIGHT\n    //                  LEFT |\n    //   DOWN                |\n    if (!obj.lockY) {\n        if (rAngle > -angle90 && rAngle < angle90) {\n            directionX = 'left';\n        } else {\n            directionX = 'right';\n        }\n    }\n\n    if (!obj.lockX) {\n        if (rAngle > 0) {\n            directionY = 'up';\n        } else {\n            directionY = 'down';\n        }\n    }\n\n    if (obj.force > this.options.threshold) {\n        var oldDirection = {};\n        var i;\n        for (i in this.direction) {\n            if (this.direction.hasOwnProperty(i)) {\n                oldDirection[i] = this.direction[i];\n            }\n        }\n\n        var same = {};\n\n        this.direction = {\n            x: directionX,\n            y: directionY,\n            angle: direction\n        };\n\n        obj.direction = this.direction;\n\n        for (i in oldDirection) {\n            if (oldDirection[i] === this.direction[i]) {\n                same[i] = true;\n            }\n        }\n\n        // If all 3 directions are the same, we don't trigger anything.\n        if (same.x && same.y && same.angle) {\n            return obj;\n        }\n\n        if (!same.x || !same.y) {\n            this.trigger('plain', obj);\n        }\n\n        if (!same.x) {\n            this.trigger('plain:' + directionX, obj);\n        }\n\n        if (!same.y) {\n            this.trigger('plain:' + directionY, obj);\n        }\n\n        if (!same.angle) {\n            this.trigger('dir dir:' + direction, obj);\n        }\n    } else {\n        this.resetDirection();\n    }\n\n    return obj;\n};\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Nipple);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/nipplejs/src/nipple.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/nipplejs/src/super.js":
/*!********************************************!*\
  !*** ./node_modules/nipplejs/src/super.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/nipplejs/src/utils.js\");\n///////////////////////\n///   SUPER CLASS   ///\n///////////////////////\n\n\n// Constants\nvar isTouch = !!('ontouchstart' in window);\nvar isPointer = window.PointerEvent ? true : false;\nvar isMSPointer = window.MSPointerEvent ? true : false;\nvar events = {\n    touch: {\n        start: 'touchstart',\n        move: 'touchmove',\n        end: 'touchend, touchcancel'\n    },\n    mouse: {\n        start: 'mousedown',\n        move: 'mousemove',\n        end: 'mouseup'\n    },\n    pointer: {\n        start: 'pointerdown',\n        move: 'pointermove',\n        end: 'pointerup, pointercancel'\n    },\n    MSPointer: {\n        start: 'MSPointerDown',\n        move: 'MSPointerMove',\n        end: 'MSPointerUp'\n    }\n};\nvar toBind;\nvar secondBind = {};\nif (isPointer) {\n    toBind = events.pointer;\n} else if (isMSPointer) {\n    toBind = events.MSPointer;\n} else if (isTouch) {\n    toBind = events.touch;\n    secondBind = events.mouse;\n} else {\n    toBind = events.mouse;\n}\n\nfunction Super () {}\n\n// Basic event system.\nSuper.prototype.on = function (arg, cb) {\n    var self = this;\n    var types = arg.split(/[ ,]+/g);\n    var type;\n    self._handlers_ = self._handlers_ || {};\n\n    for (var i = 0; i < types.length; i += 1) {\n        type = types[i];\n        self._handlers_[type] = self._handlers_[type] || [];\n        self._handlers_[type].push(cb);\n    }\n    return self;\n};\n\nSuper.prototype.off = function (type, cb) {\n    var self = this;\n    self._handlers_ = self._handlers_ || {};\n\n    if (type === undefined) {\n        self._handlers_ = {};\n    } else if (cb === undefined) {\n        self._handlers_[type] = null;\n    } else if (self._handlers_[type] &&\n            self._handlers_[type].indexOf(cb) >= 0) {\n        self._handlers_[type].splice(self._handlers_[type].indexOf(cb), 1);\n    }\n\n    return self;\n};\n\nSuper.prototype.trigger = function (arg, data) {\n    var self = this;\n    var types = arg.split(/[ ,]+/g);\n    var type;\n    self._handlers_ = self._handlers_ || {};\n\n    for (var i = 0; i < types.length; i += 1) {\n        type = types[i];\n        if (self._handlers_[type] && self._handlers_[type].length) {\n            self._handlers_[type].forEach(function (handler) {\n                handler.call(self, {\n                    type: type,\n                    target: self\n                }, data);\n            });\n        }\n    }\n};\n\n// Configuration\nSuper.prototype.config = function (options) {\n    var self = this;\n    self.options = self.defaults || {};\n    if (options) {\n        self.options = _utils__WEBPACK_IMPORTED_MODULE_0__.safeExtend(self.options, options);\n    }\n};\n\n// Bind internal events.\nSuper.prototype.bindEvt = function (el, type) {\n    var self = this;\n    self._domHandlers_ = self._domHandlers_ || {};\n\n    self._domHandlers_[type] = function () {\n        if (typeof self['on' + type] === 'function') {\n            self['on' + type].apply(self, arguments);\n        } else {\n            // eslint-disable-next-line no-console\n            console.warn('[WARNING] : Missing \"on' + type + '\" handler.');\n        }\n    };\n\n    _utils__WEBPACK_IMPORTED_MODULE_0__.bindEvt(el, toBind[type], self._domHandlers_[type]);\n\n    if (secondBind[type]) {\n        // Support for both touch and mouse at the same time.\n        _utils__WEBPACK_IMPORTED_MODULE_0__.bindEvt(el, secondBind[type], self._domHandlers_[type]);\n    }\n\n    return self;\n};\n\n// Unbind dom events.\nSuper.prototype.unbindEvt = function (el, type) {\n    var self = this;\n    self._domHandlers_ = self._domHandlers_ || {};\n\n    _utils__WEBPACK_IMPORTED_MODULE_0__.unbindEvt(el, toBind[type], self._domHandlers_[type]);\n\n    if (secondBind[type]) {\n        // Support for both touch and mouse at the same time.\n        _utils__WEBPACK_IMPORTED_MODULE_0__.unbindEvt(el, secondBind[type], self._domHandlers_[type]);\n    }\n\n    delete self._domHandlers_[type];\n\n    return this;\n};\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Super);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/nipplejs/src/super.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/nipplejs/src/utils.js":
/*!********************************************!*\
  !*** ./node_modules/nipplejs/src/utils.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   angle: () => (/* binding */ angle),\n/* harmony export */   applyPosition: () => (/* binding */ applyPosition),\n/* harmony export */   bindEvt: () => (/* binding */ bindEvt),\n/* harmony export */   clamp: () => (/* binding */ clamp),\n/* harmony export */   configStylePropertyObject: () => (/* binding */ configStylePropertyObject),\n/* harmony export */   degrees: () => (/* binding */ degrees),\n/* harmony export */   distance: () => (/* binding */ distance),\n/* harmony export */   extend: () => (/* binding */ extend),\n/* harmony export */   findCoord: () => (/* binding */ findCoord),\n/* harmony export */   getScroll: () => (/* binding */ getScroll),\n/* harmony export */   getTransitionStyle: () => (/* binding */ getTransitionStyle),\n/* harmony export */   getVendorStyle: () => (/* binding */ getVendorStyle),\n/* harmony export */   isPressed: () => (/* binding */ isPressed),\n/* harmony export */   map: () => (/* binding */ map),\n/* harmony export */   prepareEvent: () => (/* binding */ prepareEvent),\n/* harmony export */   radians: () => (/* binding */ radians),\n/* harmony export */   safeExtend: () => (/* binding */ safeExtend),\n/* harmony export */   throttle: () => (/* binding */ throttle),\n/* harmony export */   trigger: () => (/* binding */ trigger),\n/* harmony export */   unbindEvt: () => (/* binding */ unbindEvt)\n/* harmony export */ });\n///////////////////////\n///      UTILS      ///\n///////////////////////\n\nconst distance = (p1, p2) => {\n    const dx = p2.x - p1.x;\n    const dy = p2.y - p1.y;\n\n    return Math.sqrt((dx * dx) + (dy * dy));\n};\n\nconst angle = (p1, p2) => {\n    const dx = p2.x - p1.x;\n    const dy = p2.y - p1.y;\n\n    return degrees(Math.atan2(dy, dx));\n};\n\nconst findCoord = (p, d, a) => {\n    const b = {x: 0, y: 0};\n    a = radians(a);\n    b.x = p.x - d * Math.cos(a);\n    b.y = p.y - d * Math.sin(a);\n    return b;\n};\n\nconst radians = (a) => {\n    return a * (Math.PI / 180);\n};\n\nconst degrees = (a) => {\n    return a * (180 / Math.PI);\n};\n\nconst isPressed = (evt) => {\n    if (isNaN(evt.buttons)) {\n        return evt.pressure !== 0;\n    }\n    return evt.buttons !== 0;\n};\n\nconst timers = new Map();\nconst throttle = (cb) => {\n    if (timers.has(cb)) {\n        clearTimeout(timers.get(cb));\n    }\n    timers.set(cb, setTimeout(cb, 100));\n};\n\nconst bindEvt = (el, arg, handler) => {\n    const types = arg.split(/[ ,]+/g);\n    let type;\n    for (let i = 0; i < types.length; i += 1) {\n        type = types[i];\n        if (el.addEventListener) {\n            el.addEventListener(type, handler, false);\n        } else if (el.attachEvent) {\n            el.attachEvent(type, handler);\n        }\n    }\n};\n\nconst unbindEvt = (el, arg, handler) => {\n    const types = arg.split(/[ ,]+/g);\n    let type;\n    for (let i = 0; i < types.length; i += 1) {\n        type = types[i];\n        if (el.removeEventListener) {\n            el.removeEventListener(type, handler);\n        } else if (el.detachEvent) {\n            el.detachEvent(type, handler);\n        }\n    }\n};\n\nconst trigger = (el, type, data) => {\n    const evt = new CustomEvent(type, data);\n    el.dispatchEvent(evt);\n};\n\nconst prepareEvent = (evt) => {\n    evt.preventDefault();\n    return evt.type.match(/^touch/) ? evt.changedTouches : evt;\n};\n\nconst getScroll = () => {\n    const x = (window.pageXOffset !== undefined) ?\n        window.pageXOffset :\n        (document.documentElement || document.body.parentNode || document.body)\n            .scrollLeft;\n\n    const y = (window.pageYOffset !== undefined) ?\n        window.pageYOffset :\n        (document.documentElement || document.body.parentNode || document.body)\n            .scrollTop;\n    return {\n        x: x,\n        y: y\n    };\n};\n\nconst applyPosition = (el, pos) => {\n    if (pos.top || pos.right || pos.bottom || pos.left) {\n        el.style.top = pos.top;\n        el.style.right = pos.right;\n        el.style.bottom = pos.bottom;\n        el.style.left = pos.left;\n    } else {\n        el.style.left = pos.x + 'px';\n        el.style.top = pos.y + 'px';\n    }\n};\n\nconst getTransitionStyle = (property, values, time) => {\n    const obj = configStylePropertyObject(property);\n    for (let i in obj) {\n        if (obj.hasOwnProperty(i)) {\n            if (typeof values === 'string') {\n                obj[i] = values + ' ' + time;\n            } else {\n                let st = '';\n                for (let j = 0, max = values.length; j < max; j += 1) {\n                    st += values[j] + ' ' + time + ', ';\n                }\n                obj[i] = st.slice(0, -2);\n            }\n        }\n    }\n    return obj;\n};\n\nconst getVendorStyle = (property, value) => {\n    const obj = configStylePropertyObject(property);\n    for (let i in obj) {\n        if (obj.hasOwnProperty(i)) {\n            obj[i] = value;\n        }\n    }\n    return obj;\n};\n\nconst configStylePropertyObject = (prop) => {\n    const obj = {};\n    obj[prop] = '';\n    const vendors = ['webkit', 'Moz', 'o'];\n    vendors.forEach(function (vendor) {\n        obj[vendor + prop.charAt(0).toUpperCase() + prop.slice(1)] = '';\n    });\n    return obj;\n};\n\nconst extend = (objA, objB) => {\n    for (let i in objB) {\n        if (objB.hasOwnProperty(i)) {\n            objA[i] = objB[i];\n        }\n    }\n    return objA;\n};\n\n// Overwrite only what's already present\nconst safeExtend = (objA, objB) => {\n    const obj = {};\n    for (let i in objA) {\n        if (objA.hasOwnProperty(i) && objB.hasOwnProperty(i)) {\n            obj[i] = objB[i];\n        } else if (objA.hasOwnProperty(i)) {\n            obj[i] = objA[i];\n        }\n    }\n    return obj;\n};\n\n// Map for array or unique item.\nconst map = (ar, fn) => {\n    if (ar.length) {\n        for (let i = 0, max = ar.length; i < max; i += 1) {\n            fn(ar[i]);\n        }\n    } else {\n        fn(ar);\n    }\n};\n\n// Clamp position within the range\nconst clamp = (pos, nipplePos, size) => ({\n    //                          left-clamping        right-clamping\n    x: Math.min(Math.max(pos.x, nipplePos.x - size), nipplePos.x + size),\n    //                          top-clamping         bottom-clamping\n    y: Math.min(Math.max(pos.y, nipplePos.y - size), nipplePos.y + size)\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/nipplejs/src/utils.js\n");

/***/ })

};
;