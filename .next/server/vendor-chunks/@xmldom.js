/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@xmldom";
exports.ids = ["vendor-chunks/@xmldom"];
exports.modules = {

/***/ "(ssr)/./node_modules/@xmldom/xmldom/lib/conventions.js":
/*!********************************************************!*\
  !*** ./node_modules/@xmldom/xmldom/lib/conventions.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n\n/**\n * Ponyfill for `Array.prototype.find` which is only available in ES6 runtimes.\n *\n * Works with anything that has a `length` property and index access properties, including NodeList.\n *\n * @template {unknown} T\n * @param {Array<T> | ({length:number, [number]: T})} list\n * @param {function (item: T, index: number, list:Array<T> | ({length:number, [number]: T})):boolean} predicate\n * @param {Partial<Pick<ArrayConstructor['prototype'], 'find'>>?} ac `Array.prototype` by default,\n * \t\t\t\tallows injecting a custom implementation in tests\n * @returns {T | undefined}\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/find\n * @see https://tc39.es/ecma262/multipage/indexed-collections.html#sec-array.prototype.find\n */\nfunction find(list, predicate, ac) {\n\tif (ac === undefined) {\n\t\tac = Array.prototype;\n\t}\n\tif (list && typeof ac.find === 'function') {\n\t\treturn ac.find.call(list, predicate);\n\t}\n\tfor (var i = 0; i < list.length; i++) {\n\t\tif (Object.prototype.hasOwnProperty.call(list, i)) {\n\t\t\tvar item = list[i];\n\t\t\tif (predicate.call(undefined, item, i, list)) {\n\t\t\t\treturn item;\n\t\t\t}\n\t\t}\n\t}\n}\n\n/**\n * \"Shallow freezes\" an object to render it immutable.\n * Uses `Object.freeze` if available,\n * otherwise the immutability is only in the type.\n *\n * Is used to create \"enum like\" objects.\n *\n * @template T\n * @param {T} object the object to freeze\n * @param {Pick<ObjectConstructor, 'freeze'> = Object} oc `Object` by default,\n * \t\t\t\tallows to inject custom object constructor for tests\n * @returns {Readonly<T>}\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/freeze\n */\nfunction freeze(object, oc) {\n\tif (oc === undefined) {\n\t\toc = Object\n\t}\n\treturn oc && typeof oc.freeze === 'function' ? oc.freeze(object) : object\n}\n\n/**\n * Since we can not rely on `Object.assign` we provide a simplified version\n * that is sufficient for our needs.\n *\n * @param {Object} target\n * @param {Object | null | undefined} source\n *\n * @returns {Object} target\n * @throws TypeError if target is not an object\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/assign\n * @see https://tc39.es/ecma262/multipage/fundamental-objects.html#sec-object.assign\n */\nfunction assign(target, source) {\n\tif (target === null || typeof target !== 'object') {\n\t\tthrow new TypeError('target is not an object')\n\t}\n\tfor (var key in source) {\n\t\tif (Object.prototype.hasOwnProperty.call(source, key)) {\n\t\t\ttarget[key] = source[key]\n\t\t}\n\t}\n\treturn target\n}\n\n/**\n * All mime types that are allowed as input to `DOMParser.parseFromString`\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/DOMParser/parseFromString#Argument02 MDN\n * @see https://html.spec.whatwg.org/multipage/dynamic-markup-insertion.html#domparsersupportedtype WHATWG HTML Spec\n * @see DOMParser.prototype.parseFromString\n */\nvar MIME_TYPE = freeze({\n\t/**\n\t * `text/html`, the only mime type that triggers treating an XML document as HTML.\n\t *\n\t * @see DOMParser.SupportedType.isHTML\n\t * @see https://www.iana.org/assignments/media-types/text/html IANA MimeType registration\n\t * @see https://en.wikipedia.org/wiki/HTML Wikipedia\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/DOMParser/parseFromString MDN\n\t * @see https://html.spec.whatwg.org/multipage/dynamic-markup-insertion.html#dom-domparser-parsefromstring WHATWG HTML Spec\n\t */\n\tHTML: 'text/html',\n\n\t/**\n\t * Helper method to check a mime type if it indicates an HTML document\n\t *\n\t * @param {string} [value]\n\t * @returns {boolean}\n\t *\n\t * @see https://www.iana.org/assignments/media-types/text/html IANA MimeType registration\n\t * @see https://en.wikipedia.org/wiki/HTML Wikipedia\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/DOMParser/parseFromString MDN\n\t * @see https://html.spec.whatwg.org/multipage/dynamic-markup-insertion.html#dom-domparser-parsefromstring \t */\n\tisHTML: function (value) {\n\t\treturn value === MIME_TYPE.HTML\n\t},\n\n\t/**\n\t * `application/xml`, the standard mime type for XML documents.\n\t *\n\t * @see https://www.iana.org/assignments/media-types/application/xml IANA MimeType registration\n\t * @see https://tools.ietf.org/html/rfc7303#section-9.1 RFC 7303\n\t * @see https://en.wikipedia.org/wiki/XML_and_MIME Wikipedia\n\t */\n\tXML_APPLICATION: 'application/xml',\n\n\t/**\n\t * `text/html`, an alias for `application/xml`.\n\t *\n\t * @see https://tools.ietf.org/html/rfc7303#section-9.2 RFC 7303\n\t * @see https://www.iana.org/assignments/media-types/text/xml IANA MimeType registration\n\t * @see https://en.wikipedia.org/wiki/XML_and_MIME Wikipedia\n\t */\n\tXML_TEXT: 'text/xml',\n\n\t/**\n\t * `application/xhtml+xml`, indicates an XML document that has the default HTML namespace,\n\t * but is parsed as an XML document.\n\t *\n\t * @see https://www.iana.org/assignments/media-types/application/xhtml+xml IANA MimeType registration\n\t * @see https://dom.spec.whatwg.org/#dom-domimplementation-createdocument WHATWG DOM Spec\n\t * @see https://en.wikipedia.org/wiki/XHTML Wikipedia\n\t */\n\tXML_XHTML_APPLICATION: 'application/xhtml+xml',\n\n\t/**\n\t * `image/svg+xml`,\n\t *\n\t * @see https://www.iana.org/assignments/media-types/image/svg+xml IANA MimeType registration\n\t * @see https://www.w3.org/TR/SVG11/ W3C SVG 1.1\n\t * @see https://en.wikipedia.org/wiki/Scalable_Vector_Graphics Wikipedia\n\t */\n\tXML_SVG_IMAGE: 'image/svg+xml',\n})\n\n/**\n * Namespaces that are used in this code base.\n *\n * @see http://www.w3.org/TR/REC-xml-names\n */\nvar NAMESPACE = freeze({\n\t/**\n\t * The XHTML namespace.\n\t *\n\t * @see http://www.w3.org/1999/xhtml\n\t */\n\tHTML: 'http://www.w3.org/1999/xhtml',\n\n\t/**\n\t * Checks if `uri` equals `NAMESPACE.HTML`.\n\t *\n\t * @param {string} [uri]\n\t *\n\t * @see NAMESPACE.HTML\n\t */\n\tisHTML: function (uri) {\n\t\treturn uri === NAMESPACE.HTML\n\t},\n\n\t/**\n\t * The SVG namespace.\n\t *\n\t * @see http://www.w3.org/2000/svg\n\t */\n\tSVG: 'http://www.w3.org/2000/svg',\n\n\t/**\n\t * The `xml:` namespace.\n\t *\n\t * @see http://www.w3.org/XML/1998/namespace\n\t */\n\tXML: 'http://www.w3.org/XML/1998/namespace',\n\n\t/**\n\t * The `xmlns:` namespace\n\t *\n\t * @see https://www.w3.org/2000/xmlns/\n\t */\n\tXMLNS: 'http://www.w3.org/2000/xmlns/',\n})\n\nexports.assign = assign;\nexports.find = find;\nexports.freeze = freeze;\nexports.MIME_TYPE = MIME_TYPE;\nexports.NAMESPACE = NAMESPACE;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@xmldom/xmldom/lib/conventions.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@xmldom/xmldom/lib/dom-parser.js":
/*!*******************************************************!*\
  !*** ./node_modules/@xmldom/xmldom/lib/dom-parser.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var conventions = __webpack_require__(/*! ./conventions */ \"(ssr)/./node_modules/@xmldom/xmldom/lib/conventions.js\");\nvar dom = __webpack_require__(/*! ./dom */ \"(ssr)/./node_modules/@xmldom/xmldom/lib/dom.js\")\nvar entities = __webpack_require__(/*! ./entities */ \"(ssr)/./node_modules/@xmldom/xmldom/lib/entities.js\");\nvar sax = __webpack_require__(/*! ./sax */ \"(ssr)/./node_modules/@xmldom/xmldom/lib/sax.js\");\n\nvar DOMImplementation = dom.DOMImplementation;\n\nvar NAMESPACE = conventions.NAMESPACE;\n\nvar ParseError = sax.ParseError;\nvar XMLReader = sax.XMLReader;\n\n/**\n * Normalizes line ending according to https://www.w3.org/TR/xml11/#sec-line-ends:\n *\n * > XML parsed entities are often stored in computer files which,\n * > for editing convenience, are organized into lines.\n * > These lines are typically separated by some combination\n * > of the characters CARRIAGE RETURN (#xD) and LINE FEED (#xA).\n * >\n * > To simplify the tasks of applications, the XML processor must behave\n * > as if it normalized all line breaks in external parsed entities (including the document entity)\n * > on input, before parsing, by translating all of the following to a single #xA character:\n * >\n * > 1. the two-character sequence #xD #xA\n * > 2. the two-character sequence #xD #x85\n * > 3. the single character #x85\n * > 4. the single character #x2028\n * > 5. any #xD character that is not immediately followed by #xA or #x85.\n *\n * @param {string} input\n * @returns {string}\n */\nfunction normalizeLineEndings(input) {\n\treturn input\n\t\t.replace(/\\r[\\n\\u0085]/g, '\\n')\n\t\t.replace(/[\\r\\u0085\\u2028]/g, '\\n')\n}\n\n/**\n * @typedef Locator\n * @property {number} [columnNumber]\n * @property {number} [lineNumber]\n */\n\n/**\n * @typedef DOMParserOptions\n * @property {DOMHandler} [domBuilder]\n * @property {Function} [errorHandler]\n * @property {(string) => string} [normalizeLineEndings] used to replace line endings before parsing\n * \t\t\t\t\t\tdefaults to `normalizeLineEndings`\n * @property {Locator} [locator]\n * @property {Record<string, string>} [xmlns]\n *\n * @see normalizeLineEndings\n */\n\n/**\n * The DOMParser interface provides the ability to parse XML or HTML source code\n * from a string into a DOM `Document`.\n *\n * _xmldom is different from the spec in that it allows an `options` parameter,\n * to override the default behavior._\n *\n * @param {DOMParserOptions} [options]\n * @constructor\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/DOMParser\n * @see https://html.spec.whatwg.org/multipage/dynamic-markup-insertion.html#dom-parsing-and-serialization\n */\nfunction DOMParser(options){\n\tthis.options = options ||{locator:{}};\n}\n\nDOMParser.prototype.parseFromString = function(source,mimeType){\n\tvar options = this.options;\n\tvar sax =  new XMLReader();\n\tvar domBuilder = options.domBuilder || new DOMHandler();//contentHandler and LexicalHandler\n\tvar errorHandler = options.errorHandler;\n\tvar locator = options.locator;\n\tvar defaultNSMap = options.xmlns||{};\n\tvar isHTML = /\\/x?html?$/.test(mimeType);//mimeType.toLowerCase().indexOf('html') > -1;\n  \tvar entityMap = isHTML ? entities.HTML_ENTITIES : entities.XML_ENTITIES;\n\tif(locator){\n\t\tdomBuilder.setDocumentLocator(locator)\n\t}\n\n\tsax.errorHandler = buildErrorHandler(errorHandler,domBuilder,locator);\n\tsax.domBuilder = options.domBuilder || domBuilder;\n\tif(isHTML){\n\t\tdefaultNSMap[''] = NAMESPACE.HTML;\n\t}\n\tdefaultNSMap.xml = defaultNSMap.xml || NAMESPACE.XML;\n\tvar normalize = options.normalizeLineEndings || normalizeLineEndings;\n\tif (source && typeof source === 'string') {\n\t\tsax.parse(\n\t\t\tnormalize(source),\n\t\t\tdefaultNSMap,\n\t\t\tentityMap\n\t\t)\n\t} else {\n\t\tsax.errorHandler.error('invalid doc source')\n\t}\n\treturn domBuilder.doc;\n}\nfunction buildErrorHandler(errorImpl,domBuilder,locator){\n\tif(!errorImpl){\n\t\tif(domBuilder instanceof DOMHandler){\n\t\t\treturn domBuilder;\n\t\t}\n\t\terrorImpl = domBuilder ;\n\t}\n\tvar errorHandler = {}\n\tvar isCallback = errorImpl instanceof Function;\n\tlocator = locator||{}\n\tfunction build(key){\n\t\tvar fn = errorImpl[key];\n\t\tif(!fn && isCallback){\n\t\t\tfn = errorImpl.length == 2?function(msg){errorImpl(key,msg)}:errorImpl;\n\t\t}\n\t\terrorHandler[key] = fn && function(msg){\n\t\t\tfn('[xmldom '+key+']\\t'+msg+_locator(locator));\n\t\t}||function(){};\n\t}\n\tbuild('warning');\n\tbuild('error');\n\tbuild('fatalError');\n\treturn errorHandler;\n}\n\n//console.log('#\\n\\n\\n\\n\\n\\n\\n####')\n/**\n * +ContentHandler+ErrorHandler\n * +LexicalHandler+EntityResolver2\n * -DeclHandler-DTDHandler\n *\n * DefaultHandler:EntityResolver, DTDHandler, ContentHandler, ErrorHandler\n * DefaultHandler2:DefaultHandler,LexicalHandler, DeclHandler, EntityResolver2\n * @link http://www.saxproject.org/apidoc/org/xml/sax/helpers/DefaultHandler.html\n */\nfunction DOMHandler() {\n    this.cdata = false;\n}\nfunction position(locator,node){\n\tnode.lineNumber = locator.lineNumber;\n\tnode.columnNumber = locator.columnNumber;\n}\n/**\n * @see org.xml.sax.ContentHandler#startDocument\n * @link http://www.saxproject.org/apidoc/org/xml/sax/ContentHandler.html\n */\nDOMHandler.prototype = {\n\tstartDocument : function() {\n    \tthis.doc = new DOMImplementation().createDocument(null, null, null);\n    \tif (this.locator) {\n        \tthis.doc.documentURI = this.locator.systemId;\n    \t}\n\t},\n\tstartElement:function(namespaceURI, localName, qName, attrs) {\n\t\tvar doc = this.doc;\n\t    var el = doc.createElementNS(namespaceURI, qName||localName);\n\t    var len = attrs.length;\n\t    appendElement(this, el);\n\t    this.currentElement = el;\n\n\t\tthis.locator && position(this.locator,el)\n\t    for (var i = 0 ; i < len; i++) {\n\t        var namespaceURI = attrs.getURI(i);\n\t        var value = attrs.getValue(i);\n\t        var qName = attrs.getQName(i);\n\t\t\tvar attr = doc.createAttributeNS(namespaceURI, qName);\n\t\t\tthis.locator &&position(attrs.getLocator(i),attr);\n\t\t\tattr.value = attr.nodeValue = value;\n\t\t\tel.setAttributeNode(attr)\n\t    }\n\t},\n\tendElement:function(namespaceURI, localName, qName) {\n\t\tvar current = this.currentElement\n\t\tvar tagName = current.tagName;\n\t\tthis.currentElement = current.parentNode;\n\t},\n\tstartPrefixMapping:function(prefix, uri) {\n\t},\n\tendPrefixMapping:function(prefix) {\n\t},\n\tprocessingInstruction:function(target, data) {\n\t    var ins = this.doc.createProcessingInstruction(target, data);\n\t    this.locator && position(this.locator,ins)\n\t    appendElement(this, ins);\n\t},\n\tignorableWhitespace:function(ch, start, length) {\n\t},\n\tcharacters:function(chars, start, length) {\n\t\tchars = _toString.apply(this,arguments)\n\t\t//console.log(chars)\n\t\tif(chars){\n\t\t\tif (this.cdata) {\n\t\t\t\tvar charNode = this.doc.createCDATASection(chars);\n\t\t\t} else {\n\t\t\t\tvar charNode = this.doc.createTextNode(chars);\n\t\t\t}\n\t\t\tif(this.currentElement){\n\t\t\t\tthis.currentElement.appendChild(charNode);\n\t\t\t}else if(/^\\s*$/.test(chars)){\n\t\t\t\tthis.doc.appendChild(charNode);\n\t\t\t\t//process xml\n\t\t\t}\n\t\t\tthis.locator && position(this.locator,charNode)\n\t\t}\n\t},\n\tskippedEntity:function(name) {\n\t},\n\tendDocument:function() {\n\t\tthis.doc.normalize();\n\t},\n\tsetDocumentLocator:function (locator) {\n\t    if(this.locator = locator){// && !('lineNumber' in locator)){\n\t    \tlocator.lineNumber = 0;\n\t    }\n\t},\n\t//LexicalHandler\n\tcomment:function(chars, start, length) {\n\t\tchars = _toString.apply(this,arguments)\n\t    var comm = this.doc.createComment(chars);\n\t    this.locator && position(this.locator,comm)\n\t    appendElement(this, comm);\n\t},\n\n\tstartCDATA:function() {\n\t    //used in characters() methods\n\t    this.cdata = true;\n\t},\n\tendCDATA:function() {\n\t    this.cdata = false;\n\t},\n\n\tstartDTD:function(name, publicId, systemId) {\n\t\tvar impl = this.doc.implementation;\n\t    if (impl && impl.createDocumentType) {\n\t        var dt = impl.createDocumentType(name, publicId, systemId);\n\t        this.locator && position(this.locator,dt)\n\t        appendElement(this, dt);\n\t\t\t\t\tthis.doc.doctype = dt;\n\t    }\n\t},\n\t/**\n\t * @see org.xml.sax.ErrorHandler\n\t * @link http://www.saxproject.org/apidoc/org/xml/sax/ErrorHandler.html\n\t */\n\twarning:function(error) {\n\t\tconsole.warn('[xmldom warning]\\t'+error,_locator(this.locator));\n\t},\n\terror:function(error) {\n\t\tconsole.error('[xmldom error]\\t'+error,_locator(this.locator));\n\t},\n\tfatalError:function(error) {\n\t\tthrow new ParseError(error, this.locator);\n\t}\n}\nfunction _locator(l){\n\tif(l){\n\t\treturn '\\n@'+(l.systemId ||'')+'#[line:'+l.lineNumber+',col:'+l.columnNumber+']'\n\t}\n}\nfunction _toString(chars,start,length){\n\tif(typeof chars == 'string'){\n\t\treturn chars.substr(start,length)\n\t}else{//java sax connect width xmldom on rhino(what about: \"? && !(chars instanceof String)\")\n\t\tif(chars.length >= start+length || start){\n\t\t\treturn new java.lang.String(chars,start,length)+'';\n\t\t}\n\t\treturn chars;\n\t}\n}\n\n/*\n * @link http://www.saxproject.org/apidoc/org/xml/sax/ext/LexicalHandler.html\n * used method of org.xml.sax.ext.LexicalHandler:\n *  #comment(chars, start, length)\n *  #startCDATA()\n *  #endCDATA()\n *  #startDTD(name, publicId, systemId)\n *\n *\n * IGNORED method of org.xml.sax.ext.LexicalHandler:\n *  #endDTD()\n *  #startEntity(name)\n *  #endEntity(name)\n *\n *\n * @link http://www.saxproject.org/apidoc/org/xml/sax/ext/DeclHandler.html\n * IGNORED method of org.xml.sax.ext.DeclHandler\n * \t#attributeDecl(eName, aName, type, mode, value)\n *  #elementDecl(name, model)\n *  #externalEntityDecl(name, publicId, systemId)\n *  #internalEntityDecl(name, value)\n * @link http://www.saxproject.org/apidoc/org/xml/sax/ext/EntityResolver2.html\n * IGNORED method of org.xml.sax.EntityResolver2\n *  #resolveEntity(String name,String publicId,String baseURI,String systemId)\n *  #resolveEntity(publicId, systemId)\n *  #getExternalSubset(name, baseURI)\n * @link http://www.saxproject.org/apidoc/org/xml/sax/DTDHandler.html\n * IGNORED method of org.xml.sax.DTDHandler\n *  #notationDecl(name, publicId, systemId) {};\n *  #unparsedEntityDecl(name, publicId, systemId, notationName) {};\n */\n\"endDTD,startEntity,endEntity,attributeDecl,elementDecl,externalEntityDecl,internalEntityDecl,resolveEntity,getExternalSubset,notationDecl,unparsedEntityDecl\".replace(/\\w+/g,function(key){\n\tDOMHandler.prototype[key] = function(){return null}\n})\n\n/* Private static helpers treated below as private instance methods, so don't need to add these to the public API; we might use a Relator to also get rid of non-standard public properties */\nfunction appendElement (hander,node) {\n    if (!hander.currentElement) {\n        hander.doc.appendChild(node);\n    } else {\n        hander.currentElement.appendChild(node);\n    }\n}//appendChild and setAttributeNS are preformance key\n\nexports.__DOMHandler = DOMHandler;\nexports.normalizeLineEndings = normalizeLineEndings;\nexports.DOMParser = DOMParser;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@xmldom/xmldom/lib/dom-parser.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@xmldom/xmldom/lib/dom.js":
/*!************************************************!*\
  !*** ./node_modules/@xmldom/xmldom/lib/dom.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var conventions = __webpack_require__(/*! ./conventions */ \"(ssr)/./node_modules/@xmldom/xmldom/lib/conventions.js\");\n\nvar find = conventions.find;\nvar NAMESPACE = conventions.NAMESPACE;\n\n/**\n * A prerequisite for `[].filter`, to drop elements that are empty\n * @param {string} input\n * @returns {boolean}\n */\nfunction notEmptyString (input) {\n\treturn input !== ''\n}\n/**\n * @see https://infra.spec.whatwg.org/#split-on-ascii-whitespace\n * @see https://infra.spec.whatwg.org/#ascii-whitespace\n *\n * @param {string} input\n * @returns {string[]} (can be empty)\n */\nfunction splitOnASCIIWhitespace(input) {\n\t// U+0009 TAB, U+000A LF, U+000C FF, U+000D CR, U+0020 SPACE\n\treturn input ? input.split(/[\\t\\n\\f\\r ]+/).filter(notEmptyString) : []\n}\n\n/**\n * Adds element as a key to current if it is not already present.\n *\n * @param {Record<string, boolean | undefined>} current\n * @param {string} element\n * @returns {Record<string, boolean | undefined>}\n */\nfunction orderedSetReducer (current, element) {\n\tif (!current.hasOwnProperty(element)) {\n\t\tcurrent[element] = true;\n\t}\n\treturn current;\n}\n\n/**\n * @see https://infra.spec.whatwg.org/#ordered-set\n * @param {string} input\n * @returns {string[]}\n */\nfunction toOrderedSet(input) {\n\tif (!input) return [];\n\tvar list = splitOnASCIIWhitespace(input);\n\treturn Object.keys(list.reduce(orderedSetReducer, {}))\n}\n\n/**\n * Uses `list.indexOf` to implement something like `Array.prototype.includes`,\n * which we can not rely on being available.\n *\n * @param {any[]} list\n * @returns {function(any): boolean}\n */\nfunction arrayIncludes (list) {\n\treturn function(element) {\n\t\treturn list && list.indexOf(element) !== -1;\n\t}\n}\n\nfunction copy(src,dest){\n\tfor(var p in src){\n\t\tif (Object.prototype.hasOwnProperty.call(src, p)) {\n\t\t\tdest[p] = src[p];\n\t\t}\n\t}\n}\n\n/**\n^\\w+\\.prototype\\.([_\\w]+)\\s*=\\s*((?:.*\\{\\s*?[\\r\\n][\\s\\S]*?^})|\\S.*?(?=[;\\r\\n]));?\n^\\w+\\.prototype\\.([_\\w]+)\\s*=\\s*(\\S.*?(?=[;\\r\\n]));?\n */\nfunction _extends(Class,Super){\n\tvar pt = Class.prototype;\n\tif(!(pt instanceof Super)){\n\t\tfunction t(){};\n\t\tt.prototype = Super.prototype;\n\t\tt = new t();\n\t\tcopy(pt,t);\n\t\tClass.prototype = pt = t;\n\t}\n\tif(pt.constructor != Class){\n\t\tif(typeof Class != 'function'){\n\t\t\tconsole.error(\"unknown Class:\"+Class)\n\t\t}\n\t\tpt.constructor = Class\n\t}\n}\n\n// Node Types\nvar NodeType = {}\nvar ELEMENT_NODE                = NodeType.ELEMENT_NODE                = 1;\nvar ATTRIBUTE_NODE              = NodeType.ATTRIBUTE_NODE              = 2;\nvar TEXT_NODE                   = NodeType.TEXT_NODE                   = 3;\nvar CDATA_SECTION_NODE          = NodeType.CDATA_SECTION_NODE          = 4;\nvar ENTITY_REFERENCE_NODE       = NodeType.ENTITY_REFERENCE_NODE       = 5;\nvar ENTITY_NODE                 = NodeType.ENTITY_NODE                 = 6;\nvar PROCESSING_INSTRUCTION_NODE = NodeType.PROCESSING_INSTRUCTION_NODE = 7;\nvar COMMENT_NODE                = NodeType.COMMENT_NODE                = 8;\nvar DOCUMENT_NODE               = NodeType.DOCUMENT_NODE               = 9;\nvar DOCUMENT_TYPE_NODE          = NodeType.DOCUMENT_TYPE_NODE          = 10;\nvar DOCUMENT_FRAGMENT_NODE      = NodeType.DOCUMENT_FRAGMENT_NODE      = 11;\nvar NOTATION_NODE               = NodeType.NOTATION_NODE               = 12;\n\n// ExceptionCode\nvar ExceptionCode = {}\nvar ExceptionMessage = {};\nvar INDEX_SIZE_ERR              = ExceptionCode.INDEX_SIZE_ERR              = ((ExceptionMessage[1]=\"Index size error\"),1);\nvar DOMSTRING_SIZE_ERR          = ExceptionCode.DOMSTRING_SIZE_ERR          = ((ExceptionMessage[2]=\"DOMString size error\"),2);\nvar HIERARCHY_REQUEST_ERR       = ExceptionCode.HIERARCHY_REQUEST_ERR       = ((ExceptionMessage[3]=\"Hierarchy request error\"),3);\nvar WRONG_DOCUMENT_ERR          = ExceptionCode.WRONG_DOCUMENT_ERR          = ((ExceptionMessage[4]=\"Wrong document\"),4);\nvar INVALID_CHARACTER_ERR       = ExceptionCode.INVALID_CHARACTER_ERR       = ((ExceptionMessage[5]=\"Invalid character\"),5);\nvar NO_DATA_ALLOWED_ERR         = ExceptionCode.NO_DATA_ALLOWED_ERR         = ((ExceptionMessage[6]=\"No data allowed\"),6);\nvar NO_MODIFICATION_ALLOWED_ERR = ExceptionCode.NO_MODIFICATION_ALLOWED_ERR = ((ExceptionMessage[7]=\"No modification allowed\"),7);\nvar NOT_FOUND_ERR               = ExceptionCode.NOT_FOUND_ERR               = ((ExceptionMessage[8]=\"Not found\"),8);\nvar NOT_SUPPORTED_ERR           = ExceptionCode.NOT_SUPPORTED_ERR           = ((ExceptionMessage[9]=\"Not supported\"),9);\nvar INUSE_ATTRIBUTE_ERR         = ExceptionCode.INUSE_ATTRIBUTE_ERR         = ((ExceptionMessage[10]=\"Attribute in use\"),10);\n//level2\nvar INVALID_STATE_ERR        \t= ExceptionCode.INVALID_STATE_ERR        \t= ((ExceptionMessage[11]=\"Invalid state\"),11);\nvar SYNTAX_ERR               \t= ExceptionCode.SYNTAX_ERR               \t= ((ExceptionMessage[12]=\"Syntax error\"),12);\nvar INVALID_MODIFICATION_ERR \t= ExceptionCode.INVALID_MODIFICATION_ERR \t= ((ExceptionMessage[13]=\"Invalid modification\"),13);\nvar NAMESPACE_ERR            \t= ExceptionCode.NAMESPACE_ERR           \t= ((ExceptionMessage[14]=\"Invalid namespace\"),14);\nvar INVALID_ACCESS_ERR       \t= ExceptionCode.INVALID_ACCESS_ERR      \t= ((ExceptionMessage[15]=\"Invalid access\"),15);\n\n/**\n * DOM Level 2\n * Object DOMException\n * @see http://www.w3.org/TR/2000/REC-DOM-Level-2-Core-20001113/ecma-script-binding.html\n * @see http://www.w3.org/TR/REC-DOM-Level-1/ecma-script-language-binding.html\n */\nfunction DOMException(code, message) {\n\tif(message instanceof Error){\n\t\tvar error = message;\n\t}else{\n\t\terror = this;\n\t\tError.call(this, ExceptionMessage[code]);\n\t\tthis.message = ExceptionMessage[code];\n\t\tif(Error.captureStackTrace) Error.captureStackTrace(this, DOMException);\n\t}\n\terror.code = code;\n\tif(message) this.message = this.message + \": \" + message;\n\treturn error;\n};\nDOMException.prototype = Error.prototype;\ncopy(ExceptionCode,DOMException)\n\n/**\n * @see http://www.w3.org/TR/2000/REC-DOM-Level-2-Core-20001113/core.html#ID-536297177\n * The NodeList interface provides the abstraction of an ordered collection of nodes, without defining or constraining how this collection is implemented. NodeList objects in the DOM are live.\n * The items in the NodeList are accessible via an integral index, starting from 0.\n */\nfunction NodeList() {\n};\nNodeList.prototype = {\n\t/**\n\t * The number of nodes in the list. The range of valid child node indices is 0 to length-1 inclusive.\n\t * @standard level1\n\t */\n\tlength:0,\n\t/**\n\t * Returns the indexth item in the collection. If index is greater than or equal to the number of nodes in the list, this returns null.\n\t * @standard level1\n\t * @param index  unsigned long\n\t *   Index into the collection.\n\t * @return Node\n\t * \tThe node at the indexth position in the NodeList, or null if that is not a valid index.\n\t */\n\titem: function(index) {\n\t\treturn index >= 0 && index < this.length ? this[index] : null;\n\t},\n\ttoString:function(isHTML,nodeFilter){\n\t\tfor(var buf = [], i = 0;i<this.length;i++){\n\t\t\tserializeToString(this[i],buf,isHTML,nodeFilter);\n\t\t}\n\t\treturn buf.join('');\n\t},\n\t/**\n\t * @private\n\t * @param {function (Node):boolean} predicate\n\t * @returns {Node[]}\n\t */\n\tfilter: function (predicate) {\n\t\treturn Array.prototype.filter.call(this, predicate);\n\t},\n\t/**\n\t * @private\n\t * @param {Node} item\n\t * @returns {number}\n\t */\n\tindexOf: function (item) {\n\t\treturn Array.prototype.indexOf.call(this, item);\n\t},\n};\n\nfunction LiveNodeList(node,refresh){\n\tthis._node = node;\n\tthis._refresh = refresh\n\t_updateLiveList(this);\n}\nfunction _updateLiveList(list){\n\tvar inc = list._node._inc || list._node.ownerDocument._inc;\n\tif (list._inc !== inc) {\n\t\tvar ls = list._refresh(list._node);\n\t\t__set__(list,'length',ls.length);\n\t\tif (!list.$$length || ls.length < list.$$length) {\n\t\t\tfor (var i = ls.length; i in list; i++) {\n\t\t\t\tif (Object.prototype.hasOwnProperty.call(list, i)) {\n\t\t\t\t\tdelete list[i];\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tcopy(ls,list);\n\t\tlist._inc = inc;\n\t}\n}\nLiveNodeList.prototype.item = function(i){\n\t_updateLiveList(this);\n\treturn this[i] || null;\n}\n\n_extends(LiveNodeList,NodeList);\n\n/**\n * Objects implementing the NamedNodeMap interface are used\n * to represent collections of nodes that can be accessed by name.\n * Note that NamedNodeMap does not inherit from NodeList;\n * NamedNodeMaps are not maintained in any particular order.\n * Objects contained in an object implementing NamedNodeMap may also be accessed by an ordinal index,\n * but this is simply to allow convenient enumeration of the contents of a NamedNodeMap,\n * and does not imply that the DOM specifies an order to these Nodes.\n * NamedNodeMap objects in the DOM are live.\n * used for attributes or DocumentType entities\n */\nfunction NamedNodeMap() {\n};\n\nfunction _findNodeIndex(list,node){\n\tvar i = list.length;\n\twhile(i--){\n\t\tif(list[i] === node){return i}\n\t}\n}\n\nfunction _addNamedNode(el,list,newAttr,oldAttr){\n\tif(oldAttr){\n\t\tlist[_findNodeIndex(list,oldAttr)] = newAttr;\n\t}else{\n\t\tlist[list.length++] = newAttr;\n\t}\n\tif(el){\n\t\tnewAttr.ownerElement = el;\n\t\tvar doc = el.ownerDocument;\n\t\tif(doc){\n\t\t\toldAttr && _onRemoveAttribute(doc,el,oldAttr);\n\t\t\t_onAddAttribute(doc,el,newAttr);\n\t\t}\n\t}\n}\nfunction _removeNamedNode(el,list,attr){\n\t//console.log('remove attr:'+attr)\n\tvar i = _findNodeIndex(list,attr);\n\tif(i>=0){\n\t\tvar lastIndex = list.length-1\n\t\twhile(i<lastIndex){\n\t\t\tlist[i] = list[++i]\n\t\t}\n\t\tlist.length = lastIndex;\n\t\tif(el){\n\t\t\tvar doc = el.ownerDocument;\n\t\t\tif(doc){\n\t\t\t\t_onRemoveAttribute(doc,el,attr);\n\t\t\t\tattr.ownerElement = null;\n\t\t\t}\n\t\t}\n\t}else{\n\t\tthrow new DOMException(NOT_FOUND_ERR,new Error(el.tagName+'@'+attr))\n\t}\n}\nNamedNodeMap.prototype = {\n\tlength:0,\n\titem:NodeList.prototype.item,\n\tgetNamedItem: function(key) {\n//\t\tif(key.indexOf(':')>0 || key == 'xmlns'){\n//\t\t\treturn null;\n//\t\t}\n\t\t//console.log()\n\t\tvar i = this.length;\n\t\twhile(i--){\n\t\t\tvar attr = this[i];\n\t\t\t//console.log(attr.nodeName,key)\n\t\t\tif(attr.nodeName == key){\n\t\t\t\treturn attr;\n\t\t\t}\n\t\t}\n\t},\n\tsetNamedItem: function(attr) {\n\t\tvar el = attr.ownerElement;\n\t\tif(el && el!=this._ownerElement){\n\t\t\tthrow new DOMException(INUSE_ATTRIBUTE_ERR);\n\t\t}\n\t\tvar oldAttr = this.getNamedItem(attr.nodeName);\n\t\t_addNamedNode(this._ownerElement,this,attr,oldAttr);\n\t\treturn oldAttr;\n\t},\n\t/* returns Node */\n\tsetNamedItemNS: function(attr) {// raises: WRONG_DOCUMENT_ERR,NO_MODIFICATION_ALLOWED_ERR,INUSE_ATTRIBUTE_ERR\n\t\tvar el = attr.ownerElement, oldAttr;\n\t\tif(el && el!=this._ownerElement){\n\t\t\tthrow new DOMException(INUSE_ATTRIBUTE_ERR);\n\t\t}\n\t\toldAttr = this.getNamedItemNS(attr.namespaceURI,attr.localName);\n\t\t_addNamedNode(this._ownerElement,this,attr,oldAttr);\n\t\treturn oldAttr;\n\t},\n\n\t/* returns Node */\n\tremoveNamedItem: function(key) {\n\t\tvar attr = this.getNamedItem(key);\n\t\t_removeNamedNode(this._ownerElement,this,attr);\n\t\treturn attr;\n\n\n\t},// raises: NOT_FOUND_ERR,NO_MODIFICATION_ALLOWED_ERR\n\n\t//for level2\n\tremoveNamedItemNS:function(namespaceURI,localName){\n\t\tvar attr = this.getNamedItemNS(namespaceURI,localName);\n\t\t_removeNamedNode(this._ownerElement,this,attr);\n\t\treturn attr;\n\t},\n\tgetNamedItemNS: function(namespaceURI, localName) {\n\t\tvar i = this.length;\n\t\twhile(i--){\n\t\t\tvar node = this[i];\n\t\t\tif(node.localName == localName && node.namespaceURI == namespaceURI){\n\t\t\t\treturn node;\n\t\t\t}\n\t\t}\n\t\treturn null;\n\t}\n};\n\n/**\n * The DOMImplementation interface represents an object providing methods\n * which are not dependent on any particular document.\n * Such an object is returned by the `Document.implementation` property.\n *\n * __The individual methods describe the differences compared to the specs.__\n *\n * @constructor\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/DOMImplementation MDN\n * @see https://www.w3.org/TR/REC-DOM-Level-1/level-one-core.html#ID-102161490 DOM Level 1 Core (Initial)\n * @see https://www.w3.org/TR/DOM-Level-2-Core/core.html#ID-102161490 DOM Level 2 Core\n * @see https://www.w3.org/TR/DOM-Level-3-Core/core.html#ID-102161490 DOM Level 3 Core\n * @see https://dom.spec.whatwg.org/#domimplementation DOM Living Standard\n */\nfunction DOMImplementation() {\n}\n\nDOMImplementation.prototype = {\n\t/**\n\t * The DOMImplementation.hasFeature() method returns a Boolean flag indicating if a given feature is supported.\n\t * The different implementations fairly diverged in what kind of features were reported.\n\t * The latest version of the spec settled to force this method to always return true, where the functionality was accurate and in use.\n\t *\n\t * @deprecated It is deprecated and modern browsers return true in all cases.\n\t *\n\t * @param {string} feature\n\t * @param {string} [version]\n\t * @returns {boolean} always true\n\t *\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/DOMImplementation/hasFeature MDN\n\t * @see https://www.w3.org/TR/REC-DOM-Level-1/level-one-core.html#ID-5CED94D7 DOM Level 1 Core\n\t * @see https://dom.spec.whatwg.org/#dom-domimplementation-hasfeature DOM Living Standard\n\t */\n\thasFeature: function(feature, version) {\n\t\t\treturn true;\n\t},\n\t/**\n\t * Creates an XML Document object of the specified type with its document element.\n\t *\n\t * __It behaves slightly different from the description in the living standard__:\n\t * - There is no interface/class `XMLDocument`, it returns a `Document` instance.\n\t * - `contentType`, `encoding`, `mode`, `origin`, `url` fields are currently not declared.\n\t * - this implementation is not validating names or qualified names\n\t *   (when parsing XML strings, the SAX parser takes care of that)\n\t *\n\t * @param {string|null} namespaceURI\n\t * @param {string} qualifiedName\n\t * @param {DocumentType=null} doctype\n\t * @returns {Document}\n\t *\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/DOMImplementation/createDocument MDN\n\t * @see https://www.w3.org/TR/DOM-Level-2-Core/core.html#Level-2-Core-DOM-createDocument DOM Level 2 Core (initial)\n\t * @see https://dom.spec.whatwg.org/#dom-domimplementation-createdocument  DOM Level 2 Core\n\t *\n\t * @see https://dom.spec.whatwg.org/#validate-and-extract DOM: Validate and extract\n\t * @see https://www.w3.org/TR/xml/#NT-NameStartChar XML Spec: Names\n\t * @see https://www.w3.org/TR/xml-names/#ns-qualnames XML Namespaces: Qualified names\n\t */\n\tcreateDocument: function(namespaceURI,  qualifiedName, doctype){\n\t\tvar doc = new Document();\n\t\tdoc.implementation = this;\n\t\tdoc.childNodes = new NodeList();\n\t\tdoc.doctype = doctype || null;\n\t\tif (doctype){\n\t\t\tdoc.appendChild(doctype);\n\t\t}\n\t\tif (qualifiedName){\n\t\t\tvar root = doc.createElementNS(namespaceURI, qualifiedName);\n\t\t\tdoc.appendChild(root);\n\t\t}\n\t\treturn doc;\n\t},\n\t/**\n\t * Returns a doctype, with the given `qualifiedName`, `publicId`, and `systemId`.\n\t *\n\t * __This behavior is slightly different from the in the specs__:\n\t * - this implementation is not validating names or qualified names\n\t *   (when parsing XML strings, the SAX parser takes care of that)\n\t *\n\t * @param {string} qualifiedName\n\t * @param {string} [publicId]\n\t * @param {string} [systemId]\n\t * @returns {DocumentType} which can either be used with `DOMImplementation.createDocument` upon document creation\n\t * \t\t\t\t  or can be put into the document via methods like `Node.insertBefore()` or `Node.replaceChild()`\n\t *\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/DOMImplementation/createDocumentType MDN\n\t * @see https://www.w3.org/TR/DOM-Level-2-Core/core.html#Level-2-Core-DOM-createDocType DOM Level 2 Core\n\t * @see https://dom.spec.whatwg.org/#dom-domimplementation-createdocumenttype DOM Living Standard\n\t *\n\t * @see https://dom.spec.whatwg.org/#validate-and-extract DOM: Validate and extract\n\t * @see https://www.w3.org/TR/xml/#NT-NameStartChar XML Spec: Names\n\t * @see https://www.w3.org/TR/xml-names/#ns-qualnames XML Namespaces: Qualified names\n\t */\n\tcreateDocumentType: function(qualifiedName, publicId, systemId){\n\t\tvar node = new DocumentType();\n\t\tnode.name = qualifiedName;\n\t\tnode.nodeName = qualifiedName;\n\t\tnode.publicId = publicId || '';\n\t\tnode.systemId = systemId || '';\n\n\t\treturn node;\n\t}\n};\n\n\n/**\n * @see http://www.w3.org/TR/2000/REC-DOM-Level-2-Core-20001113/core.html#ID-1950641247\n */\n\nfunction Node() {\n};\n\nNode.prototype = {\n\tfirstChild : null,\n\tlastChild : null,\n\tpreviousSibling : null,\n\tnextSibling : null,\n\tattributes : null,\n\tparentNode : null,\n\tchildNodes : null,\n\townerDocument : null,\n\tnodeValue : null,\n\tnamespaceURI : null,\n\tprefix : null,\n\tlocalName : null,\n\t// Modified in DOM Level 2:\n\tinsertBefore:function(newChild, refChild){//raises\n\t\treturn _insertBefore(this,newChild,refChild);\n\t},\n\treplaceChild:function(newChild, oldChild){//raises\n\t\t_insertBefore(this, newChild,oldChild, assertPreReplacementValidityInDocument);\n\t\tif(oldChild){\n\t\t\tthis.removeChild(oldChild);\n\t\t}\n\t},\n\tremoveChild:function(oldChild){\n\t\treturn _removeChild(this,oldChild);\n\t},\n\tappendChild:function(newChild){\n\t\treturn this.insertBefore(newChild,null);\n\t},\n\thasChildNodes:function(){\n\t\treturn this.firstChild != null;\n\t},\n\tcloneNode:function(deep){\n\t\treturn cloneNode(this.ownerDocument||this,this,deep);\n\t},\n\t// Modified in DOM Level 2:\n\tnormalize:function(){\n\t\tvar child = this.firstChild;\n\t\twhile(child){\n\t\t\tvar next = child.nextSibling;\n\t\t\tif(next && next.nodeType == TEXT_NODE && child.nodeType == TEXT_NODE){\n\t\t\t\tthis.removeChild(next);\n\t\t\t\tchild.appendData(next.data);\n\t\t\t}else{\n\t\t\t\tchild.normalize();\n\t\t\t\tchild = next;\n\t\t\t}\n\t\t}\n\t},\n  \t// Introduced in DOM Level 2:\n\tisSupported:function(feature, version){\n\t\treturn this.ownerDocument.implementation.hasFeature(feature,version);\n\t},\n    // Introduced in DOM Level 2:\n    hasAttributes:function(){\n    \treturn this.attributes.length>0;\n    },\n\t/**\n\t * Look up the prefix associated to the given namespace URI, starting from this node.\n\t * **The default namespace declarations are ignored by this method.**\n\t * See Namespace Prefix Lookup for details on the algorithm used by this method.\n\t *\n\t * _Note: The implementation seems to be incomplete when compared to the algorithm described in the specs._\n\t *\n\t * @param {string | null} namespaceURI\n\t * @returns {string | null}\n\t * @see https://www.w3.org/TR/DOM-Level-3-Core/core.html#Node3-lookupNamespacePrefix\n\t * @see https://www.w3.org/TR/DOM-Level-3-Core/namespaces-algorithms.html#lookupNamespacePrefixAlgo\n\t * @see https://dom.spec.whatwg.org/#dom-node-lookupprefix\n\t * @see https://github.com/xmldom/xmldom/issues/322\n\t */\n    lookupPrefix:function(namespaceURI){\n    \tvar el = this;\n    \twhile(el){\n    \t\tvar map = el._nsMap;\n    \t\t//console.dir(map)\n    \t\tif(map){\n    \t\t\tfor(var n in map){\n\t\t\t\t\t\tif (Object.prototype.hasOwnProperty.call(map, n) && map[n] === namespaceURI) {\n\t\t\t\t\t\t\treturn n;\n\t\t\t\t\t\t}\n    \t\t\t}\n    \t\t}\n    \t\tel = el.nodeType == ATTRIBUTE_NODE?el.ownerDocument : el.parentNode;\n    \t}\n    \treturn null;\n    },\n    // Introduced in DOM Level 3:\n    lookupNamespaceURI:function(prefix){\n    \tvar el = this;\n    \twhile(el){\n    \t\tvar map = el._nsMap;\n    \t\t//console.dir(map)\n    \t\tif(map){\n    \t\t\tif(Object.prototype.hasOwnProperty.call(map, prefix)){\n    \t\t\t\treturn map[prefix] ;\n    \t\t\t}\n    \t\t}\n    \t\tel = el.nodeType == ATTRIBUTE_NODE?el.ownerDocument : el.parentNode;\n    \t}\n    \treturn null;\n    },\n    // Introduced in DOM Level 3:\n    isDefaultNamespace:function(namespaceURI){\n    \tvar prefix = this.lookupPrefix(namespaceURI);\n    \treturn prefix == null;\n    }\n};\n\n\nfunction _xmlEncoder(c){\n\treturn c == '<' && '&lt;' ||\n         c == '>' && '&gt;' ||\n         c == '&' && '&amp;' ||\n         c == '\"' && '&quot;' ||\n         '&#'+c.charCodeAt()+';'\n}\n\n\ncopy(NodeType,Node);\ncopy(NodeType,Node.prototype);\n\n/**\n * @param callback return true for continue,false for break\n * @return boolean true: break visit;\n */\nfunction _visitNode(node,callback){\n\tif(callback(node)){\n\t\treturn true;\n\t}\n\tif(node = node.firstChild){\n\t\tdo{\n\t\t\tif(_visitNode(node,callback)){return true}\n        }while(node=node.nextSibling)\n    }\n}\n\n\n\nfunction Document(){\n\tthis.ownerDocument = this;\n}\n\nfunction _onAddAttribute(doc,el,newAttr){\n\tdoc && doc._inc++;\n\tvar ns = newAttr.namespaceURI ;\n\tif(ns === NAMESPACE.XMLNS){\n\t\t//update namespace\n\t\tel._nsMap[newAttr.prefix?newAttr.localName:''] = newAttr.value\n\t}\n}\n\nfunction _onRemoveAttribute(doc,el,newAttr,remove){\n\tdoc && doc._inc++;\n\tvar ns = newAttr.namespaceURI ;\n\tif(ns === NAMESPACE.XMLNS){\n\t\t//update namespace\n\t\tdelete el._nsMap[newAttr.prefix?newAttr.localName:'']\n\t}\n}\n\n/**\n * Updates `el.childNodes`, updating the indexed items and it's `length`.\n * Passing `newChild` means it will be appended.\n * Otherwise it's assumed that an item has been removed,\n * and `el.firstNode` and it's `.nextSibling` are used\n * to walk the current list of child nodes.\n *\n * @param {Document} doc\n * @param {Node} el\n * @param {Node} [newChild]\n * @private\n */\nfunction _onUpdateChild (doc, el, newChild) {\n\tif(doc && doc._inc){\n\t\tdoc._inc++;\n\t\t//update childNodes\n\t\tvar cs = el.childNodes;\n\t\tif (newChild) {\n\t\t\tcs[cs.length++] = newChild;\n\t\t} else {\n\t\t\tvar child = el.firstChild;\n\t\t\tvar i = 0;\n\t\t\twhile (child) {\n\t\t\t\tcs[i++] = child;\n\t\t\t\tchild = child.nextSibling;\n\t\t\t}\n\t\t\tcs.length = i;\n\t\t\tdelete cs[cs.length];\n\t\t}\n\t}\n}\n\n/**\n * Removes the connections between `parentNode` and `child`\n * and any existing `child.previousSibling` or `child.nextSibling`.\n *\n * @see https://github.com/xmldom/xmldom/issues/135\n * @see https://github.com/xmldom/xmldom/issues/145\n *\n * @param {Node} parentNode\n * @param {Node} child\n * @returns {Node} the child that was removed.\n * @private\n */\nfunction _removeChild (parentNode, child) {\n\tvar previous = child.previousSibling;\n\tvar next = child.nextSibling;\n\tif (previous) {\n\t\tprevious.nextSibling = next;\n\t} else {\n\t\tparentNode.firstChild = next;\n\t}\n\tif (next) {\n\t\tnext.previousSibling = previous;\n\t} else {\n\t\tparentNode.lastChild = previous;\n\t}\n\tchild.parentNode = null;\n\tchild.previousSibling = null;\n\tchild.nextSibling = null;\n\t_onUpdateChild(parentNode.ownerDocument, parentNode);\n\treturn child;\n}\n\n/**\n * Returns `true` if `node` can be a parent for insertion.\n * @param {Node} node\n * @returns {boolean}\n */\nfunction hasValidParentNodeType(node) {\n\treturn (\n\t\tnode &&\n\t\t(node.nodeType === Node.DOCUMENT_NODE || node.nodeType === Node.DOCUMENT_FRAGMENT_NODE || node.nodeType === Node.ELEMENT_NODE)\n\t);\n}\n\n/**\n * Returns `true` if `node` can be inserted according to it's `nodeType`.\n * @param {Node} node\n * @returns {boolean}\n */\nfunction hasInsertableNodeType(node) {\n\treturn (\n\t\tnode &&\n\t\t(isElementNode(node) ||\n\t\t\tisTextNode(node) ||\n\t\t\tisDocTypeNode(node) ||\n\t\t\tnode.nodeType === Node.DOCUMENT_FRAGMENT_NODE ||\n\t\t\tnode.nodeType === Node.COMMENT_NODE ||\n\t\t\tnode.nodeType === Node.PROCESSING_INSTRUCTION_NODE)\n\t);\n}\n\n/**\n * Returns true if `node` is a DOCTYPE node\n * @param {Node} node\n * @returns {boolean}\n */\nfunction isDocTypeNode(node) {\n\treturn node && node.nodeType === Node.DOCUMENT_TYPE_NODE;\n}\n\n/**\n * Returns true if the node is an element\n * @param {Node} node\n * @returns {boolean}\n */\nfunction isElementNode(node) {\n\treturn node && node.nodeType === Node.ELEMENT_NODE;\n}\n/**\n * Returns true if `node` is a text node\n * @param {Node} node\n * @returns {boolean}\n */\nfunction isTextNode(node) {\n\treturn node && node.nodeType === Node.TEXT_NODE;\n}\n\n/**\n * Check if en element node can be inserted before `child`, or at the end if child is falsy,\n * according to the presence and position of a doctype node on the same level.\n *\n * @param {Document} doc The document node\n * @param {Node} child the node that would become the nextSibling if the element would be inserted\n * @returns {boolean} `true` if an element can be inserted before child\n * @private\n * https://dom.spec.whatwg.org/#concept-node-ensure-pre-insertion-validity\n */\nfunction isElementInsertionPossible(doc, child) {\n\tvar parentChildNodes = doc.childNodes || [];\n\tif (find(parentChildNodes, isElementNode) || isDocTypeNode(child)) {\n\t\treturn false;\n\t}\n\tvar docTypeNode = find(parentChildNodes, isDocTypeNode);\n\treturn !(child && docTypeNode && parentChildNodes.indexOf(docTypeNode) > parentChildNodes.indexOf(child));\n}\n\n/**\n * Check if en element node can be inserted before `child`, or at the end if child is falsy,\n * according to the presence and position of a doctype node on the same level.\n *\n * @param {Node} doc The document node\n * @param {Node} child the node that would become the nextSibling if the element would be inserted\n * @returns {boolean} `true` if an element can be inserted before child\n * @private\n * https://dom.spec.whatwg.org/#concept-node-ensure-pre-insertion-validity\n */\nfunction isElementReplacementPossible(doc, child) {\n\tvar parentChildNodes = doc.childNodes || [];\n\n\tfunction hasElementChildThatIsNotChild(node) {\n\t\treturn isElementNode(node) && node !== child;\n\t}\n\n\tif (find(parentChildNodes, hasElementChildThatIsNotChild)) {\n\t\treturn false;\n\t}\n\tvar docTypeNode = find(parentChildNodes, isDocTypeNode);\n\treturn !(child && docTypeNode && parentChildNodes.indexOf(docTypeNode) > parentChildNodes.indexOf(child));\n}\n\n/**\n * @private\n * Steps 1-5 of the checks before inserting and before replacing a child are the same.\n *\n * @param {Node} parent the parent node to insert `node` into\n * @param {Node} node the node to insert\n * @param {Node=} child the node that should become the `nextSibling` of `node`\n * @returns {Node}\n * @throws DOMException for several node combinations that would create a DOM that is not well-formed.\n * @throws DOMException if `child` is provided but is not a child of `parent`.\n * @see https://dom.spec.whatwg.org/#concept-node-ensure-pre-insertion-validity\n * @see https://dom.spec.whatwg.org/#concept-node-replace\n */\nfunction assertPreInsertionValidity1to5(parent, node, child) {\n\t// 1. If `parent` is not a Document, DocumentFragment, or Element node, then throw a \"HierarchyRequestError\" DOMException.\n\tif (!hasValidParentNodeType(parent)) {\n\t\tthrow new DOMException(HIERARCHY_REQUEST_ERR, 'Unexpected parent node type ' + parent.nodeType);\n\t}\n\t// 2. If `node` is a host-including inclusive ancestor of `parent`, then throw a \"HierarchyRequestError\" DOMException.\n\t// not implemented!\n\t// 3. If `child` is non-null and its parent is not `parent`, then throw a \"NotFoundError\" DOMException.\n\tif (child && child.parentNode !== parent) {\n\t\tthrow new DOMException(NOT_FOUND_ERR, 'child not in parent');\n\t}\n\tif (\n\t\t// 4. If `node` is not a DocumentFragment, DocumentType, Element, or CharacterData node, then throw a \"HierarchyRequestError\" DOMException.\n\t\t!hasInsertableNodeType(node) ||\n\t\t// 5. If either `node` is a Text node and `parent` is a document,\n\t\t// the sax parser currently adds top level text nodes, this will be fixed in 0.9.0\n\t\t// || (node.nodeType === Node.TEXT_NODE && parent.nodeType === Node.DOCUMENT_NODE)\n\t\t// or `node` is a doctype and `parent` is not a document, then throw a \"HierarchyRequestError\" DOMException.\n\t\t(isDocTypeNode(node) && parent.nodeType !== Node.DOCUMENT_NODE)\n\t) {\n\t\tthrow new DOMException(\n\t\t\tHIERARCHY_REQUEST_ERR,\n\t\t\t'Unexpected node type ' + node.nodeType + ' for parent node type ' + parent.nodeType\n\t\t);\n\t}\n}\n\n/**\n * @private\n * Step 6 of the checks before inserting and before replacing a child are different.\n *\n * @param {Document} parent the parent node to insert `node` into\n * @param {Node} node the node to insert\n * @param {Node | undefined} child the node that should become the `nextSibling` of `node`\n * @returns {Node}\n * @throws DOMException for several node combinations that would create a DOM that is not well-formed.\n * @throws DOMException if `child` is provided but is not a child of `parent`.\n * @see https://dom.spec.whatwg.org/#concept-node-ensure-pre-insertion-validity\n * @see https://dom.spec.whatwg.org/#concept-node-replace\n */\nfunction assertPreInsertionValidityInDocument(parent, node, child) {\n\tvar parentChildNodes = parent.childNodes || [];\n\tvar nodeChildNodes = node.childNodes || [];\n\n\t// DocumentFragment\n\tif (node.nodeType === Node.DOCUMENT_FRAGMENT_NODE) {\n\t\tvar nodeChildElements = nodeChildNodes.filter(isElementNode);\n\t\t// If node has more than one element child or has a Text node child.\n\t\tif (nodeChildElements.length > 1 || find(nodeChildNodes, isTextNode)) {\n\t\t\tthrow new DOMException(HIERARCHY_REQUEST_ERR, 'More than one element or text in fragment');\n\t\t}\n\t\t// Otherwise, if `node` has one element child and either `parent` has an element child,\n\t\t// `child` is a doctype, or `child` is non-null and a doctype is following `child`.\n\t\tif (nodeChildElements.length === 1 && !isElementInsertionPossible(parent, child)) {\n\t\t\tthrow new DOMException(HIERARCHY_REQUEST_ERR, 'Element in fragment can not be inserted before doctype');\n\t\t}\n\t}\n\t// Element\n\tif (isElementNode(node)) {\n\t\t// `parent` has an element child, `child` is a doctype,\n\t\t// or `child` is non-null and a doctype is following `child`.\n\t\tif (!isElementInsertionPossible(parent, child)) {\n\t\t\tthrow new DOMException(HIERARCHY_REQUEST_ERR, 'Only one element can be added and only after doctype');\n\t\t}\n\t}\n\t// DocumentType\n\tif (isDocTypeNode(node)) {\n\t\t// `parent` has a doctype child,\n\t\tif (find(parentChildNodes, isDocTypeNode)) {\n\t\t\tthrow new DOMException(HIERARCHY_REQUEST_ERR, 'Only one doctype is allowed');\n\t\t}\n\t\tvar parentElementChild = find(parentChildNodes, isElementNode);\n\t\t// `child` is non-null and an element is preceding `child`,\n\t\tif (child && parentChildNodes.indexOf(parentElementChild) < parentChildNodes.indexOf(child)) {\n\t\t\tthrow new DOMException(HIERARCHY_REQUEST_ERR, 'Doctype can only be inserted before an element');\n\t\t}\n\t\t// or `child` is null and `parent` has an element child.\n\t\tif (!child && parentElementChild) {\n\t\t\tthrow new DOMException(HIERARCHY_REQUEST_ERR, 'Doctype can not be appended since element is present');\n\t\t}\n\t}\n}\n\n/**\n * @private\n * Step 6 of the checks before inserting and before replacing a child are different.\n *\n * @param {Document} parent the parent node to insert `node` into\n * @param {Node} node the node to insert\n * @param {Node | undefined} child the node that should become the `nextSibling` of `node`\n * @returns {Node}\n * @throws DOMException for several node combinations that would create a DOM that is not well-formed.\n * @throws DOMException if `child` is provided but is not a child of `parent`.\n * @see https://dom.spec.whatwg.org/#concept-node-ensure-pre-insertion-validity\n * @see https://dom.spec.whatwg.org/#concept-node-replace\n */\nfunction assertPreReplacementValidityInDocument(parent, node, child) {\n\tvar parentChildNodes = parent.childNodes || [];\n\tvar nodeChildNodes = node.childNodes || [];\n\n\t// DocumentFragment\n\tif (node.nodeType === Node.DOCUMENT_FRAGMENT_NODE) {\n\t\tvar nodeChildElements = nodeChildNodes.filter(isElementNode);\n\t\t// If `node` has more than one element child or has a Text node child.\n\t\tif (nodeChildElements.length > 1 || find(nodeChildNodes, isTextNode)) {\n\t\t\tthrow new DOMException(HIERARCHY_REQUEST_ERR, 'More than one element or text in fragment');\n\t\t}\n\t\t// Otherwise, if `node` has one element child and either `parent` has an element child that is not `child` or a doctype is following `child`.\n\t\tif (nodeChildElements.length === 1 && !isElementReplacementPossible(parent, child)) {\n\t\t\tthrow new DOMException(HIERARCHY_REQUEST_ERR, 'Element in fragment can not be inserted before doctype');\n\t\t}\n\t}\n\t// Element\n\tif (isElementNode(node)) {\n\t\t// `parent` has an element child that is not `child` or a doctype is following `child`.\n\t\tif (!isElementReplacementPossible(parent, child)) {\n\t\t\tthrow new DOMException(HIERARCHY_REQUEST_ERR, 'Only one element can be added and only after doctype');\n\t\t}\n\t}\n\t// DocumentType\n\tif (isDocTypeNode(node)) {\n\t\tfunction hasDoctypeChildThatIsNotChild(node) {\n\t\t\treturn isDocTypeNode(node) && node !== child;\n\t\t}\n\n\t\t// `parent` has a doctype child that is not `child`,\n\t\tif (find(parentChildNodes, hasDoctypeChildThatIsNotChild)) {\n\t\t\tthrow new DOMException(HIERARCHY_REQUEST_ERR, 'Only one doctype is allowed');\n\t\t}\n\t\tvar parentElementChild = find(parentChildNodes, isElementNode);\n\t\t// or an element is preceding `child`.\n\t\tif (child && parentChildNodes.indexOf(parentElementChild) < parentChildNodes.indexOf(child)) {\n\t\t\tthrow new DOMException(HIERARCHY_REQUEST_ERR, 'Doctype can only be inserted before an element');\n\t\t}\n\t}\n}\n\n/**\n * @private\n * @param {Node} parent the parent node to insert `node` into\n * @param {Node} node the node to insert\n * @param {Node=} child the node that should become the `nextSibling` of `node`\n * @returns {Node}\n * @throws DOMException for several node combinations that would create a DOM that is not well-formed.\n * @throws DOMException if `child` is provided but is not a child of `parent`.\n * @see https://dom.spec.whatwg.org/#concept-node-ensure-pre-insertion-validity\n */\nfunction _insertBefore(parent, node, child, _inDocumentAssertion) {\n\t// To ensure pre-insertion validity of a node into a parent before a child, run these steps:\n\tassertPreInsertionValidity1to5(parent, node, child);\n\n\t// If parent is a document, and any of the statements below, switched on the interface node implements,\n\t// are true, then throw a \"HierarchyRequestError\" DOMException.\n\tif (parent.nodeType === Node.DOCUMENT_NODE) {\n\t\t(_inDocumentAssertion || assertPreInsertionValidityInDocument)(parent, node, child);\n\t}\n\n\tvar cp = node.parentNode;\n\tif(cp){\n\t\tcp.removeChild(node);//remove and update\n\t}\n\tif(node.nodeType === DOCUMENT_FRAGMENT_NODE){\n\t\tvar newFirst = node.firstChild;\n\t\tif (newFirst == null) {\n\t\t\treturn node;\n\t\t}\n\t\tvar newLast = node.lastChild;\n\t}else{\n\t\tnewFirst = newLast = node;\n\t}\n\tvar pre = child ? child.previousSibling : parent.lastChild;\n\n\tnewFirst.previousSibling = pre;\n\tnewLast.nextSibling = child;\n\n\n\tif(pre){\n\t\tpre.nextSibling = newFirst;\n\t}else{\n\t\tparent.firstChild = newFirst;\n\t}\n\tif(child == null){\n\t\tparent.lastChild = newLast;\n\t}else{\n\t\tchild.previousSibling = newLast;\n\t}\n\tdo{\n\t\tnewFirst.parentNode = parent;\n\t}while(newFirst !== newLast && (newFirst= newFirst.nextSibling))\n\t_onUpdateChild(parent.ownerDocument||parent, parent);\n\t//console.log(parent.lastChild.nextSibling == null)\n\tif (node.nodeType == DOCUMENT_FRAGMENT_NODE) {\n\t\tnode.firstChild = node.lastChild = null;\n\t}\n\treturn node;\n}\n\n/**\n * Appends `newChild` to `parentNode`.\n * If `newChild` is already connected to a `parentNode` it is first removed from it.\n *\n * @see https://github.com/xmldom/xmldom/issues/135\n * @see https://github.com/xmldom/xmldom/issues/145\n * @param {Node} parentNode\n * @param {Node} newChild\n * @returns {Node}\n * @private\n */\nfunction _appendSingleChild (parentNode, newChild) {\n\tif (newChild.parentNode) {\n\t\tnewChild.parentNode.removeChild(newChild);\n\t}\n\tnewChild.parentNode = parentNode;\n\tnewChild.previousSibling = parentNode.lastChild;\n\tnewChild.nextSibling = null;\n\tif (newChild.previousSibling) {\n\t\tnewChild.previousSibling.nextSibling = newChild;\n\t} else {\n\t\tparentNode.firstChild = newChild;\n\t}\n\tparentNode.lastChild = newChild;\n\t_onUpdateChild(parentNode.ownerDocument, parentNode, newChild);\n\treturn newChild;\n}\n\nDocument.prototype = {\n\t//implementation : null,\n\tnodeName :  '#document',\n\tnodeType :  DOCUMENT_NODE,\n\t/**\n\t * The DocumentType node of the document.\n\t *\n\t * @readonly\n\t * @type DocumentType\n\t */\n\tdoctype :  null,\n\tdocumentElement :  null,\n\t_inc : 1,\n\n\tinsertBefore :  function(newChild, refChild){//raises\n\t\tif(newChild.nodeType == DOCUMENT_FRAGMENT_NODE){\n\t\t\tvar child = newChild.firstChild;\n\t\t\twhile(child){\n\t\t\t\tvar next = child.nextSibling;\n\t\t\t\tthis.insertBefore(child,refChild);\n\t\t\t\tchild = next;\n\t\t\t}\n\t\t\treturn newChild;\n\t\t}\n\t\t_insertBefore(this, newChild, refChild);\n\t\tnewChild.ownerDocument = this;\n\t\tif (this.documentElement === null && newChild.nodeType === ELEMENT_NODE) {\n\t\t\tthis.documentElement = newChild;\n\t\t}\n\n\t\treturn newChild;\n\t},\n\tremoveChild :  function(oldChild){\n\t\tif(this.documentElement == oldChild){\n\t\t\tthis.documentElement = null;\n\t\t}\n\t\treturn _removeChild(this,oldChild);\n\t},\n\treplaceChild: function (newChild, oldChild) {\n\t\t//raises\n\t\t_insertBefore(this, newChild, oldChild, assertPreReplacementValidityInDocument);\n\t\tnewChild.ownerDocument = this;\n\t\tif (oldChild) {\n\t\t\tthis.removeChild(oldChild);\n\t\t}\n\t\tif (isElementNode(newChild)) {\n\t\t\tthis.documentElement = newChild;\n\t\t}\n\t},\n\t// Introduced in DOM Level 2:\n\timportNode : function(importedNode,deep){\n\t\treturn importNode(this,importedNode,deep);\n\t},\n\t// Introduced in DOM Level 2:\n\tgetElementById :\tfunction(id){\n\t\tvar rtv = null;\n\t\t_visitNode(this.documentElement,function(node){\n\t\t\tif(node.nodeType == ELEMENT_NODE){\n\t\t\t\tif(node.getAttribute('id') == id){\n\t\t\t\t\trtv = node;\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t}\n\t\t})\n\t\treturn rtv;\n\t},\n\n\t/**\n\t * The `getElementsByClassName` method of `Document` interface returns an array-like object\n\t * of all child elements which have **all** of the given class name(s).\n\t *\n\t * Returns an empty list if `classeNames` is an empty string or only contains HTML white space characters.\n\t *\n\t *\n\t * Warning: This is a live LiveNodeList.\n\t * Changes in the DOM will reflect in the array as the changes occur.\n\t * If an element selected by this array no longer qualifies for the selector,\n\t * it will automatically be removed. Be aware of this for iteration purposes.\n\t *\n\t * @param {string} classNames is a string representing the class name(s) to match; multiple class names are separated by (ASCII-)whitespace\n\t *\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/Document/getElementsByClassName\n\t * @see https://dom.spec.whatwg.org/#concept-getelementsbyclassname\n\t */\n\tgetElementsByClassName: function(classNames) {\n\t\tvar classNamesSet = toOrderedSet(classNames)\n\t\treturn new LiveNodeList(this, function(base) {\n\t\t\tvar ls = [];\n\t\t\tif (classNamesSet.length > 0) {\n\t\t\t\t_visitNode(base.documentElement, function(node) {\n\t\t\t\t\tif(node !== base && node.nodeType === ELEMENT_NODE) {\n\t\t\t\t\t\tvar nodeClassNames = node.getAttribute('class')\n\t\t\t\t\t\t// can be null if the attribute does not exist\n\t\t\t\t\t\tif (nodeClassNames) {\n\t\t\t\t\t\t\t// before splitting and iterating just compare them for the most common case\n\t\t\t\t\t\t\tvar matches = classNames === nodeClassNames;\n\t\t\t\t\t\t\tif (!matches) {\n\t\t\t\t\t\t\t\tvar nodeClassNamesSet = toOrderedSet(nodeClassNames)\n\t\t\t\t\t\t\t\tmatches = classNamesSet.every(arrayIncludes(nodeClassNamesSet))\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif(matches) {\n\t\t\t\t\t\t\t\tls.push(node);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t\treturn ls;\n\t\t});\n\t},\n\n\t//document factory method:\n\tcreateElement :\tfunction(tagName){\n\t\tvar node = new Element();\n\t\tnode.ownerDocument = this;\n\t\tnode.nodeName = tagName;\n\t\tnode.tagName = tagName;\n\t\tnode.localName = tagName;\n\t\tnode.childNodes = new NodeList();\n\t\tvar attrs\t= node.attributes = new NamedNodeMap();\n\t\tattrs._ownerElement = node;\n\t\treturn node;\n\t},\n\tcreateDocumentFragment :\tfunction(){\n\t\tvar node = new DocumentFragment();\n\t\tnode.ownerDocument = this;\n\t\tnode.childNodes = new NodeList();\n\t\treturn node;\n\t},\n\tcreateTextNode :\tfunction(data){\n\t\tvar node = new Text();\n\t\tnode.ownerDocument = this;\n\t\tnode.appendData(data)\n\t\treturn node;\n\t},\n\tcreateComment :\tfunction(data){\n\t\tvar node = new Comment();\n\t\tnode.ownerDocument = this;\n\t\tnode.appendData(data)\n\t\treturn node;\n\t},\n\tcreateCDATASection :\tfunction(data){\n\t\tvar node = new CDATASection();\n\t\tnode.ownerDocument = this;\n\t\tnode.appendData(data)\n\t\treturn node;\n\t},\n\tcreateProcessingInstruction :\tfunction(target,data){\n\t\tvar node = new ProcessingInstruction();\n\t\tnode.ownerDocument = this;\n\t\tnode.tagName = node.nodeName = node.target = target;\n\t\tnode.nodeValue = node.data = data;\n\t\treturn node;\n\t},\n\tcreateAttribute :\tfunction(name){\n\t\tvar node = new Attr();\n\t\tnode.ownerDocument\t= this;\n\t\tnode.name = name;\n\t\tnode.nodeName\t= name;\n\t\tnode.localName = name;\n\t\tnode.specified = true;\n\t\treturn node;\n\t},\n\tcreateEntityReference :\tfunction(name){\n\t\tvar node = new EntityReference();\n\t\tnode.ownerDocument\t= this;\n\t\tnode.nodeName\t= name;\n\t\treturn node;\n\t},\n\t// Introduced in DOM Level 2:\n\tcreateElementNS :\tfunction(namespaceURI,qualifiedName){\n\t\tvar node = new Element();\n\t\tvar pl = qualifiedName.split(':');\n\t\tvar attrs\t= node.attributes = new NamedNodeMap();\n\t\tnode.childNodes = new NodeList();\n\t\tnode.ownerDocument = this;\n\t\tnode.nodeName = qualifiedName;\n\t\tnode.tagName = qualifiedName;\n\t\tnode.namespaceURI = namespaceURI;\n\t\tif(pl.length == 2){\n\t\t\tnode.prefix = pl[0];\n\t\t\tnode.localName = pl[1];\n\t\t}else{\n\t\t\t//el.prefix = null;\n\t\t\tnode.localName = qualifiedName;\n\t\t}\n\t\tattrs._ownerElement = node;\n\t\treturn node;\n\t},\n\t// Introduced in DOM Level 2:\n\tcreateAttributeNS :\tfunction(namespaceURI,qualifiedName){\n\t\tvar node = new Attr();\n\t\tvar pl = qualifiedName.split(':');\n\t\tnode.ownerDocument = this;\n\t\tnode.nodeName = qualifiedName;\n\t\tnode.name = qualifiedName;\n\t\tnode.namespaceURI = namespaceURI;\n\t\tnode.specified = true;\n\t\tif(pl.length == 2){\n\t\t\tnode.prefix = pl[0];\n\t\t\tnode.localName = pl[1];\n\t\t}else{\n\t\t\t//el.prefix = null;\n\t\t\tnode.localName = qualifiedName;\n\t\t}\n\t\treturn node;\n\t}\n};\n_extends(Document,Node);\n\n\nfunction Element() {\n\tthis._nsMap = {};\n};\nElement.prototype = {\n\tnodeType : ELEMENT_NODE,\n\thasAttribute : function(name){\n\t\treturn this.getAttributeNode(name)!=null;\n\t},\n\tgetAttribute : function(name){\n\t\tvar attr = this.getAttributeNode(name);\n\t\treturn attr && attr.value || '';\n\t},\n\tgetAttributeNode : function(name){\n\t\treturn this.attributes.getNamedItem(name);\n\t},\n\tsetAttribute : function(name, value){\n\t\tvar attr = this.ownerDocument.createAttribute(name);\n\t\tattr.value = attr.nodeValue = \"\" + value;\n\t\tthis.setAttributeNode(attr)\n\t},\n\tremoveAttribute : function(name){\n\t\tvar attr = this.getAttributeNode(name)\n\t\tattr && this.removeAttributeNode(attr);\n\t},\n\n\t//four real opeartion method\n\tappendChild:function(newChild){\n\t\tif(newChild.nodeType === DOCUMENT_FRAGMENT_NODE){\n\t\t\treturn this.insertBefore(newChild,null);\n\t\t}else{\n\t\t\treturn _appendSingleChild(this,newChild);\n\t\t}\n\t},\n\tsetAttributeNode : function(newAttr){\n\t\treturn this.attributes.setNamedItem(newAttr);\n\t},\n\tsetAttributeNodeNS : function(newAttr){\n\t\treturn this.attributes.setNamedItemNS(newAttr);\n\t},\n\tremoveAttributeNode : function(oldAttr){\n\t\t//console.log(this == oldAttr.ownerElement)\n\t\treturn this.attributes.removeNamedItem(oldAttr.nodeName);\n\t},\n\t//get real attribute name,and remove it by removeAttributeNode\n\tremoveAttributeNS : function(namespaceURI, localName){\n\t\tvar old = this.getAttributeNodeNS(namespaceURI, localName);\n\t\told && this.removeAttributeNode(old);\n\t},\n\n\thasAttributeNS : function(namespaceURI, localName){\n\t\treturn this.getAttributeNodeNS(namespaceURI, localName)!=null;\n\t},\n\tgetAttributeNS : function(namespaceURI, localName){\n\t\tvar attr = this.getAttributeNodeNS(namespaceURI, localName);\n\t\treturn attr && attr.value || '';\n\t},\n\tsetAttributeNS : function(namespaceURI, qualifiedName, value){\n\t\tvar attr = this.ownerDocument.createAttributeNS(namespaceURI, qualifiedName);\n\t\tattr.value = attr.nodeValue = \"\" + value;\n\t\tthis.setAttributeNode(attr)\n\t},\n\tgetAttributeNodeNS : function(namespaceURI, localName){\n\t\treturn this.attributes.getNamedItemNS(namespaceURI, localName);\n\t},\n\n\tgetElementsByTagName : function(tagName){\n\t\treturn new LiveNodeList(this,function(base){\n\t\t\tvar ls = [];\n\t\t\t_visitNode(base,function(node){\n\t\t\t\tif(node !== base && node.nodeType == ELEMENT_NODE && (tagName === '*' || node.tagName == tagName)){\n\t\t\t\t\tls.push(node);\n\t\t\t\t}\n\t\t\t});\n\t\t\treturn ls;\n\t\t});\n\t},\n\tgetElementsByTagNameNS : function(namespaceURI, localName){\n\t\treturn new LiveNodeList(this,function(base){\n\t\t\tvar ls = [];\n\t\t\t_visitNode(base,function(node){\n\t\t\t\tif(node !== base && node.nodeType === ELEMENT_NODE && (namespaceURI === '*' || node.namespaceURI === namespaceURI) && (localName === '*' || node.localName == localName)){\n\t\t\t\t\tls.push(node);\n\t\t\t\t}\n\t\t\t});\n\t\t\treturn ls;\n\n\t\t});\n\t}\n};\nDocument.prototype.getElementsByTagName = Element.prototype.getElementsByTagName;\nDocument.prototype.getElementsByTagNameNS = Element.prototype.getElementsByTagNameNS;\n\n\n_extends(Element,Node);\nfunction Attr() {\n};\nAttr.prototype.nodeType = ATTRIBUTE_NODE;\n_extends(Attr,Node);\n\n\nfunction CharacterData() {\n};\nCharacterData.prototype = {\n\tdata : '',\n\tsubstringData : function(offset, count) {\n\t\treturn this.data.substring(offset, offset+count);\n\t},\n\tappendData: function(text) {\n\t\ttext = this.data+text;\n\t\tthis.nodeValue = this.data = text;\n\t\tthis.length = text.length;\n\t},\n\tinsertData: function(offset,text) {\n\t\tthis.replaceData(offset,0,text);\n\n\t},\n\tappendChild:function(newChild){\n\t\tthrow new Error(ExceptionMessage[HIERARCHY_REQUEST_ERR])\n\t},\n\tdeleteData: function(offset, count) {\n\t\tthis.replaceData(offset,count,\"\");\n\t},\n\treplaceData: function(offset, count, text) {\n\t\tvar start = this.data.substring(0,offset);\n\t\tvar end = this.data.substring(offset+count);\n\t\ttext = start + text + end;\n\t\tthis.nodeValue = this.data = text;\n\t\tthis.length = text.length;\n\t}\n}\n_extends(CharacterData,Node);\nfunction Text() {\n};\nText.prototype = {\n\tnodeName : \"#text\",\n\tnodeType : TEXT_NODE,\n\tsplitText : function(offset) {\n\t\tvar text = this.data;\n\t\tvar newText = text.substring(offset);\n\t\ttext = text.substring(0, offset);\n\t\tthis.data = this.nodeValue = text;\n\t\tthis.length = text.length;\n\t\tvar newNode = this.ownerDocument.createTextNode(newText);\n\t\tif(this.parentNode){\n\t\t\tthis.parentNode.insertBefore(newNode, this.nextSibling);\n\t\t}\n\t\treturn newNode;\n\t}\n}\n_extends(Text,CharacterData);\nfunction Comment() {\n};\nComment.prototype = {\n\tnodeName : \"#comment\",\n\tnodeType : COMMENT_NODE\n}\n_extends(Comment,CharacterData);\n\nfunction CDATASection() {\n};\nCDATASection.prototype = {\n\tnodeName : \"#cdata-section\",\n\tnodeType : CDATA_SECTION_NODE\n}\n_extends(CDATASection,CharacterData);\n\n\nfunction DocumentType() {\n};\nDocumentType.prototype.nodeType = DOCUMENT_TYPE_NODE;\n_extends(DocumentType,Node);\n\nfunction Notation() {\n};\nNotation.prototype.nodeType = NOTATION_NODE;\n_extends(Notation,Node);\n\nfunction Entity() {\n};\nEntity.prototype.nodeType = ENTITY_NODE;\n_extends(Entity,Node);\n\nfunction EntityReference() {\n};\nEntityReference.prototype.nodeType = ENTITY_REFERENCE_NODE;\n_extends(EntityReference,Node);\n\nfunction DocumentFragment() {\n};\nDocumentFragment.prototype.nodeName =\t\"#document-fragment\";\nDocumentFragment.prototype.nodeType =\tDOCUMENT_FRAGMENT_NODE;\n_extends(DocumentFragment,Node);\n\n\nfunction ProcessingInstruction() {\n}\nProcessingInstruction.prototype.nodeType = PROCESSING_INSTRUCTION_NODE;\n_extends(ProcessingInstruction,Node);\nfunction XMLSerializer(){}\nXMLSerializer.prototype.serializeToString = function(node,isHtml,nodeFilter){\n\treturn nodeSerializeToString.call(node,isHtml,nodeFilter);\n}\nNode.prototype.toString = nodeSerializeToString;\nfunction nodeSerializeToString(isHtml,nodeFilter){\n\tvar buf = [];\n\tvar refNode = this.nodeType == 9 && this.documentElement || this;\n\tvar prefix = refNode.prefix;\n\tvar uri = refNode.namespaceURI;\n\n\tif(uri && prefix == null){\n\t\t//console.log(prefix)\n\t\tvar prefix = refNode.lookupPrefix(uri);\n\t\tif(prefix == null){\n\t\t\t//isHTML = true;\n\t\t\tvar visibleNamespaces=[\n\t\t\t{namespace:uri,prefix:null}\n\t\t\t//{namespace:uri,prefix:''}\n\t\t\t]\n\t\t}\n\t}\n\tserializeToString(this,buf,isHtml,nodeFilter,visibleNamespaces);\n\t//console.log('###',this.nodeType,uri,prefix,buf.join(''))\n\treturn buf.join('');\n}\n\nfunction needNamespaceDefine(node, isHTML, visibleNamespaces) {\n\tvar prefix = node.prefix || '';\n\tvar uri = node.namespaceURI;\n\t// According to [Namespaces in XML 1.0](https://www.w3.org/TR/REC-xml-names/#ns-using) ,\n\t// and more specifically https://www.w3.org/TR/REC-xml-names/#nsc-NoPrefixUndecl :\n\t// > In a namespace declaration for a prefix [...], the attribute value MUST NOT be empty.\n\t// in a similar manner [Namespaces in XML 1.1](https://www.w3.org/TR/xml-names11/#ns-using)\n\t// and more specifically https://www.w3.org/TR/xml-names11/#nsc-NSDeclared :\n\t// > [...] Furthermore, the attribute value [...] must not be an empty string.\n\t// so serializing empty namespace value like xmlns:ds=\"\" would produce an invalid XML document.\n\tif (!uri) {\n\t\treturn false;\n\t}\n\tif (prefix === \"xml\" && uri === NAMESPACE.XML || uri === NAMESPACE.XMLNS) {\n\t\treturn false;\n\t}\n\n\tvar i = visibleNamespaces.length\n\twhile (i--) {\n\t\tvar ns = visibleNamespaces[i];\n\t\t// get namespace prefix\n\t\tif (ns.prefix === prefix) {\n\t\t\treturn ns.namespace !== uri;\n\t\t}\n\t}\n\treturn true;\n}\n/**\n * Well-formed constraint: No < in Attribute Values\n * > The replacement text of any entity referred to directly or indirectly\n * > in an attribute value must not contain a <.\n * @see https://www.w3.org/TR/xml11/#CleanAttrVals\n * @see https://www.w3.org/TR/xml11/#NT-AttValue\n *\n * Literal whitespace other than space that appear in attribute values\n * are serialized as their entity references, so they will be preserved.\n * (In contrast to whitespace literals in the input which are normalized to spaces)\n * @see https://www.w3.org/TR/xml11/#AVNormalize\n * @see https://w3c.github.io/DOM-Parsing/#serializing-an-element-s-attributes\n */\nfunction addSerializedAttribute(buf, qualifiedName, value) {\n\tbuf.push(' ', qualifiedName, '=\"', value.replace(/[<>&\"\\t\\n\\r]/g, _xmlEncoder), '\"')\n}\n\nfunction serializeToString(node,buf,isHTML,nodeFilter,visibleNamespaces){\n\tif (!visibleNamespaces) {\n\t\tvisibleNamespaces = [];\n\t}\n\n\tif(nodeFilter){\n\t\tnode = nodeFilter(node);\n\t\tif(node){\n\t\t\tif(typeof node == 'string'){\n\t\t\t\tbuf.push(node);\n\t\t\t\treturn;\n\t\t\t}\n\t\t}else{\n\t\t\treturn;\n\t\t}\n\t\t//buf.sort.apply(attrs, attributeSorter);\n\t}\n\n\tswitch(node.nodeType){\n\tcase ELEMENT_NODE:\n\t\tvar attrs = node.attributes;\n\t\tvar len = attrs.length;\n\t\tvar child = node.firstChild;\n\t\tvar nodeName = node.tagName;\n\n\t\tisHTML = NAMESPACE.isHTML(node.namespaceURI) || isHTML\n\n\t\tvar prefixedNodeName = nodeName\n\t\tif (!isHTML && !node.prefix && node.namespaceURI) {\n\t\t\tvar defaultNS\n\t\t\t// lookup current default ns from `xmlns` attribute\n\t\t\tfor (var ai = 0; ai < attrs.length; ai++) {\n\t\t\t\tif (attrs.item(ai).name === 'xmlns') {\n\t\t\t\t\tdefaultNS = attrs.item(ai).value\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (!defaultNS) {\n\t\t\t\t// lookup current default ns in visibleNamespaces\n\t\t\t\tfor (var nsi = visibleNamespaces.length - 1; nsi >= 0; nsi--) {\n\t\t\t\t\tvar namespace = visibleNamespaces[nsi]\n\t\t\t\t\tif (namespace.prefix === '' && namespace.namespace === node.namespaceURI) {\n\t\t\t\t\t\tdefaultNS = namespace.namespace\n\t\t\t\t\t\tbreak\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (defaultNS !== node.namespaceURI) {\n\t\t\t\tfor (var nsi = visibleNamespaces.length - 1; nsi >= 0; nsi--) {\n\t\t\t\t\tvar namespace = visibleNamespaces[nsi]\n\t\t\t\t\tif (namespace.namespace === node.namespaceURI) {\n\t\t\t\t\t\tif (namespace.prefix) {\n\t\t\t\t\t\t\tprefixedNodeName = namespace.prefix + ':' + nodeName\n\t\t\t\t\t\t}\n\t\t\t\t\t\tbreak\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tbuf.push('<', prefixedNodeName);\n\n\t\tfor(var i=0;i<len;i++){\n\t\t\t// add namespaces for attributes\n\t\t\tvar attr = attrs.item(i);\n\t\t\tif (attr.prefix == 'xmlns') {\n\t\t\t\tvisibleNamespaces.push({ prefix: attr.localName, namespace: attr.value });\n\t\t\t}else if(attr.nodeName == 'xmlns'){\n\t\t\t\tvisibleNamespaces.push({ prefix: '', namespace: attr.value });\n\t\t\t}\n\t\t}\n\n\t\tfor(var i=0;i<len;i++){\n\t\t\tvar attr = attrs.item(i);\n\t\t\tif (needNamespaceDefine(attr,isHTML, visibleNamespaces)) {\n\t\t\t\tvar prefix = attr.prefix||'';\n\t\t\t\tvar uri = attr.namespaceURI;\n\t\t\t\taddSerializedAttribute(buf, prefix ? 'xmlns:' + prefix : \"xmlns\", uri);\n\t\t\t\tvisibleNamespaces.push({ prefix: prefix, namespace:uri });\n\t\t\t}\n\t\t\tserializeToString(attr,buf,isHTML,nodeFilter,visibleNamespaces);\n\t\t}\n\n\t\t// add namespace for current node\n\t\tif (nodeName === prefixedNodeName && needNamespaceDefine(node, isHTML, visibleNamespaces)) {\n\t\t\tvar prefix = node.prefix||'';\n\t\t\tvar uri = node.namespaceURI;\n\t\t\taddSerializedAttribute(buf, prefix ? 'xmlns:' + prefix : \"xmlns\", uri);\n\t\t\tvisibleNamespaces.push({ prefix: prefix, namespace:uri });\n\t\t}\n\n\t\tif(child || isHTML && !/^(?:meta|link|img|br|hr|input)$/i.test(nodeName)){\n\t\t\tbuf.push('>');\n\t\t\t//if is cdata child node\n\t\t\tif(isHTML && /^script$/i.test(nodeName)){\n\t\t\t\twhile(child){\n\t\t\t\t\tif(child.data){\n\t\t\t\t\t\tbuf.push(child.data);\n\t\t\t\t\t}else{\n\t\t\t\t\t\tserializeToString(child, buf, isHTML, nodeFilter, visibleNamespaces.slice());\n\t\t\t\t\t}\n\t\t\t\t\tchild = child.nextSibling;\n\t\t\t\t}\n\t\t\t}else\n\t\t\t{\n\t\t\t\twhile(child){\n\t\t\t\t\tserializeToString(child, buf, isHTML, nodeFilter, visibleNamespaces.slice());\n\t\t\t\t\tchild = child.nextSibling;\n\t\t\t\t}\n\t\t\t}\n\t\t\tbuf.push('</',prefixedNodeName,'>');\n\t\t}else{\n\t\t\tbuf.push('/>');\n\t\t}\n\t\t// remove added visible namespaces\n\t\t//visibleNamespaces.length = startVisibleNamespaces;\n\t\treturn;\n\tcase DOCUMENT_NODE:\n\tcase DOCUMENT_FRAGMENT_NODE:\n\t\tvar child = node.firstChild;\n\t\twhile(child){\n\t\t\tserializeToString(child, buf, isHTML, nodeFilter, visibleNamespaces.slice());\n\t\t\tchild = child.nextSibling;\n\t\t}\n\t\treturn;\n\tcase ATTRIBUTE_NODE:\n\t\treturn addSerializedAttribute(buf, node.name, node.value);\n\tcase TEXT_NODE:\n\t\t/**\n\t\t * The ampersand character (&) and the left angle bracket (<) must not appear in their literal form,\n\t\t * except when used as markup delimiters, or within a comment, a processing instruction, or a CDATA section.\n\t\t * If they are needed elsewhere, they must be escaped using either numeric character references or the strings\n\t\t * `&amp;` and `&lt;` respectively.\n\t\t * The right angle bracket (>) may be represented using the string \" &gt; \", and must, for compatibility,\n\t\t * be escaped using either `&gt;` or a character reference when it appears in the string `]]>` in content,\n\t\t * when that string is not marking the end of a CDATA section.\n\t\t *\n\t\t * In the content of elements, character data is any string of characters\n\t\t * which does not contain the start-delimiter of any markup\n\t\t * and does not include the CDATA-section-close delimiter, `]]>`.\n\t\t *\n\t\t * @see https://www.w3.org/TR/xml/#NT-CharData\n\t\t * @see https://w3c.github.io/DOM-Parsing/#xml-serializing-a-text-node\n\t\t */\n\t\treturn buf.push(node.data\n\t\t\t.replace(/[<&>]/g,_xmlEncoder)\n\t\t);\n\tcase CDATA_SECTION_NODE:\n\t\treturn buf.push( '<![CDATA[',node.data,']]>');\n\tcase COMMENT_NODE:\n\t\treturn buf.push( \"<!--\",node.data,\"-->\");\n\tcase DOCUMENT_TYPE_NODE:\n\t\tvar pubid = node.publicId;\n\t\tvar sysid = node.systemId;\n\t\tbuf.push('<!DOCTYPE ',node.name);\n\t\tif(pubid){\n\t\t\tbuf.push(' PUBLIC ', pubid);\n\t\t\tif (sysid && sysid!='.') {\n\t\t\t\tbuf.push(' ', sysid);\n\t\t\t}\n\t\t\tbuf.push('>');\n\t\t}else if(sysid && sysid!='.'){\n\t\t\tbuf.push(' SYSTEM ', sysid, '>');\n\t\t}else{\n\t\t\tvar sub = node.internalSubset;\n\t\t\tif(sub){\n\t\t\t\tbuf.push(\" [\",sub,\"]\");\n\t\t\t}\n\t\t\tbuf.push(\">\");\n\t\t}\n\t\treturn;\n\tcase PROCESSING_INSTRUCTION_NODE:\n\t\treturn buf.push( \"<?\",node.target,\" \",node.data,\"?>\");\n\tcase ENTITY_REFERENCE_NODE:\n\t\treturn buf.push( '&',node.nodeName,';');\n\t//case ENTITY_NODE:\n\t//case NOTATION_NODE:\n\tdefault:\n\t\tbuf.push('??',node.nodeName);\n\t}\n}\nfunction importNode(doc,node,deep){\n\tvar node2;\n\tswitch (node.nodeType) {\n\tcase ELEMENT_NODE:\n\t\tnode2 = node.cloneNode(false);\n\t\tnode2.ownerDocument = doc;\n\t\t//var attrs = node2.attributes;\n\t\t//var len = attrs.length;\n\t\t//for(var i=0;i<len;i++){\n\t\t\t//node2.setAttributeNodeNS(importNode(doc,attrs.item(i),deep));\n\t\t//}\n\tcase DOCUMENT_FRAGMENT_NODE:\n\t\tbreak;\n\tcase ATTRIBUTE_NODE:\n\t\tdeep = true;\n\t\tbreak;\n\t//case ENTITY_REFERENCE_NODE:\n\t//case PROCESSING_INSTRUCTION_NODE:\n\t////case TEXT_NODE:\n\t//case CDATA_SECTION_NODE:\n\t//case COMMENT_NODE:\n\t//\tdeep = false;\n\t//\tbreak;\n\t//case DOCUMENT_NODE:\n\t//case DOCUMENT_TYPE_NODE:\n\t//cannot be imported.\n\t//case ENTITY_NODE:\n\t//case NOTATION_NODE：\n\t//can not hit in level3\n\t//default:throw e;\n\t}\n\tif(!node2){\n\t\tnode2 = node.cloneNode(false);//false\n\t}\n\tnode2.ownerDocument = doc;\n\tnode2.parentNode = null;\n\tif(deep){\n\t\tvar child = node.firstChild;\n\t\twhile(child){\n\t\t\tnode2.appendChild(importNode(doc,child,deep));\n\t\t\tchild = child.nextSibling;\n\t\t}\n\t}\n\treturn node2;\n}\n//\n//var _relationMap = {firstChild:1,lastChild:1,previousSibling:1,nextSibling:1,\n//\t\t\t\t\tattributes:1,childNodes:1,parentNode:1,documentElement:1,doctype,};\nfunction cloneNode(doc,node,deep){\n\tvar node2 = new node.constructor();\n\tfor (var n in node) {\n\t\tif (Object.prototype.hasOwnProperty.call(node, n)) {\n\t\t\tvar v = node[n];\n\t\t\tif (typeof v != \"object\") {\n\t\t\t\tif (v != node2[n]) {\n\t\t\t\t\tnode2[n] = v;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\tif(node.childNodes){\n\t\tnode2.childNodes = new NodeList();\n\t}\n\tnode2.ownerDocument = doc;\n\tswitch (node2.nodeType) {\n\tcase ELEMENT_NODE:\n\t\tvar attrs\t= node.attributes;\n\t\tvar attrs2\t= node2.attributes = new NamedNodeMap();\n\t\tvar len = attrs.length\n\t\tattrs2._ownerElement = node2;\n\t\tfor(var i=0;i<len;i++){\n\t\t\tnode2.setAttributeNode(cloneNode(doc,attrs.item(i),true));\n\t\t}\n\t\tbreak;;\n\tcase ATTRIBUTE_NODE:\n\t\tdeep = true;\n\t}\n\tif(deep){\n\t\tvar child = node.firstChild;\n\t\twhile(child){\n\t\t\tnode2.appendChild(cloneNode(doc,child,deep));\n\t\t\tchild = child.nextSibling;\n\t\t}\n\t}\n\treturn node2;\n}\n\nfunction __set__(object,key,value){\n\tobject[key] = value\n}\n//do dynamic\ntry{\n\tif(Object.defineProperty){\n\t\tObject.defineProperty(LiveNodeList.prototype,'length',{\n\t\t\tget:function(){\n\t\t\t\t_updateLiveList(this);\n\t\t\t\treturn this.$$length;\n\t\t\t}\n\t\t});\n\n\t\tObject.defineProperty(Node.prototype,'textContent',{\n\t\t\tget:function(){\n\t\t\t\treturn getTextContent(this);\n\t\t\t},\n\n\t\t\tset:function(data){\n\t\t\t\tswitch(this.nodeType){\n\t\t\t\tcase ELEMENT_NODE:\n\t\t\t\tcase DOCUMENT_FRAGMENT_NODE:\n\t\t\t\t\twhile(this.firstChild){\n\t\t\t\t\t\tthis.removeChild(this.firstChild);\n\t\t\t\t\t}\n\t\t\t\t\tif(data || String(data)){\n\t\t\t\t\t\tthis.appendChild(this.ownerDocument.createTextNode(data));\n\t\t\t\t\t}\n\t\t\t\t\tbreak;\n\n\t\t\t\tdefault:\n\t\t\t\t\tthis.data = data;\n\t\t\t\t\tthis.value = data;\n\t\t\t\t\tthis.nodeValue = data;\n\t\t\t\t}\n\t\t\t}\n\t\t})\n\n\t\tfunction getTextContent(node){\n\t\t\tswitch(node.nodeType){\n\t\t\tcase ELEMENT_NODE:\n\t\t\tcase DOCUMENT_FRAGMENT_NODE:\n\t\t\t\tvar buf = [];\n\t\t\t\tnode = node.firstChild;\n\t\t\t\twhile(node){\n\t\t\t\t\tif(node.nodeType!==7 && node.nodeType !==8){\n\t\t\t\t\t\tbuf.push(getTextContent(node));\n\t\t\t\t\t}\n\t\t\t\t\tnode = node.nextSibling;\n\t\t\t\t}\n\t\t\t\treturn buf.join('');\n\t\t\tdefault:\n\t\t\t\treturn node.nodeValue;\n\t\t\t}\n\t\t}\n\n\t\t__set__ = function(object,key,value){\n\t\t\t//console.log(value)\n\t\t\tobject['$$'+key] = value\n\t\t}\n\t}\n}catch(e){//ie8\n}\n\n//if(typeof require == 'function'){\n\texports.DocumentType = DocumentType;\n\texports.DOMException = DOMException;\n\texports.DOMImplementation = DOMImplementation;\n\texports.Element = Element;\n\texports.Node = Node;\n\texports.NodeList = NodeList;\n\texports.XMLSerializer = XMLSerializer;\n//}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHhtbGRvbS94bWxkb20vbGliL2RvbS5qcyIsIm1hcHBpbmdzIjoiQUFBQSxrQkFBa0IsbUJBQU8sQ0FBQyw2RUFBZTs7QUFFekM7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLGFBQWEsVUFBVTtBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcscUNBQXFDO0FBQ2hELFdBQVcsUUFBUTtBQUNuQixhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxREFBcUQ7QUFDckQ7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLE9BQU87QUFDbEIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLHdDQUF3QyxvQkFBb0IsWUFBWSxRQUFRO0FBQ2hGLDJDQUEyQyxRQUFRO0FBQ25EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxFQUFFO0FBQ0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsRUFBRTtBQUNGO0FBQ0EsMEJBQTBCLGNBQWM7QUFDeEM7QUFDQTtBQUNBO0FBQ0EsRUFBRTtBQUNGO0FBQ0E7QUFDQSxZQUFZLHlCQUF5QjtBQUNyQyxjQUFjO0FBQ2Q7QUFDQTtBQUNBO0FBQ0EsRUFBRTtBQUNGO0FBQ0E7QUFDQSxZQUFZLE1BQU07QUFDbEIsY0FBYztBQUNkO0FBQ0E7QUFDQTtBQUNBLEVBQUU7QUFDRjs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCLFdBQVc7QUFDdEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCO0FBQ3ZCO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsRUFBRTtBQUNGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEVBQUU7QUFDRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEVBQUU7QUFDRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsRUFBRTtBQUNGO0FBQ0EsaUNBQWlDO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsRUFBRTs7QUFFRjtBQUNBO0FBQ0E7QUFDQTtBQUNBOzs7QUFHQSxFQUFFOztBQUVGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxFQUFFO0FBQ0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLFFBQVE7QUFDcEIsWUFBWSxRQUFRO0FBQ3BCLGNBQWMsU0FBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEVBQUU7QUFDRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLGFBQWE7QUFDekIsWUFBWSxRQUFRO0FBQ3BCLFlBQVksbUJBQW1CO0FBQy9CLGNBQWM7QUFDZDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEVBQUU7QUFDRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksUUFBUTtBQUNwQixZQUFZLFFBQVE7QUFDcEIsWUFBWSxRQUFRO0FBQ3BCLGNBQWMsY0FBYztBQUM1QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7OztBQUdBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQ0FBMkM7QUFDM0M7QUFDQSxFQUFFO0FBQ0YsMkNBQTJDO0FBQzNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsRUFBRTtBQUNGO0FBQ0E7QUFDQSxFQUFFO0FBQ0Y7QUFDQTtBQUNBLEVBQUU7QUFDRjtBQUNBO0FBQ0EsRUFBRTtBQUNGO0FBQ0E7QUFDQSxFQUFFO0FBQ0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEVBQUU7QUFDRjtBQUNBO0FBQ0E7QUFDQSxFQUFFO0FBQ0Y7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSxlQUFlO0FBQzNCLGNBQWM7QUFDZDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOzs7QUFHQTtBQUNBLHlCQUF5QjtBQUN6QiwwQkFBMEI7QUFDMUIsMkJBQTJCO0FBQzNCLDRCQUE0QjtBQUM1QiwrQkFBK0I7QUFDL0I7OztBQUdBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUM7QUFDakMsU0FBUztBQUNUO0FBQ0E7Ozs7QUFJQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxVQUFVO0FBQ3JCLFdBQVcsTUFBTTtBQUNqQixXQUFXLE1BQU07QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLE1BQU07QUFDakIsV0FBVyxNQUFNO0FBQ2pCLGFBQWEsTUFBTTtBQUNuQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxXQUFXLE1BQU07QUFDakIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxXQUFXLE1BQU07QUFDakIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsV0FBVyxNQUFNO0FBQ2pCLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsV0FBVyxNQUFNO0FBQ2pCLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLE1BQU07QUFDakIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxVQUFVO0FBQ3JCLFdBQVcsTUFBTTtBQUNqQixhQUFhLFNBQVM7QUFDdEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsTUFBTTtBQUNqQixXQUFXLE1BQU07QUFDakIsYUFBYSxTQUFTO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsTUFBTTtBQUNqQixXQUFXLE1BQU07QUFDakIsV0FBVyxPQUFPO0FBQ2xCLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsVUFBVTtBQUNyQixXQUFXLE1BQU07QUFDakIsV0FBVyxrQkFBa0I7QUFDN0IsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsVUFBVTtBQUNyQixXQUFXLE1BQU07QUFDakIsV0FBVyxrQkFBa0I7QUFDN0IsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLFdBQVcsTUFBTTtBQUNqQixXQUFXLE1BQU07QUFDakIsV0FBVyxPQUFPO0FBQ2xCLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSx1QkFBdUI7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxFQUFFO0FBQ0Y7QUFDQTtBQUNBOztBQUVBO0FBQ0E7OztBQUdBO0FBQ0E7QUFDQSxFQUFFO0FBQ0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQSxFQUFFO0FBQ0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQSxFQUFFO0FBQ0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxNQUFNO0FBQ2pCLFdBQVcsTUFBTTtBQUNqQixhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsOENBQThDO0FBQzlDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxFQUFFO0FBQ0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEVBQUU7QUFDRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEVBQUU7QUFDRjtBQUNBO0FBQ0E7QUFDQSxFQUFFO0FBQ0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQSxFQUFFOztBQUVGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksUUFBUSxnRUFBZ0U7QUFDcEY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLEdBQUc7QUFDSCxFQUFFOztBQUVGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxFQUFFO0FBQ0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEVBQUU7QUFDRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsRUFBRTtBQUNGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxFQUFFO0FBQ0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEVBQUU7QUFDRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxFQUFFO0FBQ0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEVBQUU7QUFDRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsRUFBRTtBQUNGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxFQUFFO0FBQ0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOzs7QUFHQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEVBQUU7QUFDRjtBQUNBO0FBQ0E7QUFDQSxFQUFFO0FBQ0Y7QUFDQTtBQUNBLEVBQUU7QUFDRjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEVBQUU7QUFDRjtBQUNBO0FBQ0E7QUFDQSxFQUFFOztBQUVGO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQSxFQUFFO0FBQ0Y7QUFDQTtBQUNBLEVBQUU7QUFDRjtBQUNBO0FBQ0EsRUFBRTtBQUNGO0FBQ0E7QUFDQTtBQUNBLEVBQUU7QUFDRjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEVBQUU7O0FBRUY7QUFDQTtBQUNBLEVBQUU7QUFDRjtBQUNBO0FBQ0E7QUFDQSxFQUFFO0FBQ0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQSxFQUFFO0FBQ0Y7QUFDQTtBQUNBLEVBQUU7O0FBRUY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQSxHQUFHO0FBQ0gsRUFBRTtBQUNGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKOztBQUVBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTs7O0FBR0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7O0FBR0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsRUFBRTtBQUNGO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsRUFBRTtBQUNGO0FBQ0E7O0FBRUEsRUFBRTtBQUNGO0FBQ0E7QUFDQSxFQUFFO0FBQ0Y7QUFDQTtBQUNBLEVBQUU7QUFDRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOzs7QUFHQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOzs7QUFHQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSixNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsbUJBQW1CO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaURBQWlELFVBQVU7QUFDM0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlEQUFpRCxVQUFVO0FBQzNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBLGNBQWMsTUFBTTtBQUNwQjtBQUNBO0FBQ0E7QUFDQSw2QkFBNkIsK0NBQStDO0FBQzVFLElBQUk7QUFDSiw2QkFBNkIsbUNBQW1DO0FBQ2hFO0FBQ0E7O0FBRUEsY0FBYyxNQUFNO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2QkFBNkIsK0JBQStCO0FBQzVEO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLCtCQUErQjtBQUMzRDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxXQUFXO0FBQ3RCLDRFQUE0RTtBQUM1RSxrQ0FBa0M7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVDQUF1QztBQUN2QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLE1BQU07QUFDdEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQztBQUNoQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQjtBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYyxNQUFNO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBO0FBQ0E7QUFDQSxJQUFJOztBQUVKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLFNBQVM7QUFDVjs7QUFFQTtBQUNBLENBQUMsb0JBQW9CO0FBQ3JCLENBQUMsb0JBQW9CO0FBQ3JCLENBQUMseUJBQXlCO0FBQzFCLENBQUMsZUFBZTtBQUNoQixDQUFDLFlBQVk7QUFDYixDQUFDLGdCQUFnQjtBQUNqQixDQUFDLHFCQUFxQjtBQUN0QiIsInNvdXJjZXMiOlsiL1VzZXJzL3l1c3VmL0Rlc2t0b3AvcG9ydGZvbGlvL25vZGVfbW9kdWxlcy9AeG1sZG9tL3htbGRvbS9saWIvZG9tLmpzIl0sInNvdXJjZXNDb250ZW50IjpbInZhciBjb252ZW50aW9ucyA9IHJlcXVpcmUoXCIuL2NvbnZlbnRpb25zXCIpO1xuXG52YXIgZmluZCA9IGNvbnZlbnRpb25zLmZpbmQ7XG52YXIgTkFNRVNQQUNFID0gY29udmVudGlvbnMuTkFNRVNQQUNFO1xuXG4vKipcbiAqIEEgcHJlcmVxdWlzaXRlIGZvciBgW10uZmlsdGVyYCwgdG8gZHJvcCBlbGVtZW50cyB0aGF0IGFyZSBlbXB0eVxuICogQHBhcmFtIHtzdHJpbmd9IGlucHV0XG4gKiBAcmV0dXJucyB7Ym9vbGVhbn1cbiAqL1xuZnVuY3Rpb24gbm90RW1wdHlTdHJpbmcgKGlucHV0KSB7XG5cdHJldHVybiBpbnB1dCAhPT0gJydcbn1cbi8qKlxuICogQHNlZSBodHRwczovL2luZnJhLnNwZWMud2hhdHdnLm9yZy8jc3BsaXQtb24tYXNjaWktd2hpdGVzcGFjZVxuICogQHNlZSBodHRwczovL2luZnJhLnNwZWMud2hhdHdnLm9yZy8jYXNjaWktd2hpdGVzcGFjZVxuICpcbiAqIEBwYXJhbSB7c3RyaW5nfSBpbnB1dFxuICogQHJldHVybnMge3N0cmluZ1tdfSAoY2FuIGJlIGVtcHR5KVxuICovXG5mdW5jdGlvbiBzcGxpdE9uQVNDSUlXaGl0ZXNwYWNlKGlucHV0KSB7XG5cdC8vIFUrMDAwOSBUQUIsIFUrMDAwQSBMRiwgVSswMDBDIEZGLCBVKzAwMEQgQ1IsIFUrMDAyMCBTUEFDRVxuXHRyZXR1cm4gaW5wdXQgPyBpbnB1dC5zcGxpdCgvW1xcdFxcblxcZlxcciBdKy8pLmZpbHRlcihub3RFbXB0eVN0cmluZykgOiBbXVxufVxuXG4vKipcbiAqIEFkZHMgZWxlbWVudCBhcyBhIGtleSB0byBjdXJyZW50IGlmIGl0IGlzIG5vdCBhbHJlYWR5IHByZXNlbnQuXG4gKlxuICogQHBhcmFtIHtSZWNvcmQ8c3RyaW5nLCBib29sZWFuIHwgdW5kZWZpbmVkPn0gY3VycmVudFxuICogQHBhcmFtIHtzdHJpbmd9IGVsZW1lbnRcbiAqIEByZXR1cm5zIHtSZWNvcmQ8c3RyaW5nLCBib29sZWFuIHwgdW5kZWZpbmVkPn1cbiAqL1xuZnVuY3Rpb24gb3JkZXJlZFNldFJlZHVjZXIgKGN1cnJlbnQsIGVsZW1lbnQpIHtcblx0aWYgKCFjdXJyZW50Lmhhc093blByb3BlcnR5KGVsZW1lbnQpKSB7XG5cdFx0Y3VycmVudFtlbGVtZW50XSA9IHRydWU7XG5cdH1cblx0cmV0dXJuIGN1cnJlbnQ7XG59XG5cbi8qKlxuICogQHNlZSBodHRwczovL2luZnJhLnNwZWMud2hhdHdnLm9yZy8jb3JkZXJlZC1zZXRcbiAqIEBwYXJhbSB7c3RyaW5nfSBpbnB1dFxuICogQHJldHVybnMge3N0cmluZ1tdfVxuICovXG5mdW5jdGlvbiB0b09yZGVyZWRTZXQoaW5wdXQpIHtcblx0aWYgKCFpbnB1dCkgcmV0dXJuIFtdO1xuXHR2YXIgbGlzdCA9IHNwbGl0T25BU0NJSVdoaXRlc3BhY2UoaW5wdXQpO1xuXHRyZXR1cm4gT2JqZWN0LmtleXMobGlzdC5yZWR1Y2Uob3JkZXJlZFNldFJlZHVjZXIsIHt9KSlcbn1cblxuLyoqXG4gKiBVc2VzIGBsaXN0LmluZGV4T2ZgIHRvIGltcGxlbWVudCBzb21ldGhpbmcgbGlrZSBgQXJyYXkucHJvdG90eXBlLmluY2x1ZGVzYCxcbiAqIHdoaWNoIHdlIGNhbiBub3QgcmVseSBvbiBiZWluZyBhdmFpbGFibGUuXG4gKlxuICogQHBhcmFtIHthbnlbXX0gbGlzdFxuICogQHJldHVybnMge2Z1bmN0aW9uKGFueSk6IGJvb2xlYW59XG4gKi9cbmZ1bmN0aW9uIGFycmF5SW5jbHVkZXMgKGxpc3QpIHtcblx0cmV0dXJuIGZ1bmN0aW9uKGVsZW1lbnQpIHtcblx0XHRyZXR1cm4gbGlzdCAmJiBsaXN0LmluZGV4T2YoZWxlbWVudCkgIT09IC0xO1xuXHR9XG59XG5cbmZ1bmN0aW9uIGNvcHkoc3JjLGRlc3Qpe1xuXHRmb3IodmFyIHAgaW4gc3JjKXtcblx0XHRpZiAoT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKHNyYywgcCkpIHtcblx0XHRcdGRlc3RbcF0gPSBzcmNbcF07XG5cdFx0fVxuXHR9XG59XG5cbi8qKlxuXlxcdytcXC5wcm90b3R5cGVcXC4oW19cXHddKylcXHMqPVxccyooKD86LipcXHtcXHMqP1tcXHJcXG5dW1xcc1xcU10qP159KXxcXFMuKj8oPz1bO1xcclxcbl0pKTs/XG5eXFx3K1xcLnByb3RvdHlwZVxcLihbX1xcd10rKVxccyo9XFxzKihcXFMuKj8oPz1bO1xcclxcbl0pKTs/XG4gKi9cbmZ1bmN0aW9uIF9leHRlbmRzKENsYXNzLFN1cGVyKXtcblx0dmFyIHB0ID0gQ2xhc3MucHJvdG90eXBlO1xuXHRpZighKHB0IGluc3RhbmNlb2YgU3VwZXIpKXtcblx0XHRmdW5jdGlvbiB0KCl7fTtcblx0XHR0LnByb3RvdHlwZSA9IFN1cGVyLnByb3RvdHlwZTtcblx0XHR0ID0gbmV3IHQoKTtcblx0XHRjb3B5KHB0LHQpO1xuXHRcdENsYXNzLnByb3RvdHlwZSA9IHB0ID0gdDtcblx0fVxuXHRpZihwdC5jb25zdHJ1Y3RvciAhPSBDbGFzcyl7XG5cdFx0aWYodHlwZW9mIENsYXNzICE9ICdmdW5jdGlvbicpe1xuXHRcdFx0Y29uc29sZS5lcnJvcihcInVua25vd24gQ2xhc3M6XCIrQ2xhc3MpXG5cdFx0fVxuXHRcdHB0LmNvbnN0cnVjdG9yID0gQ2xhc3Ncblx0fVxufVxuXG4vLyBOb2RlIFR5cGVzXG52YXIgTm9kZVR5cGUgPSB7fVxudmFyIEVMRU1FTlRfTk9ERSAgICAgICAgICAgICAgICA9IE5vZGVUeXBlLkVMRU1FTlRfTk9ERSAgICAgICAgICAgICAgICA9IDE7XG52YXIgQVRUUklCVVRFX05PREUgICAgICAgICAgICAgID0gTm9kZVR5cGUuQVRUUklCVVRFX05PREUgICAgICAgICAgICAgID0gMjtcbnZhciBURVhUX05PREUgICAgICAgICAgICAgICAgICAgPSBOb2RlVHlwZS5URVhUX05PREUgICAgICAgICAgICAgICAgICAgPSAzO1xudmFyIENEQVRBX1NFQ1RJT05fTk9ERSAgICAgICAgICA9IE5vZGVUeXBlLkNEQVRBX1NFQ1RJT05fTk9ERSAgICAgICAgICA9IDQ7XG52YXIgRU5USVRZX1JFRkVSRU5DRV9OT0RFICAgICAgID0gTm9kZVR5cGUuRU5USVRZX1JFRkVSRU5DRV9OT0RFICAgICAgID0gNTtcbnZhciBFTlRJVFlfTk9ERSAgICAgICAgICAgICAgICAgPSBOb2RlVHlwZS5FTlRJVFlfTk9ERSAgICAgICAgICAgICAgICAgPSA2O1xudmFyIFBST0NFU1NJTkdfSU5TVFJVQ1RJT05fTk9ERSA9IE5vZGVUeXBlLlBST0NFU1NJTkdfSU5TVFJVQ1RJT05fTk9ERSA9IDc7XG52YXIgQ09NTUVOVF9OT0RFICAgICAgICAgICAgICAgID0gTm9kZVR5cGUuQ09NTUVOVF9OT0RFICAgICAgICAgICAgICAgID0gODtcbnZhciBET0NVTUVOVF9OT0RFICAgICAgICAgICAgICAgPSBOb2RlVHlwZS5ET0NVTUVOVF9OT0RFICAgICAgICAgICAgICAgPSA5O1xudmFyIERPQ1VNRU5UX1RZUEVfTk9ERSAgICAgICAgICA9IE5vZGVUeXBlLkRPQ1VNRU5UX1RZUEVfTk9ERSAgICAgICAgICA9IDEwO1xudmFyIERPQ1VNRU5UX0ZSQUdNRU5UX05PREUgICAgICA9IE5vZGVUeXBlLkRPQ1VNRU5UX0ZSQUdNRU5UX05PREUgICAgICA9IDExO1xudmFyIE5PVEFUSU9OX05PREUgICAgICAgICAgICAgICA9IE5vZGVUeXBlLk5PVEFUSU9OX05PREUgICAgICAgICAgICAgICA9IDEyO1xuXG4vLyBFeGNlcHRpb25Db2RlXG52YXIgRXhjZXB0aW9uQ29kZSA9IHt9XG52YXIgRXhjZXB0aW9uTWVzc2FnZSA9IHt9O1xudmFyIElOREVYX1NJWkVfRVJSICAgICAgICAgICAgICA9IEV4Y2VwdGlvbkNvZGUuSU5ERVhfU0laRV9FUlIgICAgICAgICAgICAgID0gKChFeGNlcHRpb25NZXNzYWdlWzFdPVwiSW5kZXggc2l6ZSBlcnJvclwiKSwxKTtcbnZhciBET01TVFJJTkdfU0laRV9FUlIgICAgICAgICAgPSBFeGNlcHRpb25Db2RlLkRPTVNUUklOR19TSVpFX0VSUiAgICAgICAgICA9ICgoRXhjZXB0aW9uTWVzc2FnZVsyXT1cIkRPTVN0cmluZyBzaXplIGVycm9yXCIpLDIpO1xudmFyIEhJRVJBUkNIWV9SRVFVRVNUX0VSUiAgICAgICA9IEV4Y2VwdGlvbkNvZGUuSElFUkFSQ0hZX1JFUVVFU1RfRVJSICAgICAgID0gKChFeGNlcHRpb25NZXNzYWdlWzNdPVwiSGllcmFyY2h5IHJlcXVlc3QgZXJyb3JcIiksMyk7XG52YXIgV1JPTkdfRE9DVU1FTlRfRVJSICAgICAgICAgID0gRXhjZXB0aW9uQ29kZS5XUk9OR19ET0NVTUVOVF9FUlIgICAgICAgICAgPSAoKEV4Y2VwdGlvbk1lc3NhZ2VbNF09XCJXcm9uZyBkb2N1bWVudFwiKSw0KTtcbnZhciBJTlZBTElEX0NIQVJBQ1RFUl9FUlIgICAgICAgPSBFeGNlcHRpb25Db2RlLklOVkFMSURfQ0hBUkFDVEVSX0VSUiAgICAgICA9ICgoRXhjZXB0aW9uTWVzc2FnZVs1XT1cIkludmFsaWQgY2hhcmFjdGVyXCIpLDUpO1xudmFyIE5PX0RBVEFfQUxMT1dFRF9FUlIgICAgICAgICA9IEV4Y2VwdGlvbkNvZGUuTk9fREFUQV9BTExPV0VEX0VSUiAgICAgICAgID0gKChFeGNlcHRpb25NZXNzYWdlWzZdPVwiTm8gZGF0YSBhbGxvd2VkXCIpLDYpO1xudmFyIE5PX01PRElGSUNBVElPTl9BTExPV0VEX0VSUiA9IEV4Y2VwdGlvbkNvZGUuTk9fTU9ESUZJQ0FUSU9OX0FMTE9XRURfRVJSID0gKChFeGNlcHRpb25NZXNzYWdlWzddPVwiTm8gbW9kaWZpY2F0aW9uIGFsbG93ZWRcIiksNyk7XG52YXIgTk9UX0ZPVU5EX0VSUiAgICAgICAgICAgICAgID0gRXhjZXB0aW9uQ29kZS5OT1RfRk9VTkRfRVJSICAgICAgICAgICAgICAgPSAoKEV4Y2VwdGlvbk1lc3NhZ2VbOF09XCJOb3QgZm91bmRcIiksOCk7XG52YXIgTk9UX1NVUFBPUlRFRF9FUlIgICAgICAgICAgID0gRXhjZXB0aW9uQ29kZS5OT1RfU1VQUE9SVEVEX0VSUiAgICAgICAgICAgPSAoKEV4Y2VwdGlvbk1lc3NhZ2VbOV09XCJOb3Qgc3VwcG9ydGVkXCIpLDkpO1xudmFyIElOVVNFX0FUVFJJQlVURV9FUlIgICAgICAgICA9IEV4Y2VwdGlvbkNvZGUuSU5VU0VfQVRUUklCVVRFX0VSUiAgICAgICAgID0gKChFeGNlcHRpb25NZXNzYWdlWzEwXT1cIkF0dHJpYnV0ZSBpbiB1c2VcIiksMTApO1xuLy9sZXZlbDJcbnZhciBJTlZBTElEX1NUQVRFX0VSUiAgICAgICAgXHQ9IEV4Y2VwdGlvbkNvZGUuSU5WQUxJRF9TVEFURV9FUlIgICAgICAgIFx0PSAoKEV4Y2VwdGlvbk1lc3NhZ2VbMTFdPVwiSW52YWxpZCBzdGF0ZVwiKSwxMSk7XG52YXIgU1lOVEFYX0VSUiAgICAgICAgICAgICAgIFx0PSBFeGNlcHRpb25Db2RlLlNZTlRBWF9FUlIgICAgICAgICAgICAgICBcdD0gKChFeGNlcHRpb25NZXNzYWdlWzEyXT1cIlN5bnRheCBlcnJvclwiKSwxMik7XG52YXIgSU5WQUxJRF9NT0RJRklDQVRJT05fRVJSIFx0PSBFeGNlcHRpb25Db2RlLklOVkFMSURfTU9ESUZJQ0FUSU9OX0VSUiBcdD0gKChFeGNlcHRpb25NZXNzYWdlWzEzXT1cIkludmFsaWQgbW9kaWZpY2F0aW9uXCIpLDEzKTtcbnZhciBOQU1FU1BBQ0VfRVJSICAgICAgICAgICAgXHQ9IEV4Y2VwdGlvbkNvZGUuTkFNRVNQQUNFX0VSUiAgICAgICAgICAgXHQ9ICgoRXhjZXB0aW9uTWVzc2FnZVsxNF09XCJJbnZhbGlkIG5hbWVzcGFjZVwiKSwxNCk7XG52YXIgSU5WQUxJRF9BQ0NFU1NfRVJSICAgICAgIFx0PSBFeGNlcHRpb25Db2RlLklOVkFMSURfQUNDRVNTX0VSUiAgICAgIFx0PSAoKEV4Y2VwdGlvbk1lc3NhZ2VbMTVdPVwiSW52YWxpZCBhY2Nlc3NcIiksMTUpO1xuXG4vKipcbiAqIERPTSBMZXZlbCAyXG4gKiBPYmplY3QgRE9NRXhjZXB0aW9uXG4gKiBAc2VlIGh0dHA6Ly93d3cudzMub3JnL1RSLzIwMDAvUkVDLURPTS1MZXZlbC0yLUNvcmUtMjAwMDExMTMvZWNtYS1zY3JpcHQtYmluZGluZy5odG1sXG4gKiBAc2VlIGh0dHA6Ly93d3cudzMub3JnL1RSL1JFQy1ET00tTGV2ZWwtMS9lY21hLXNjcmlwdC1sYW5ndWFnZS1iaW5kaW5nLmh0bWxcbiAqL1xuZnVuY3Rpb24gRE9NRXhjZXB0aW9uKGNvZGUsIG1lc3NhZ2UpIHtcblx0aWYobWVzc2FnZSBpbnN0YW5jZW9mIEVycm9yKXtcblx0XHR2YXIgZXJyb3IgPSBtZXNzYWdlO1xuXHR9ZWxzZXtcblx0XHRlcnJvciA9IHRoaXM7XG5cdFx0RXJyb3IuY2FsbCh0aGlzLCBFeGNlcHRpb25NZXNzYWdlW2NvZGVdKTtcblx0XHR0aGlzLm1lc3NhZ2UgPSBFeGNlcHRpb25NZXNzYWdlW2NvZGVdO1xuXHRcdGlmKEVycm9yLmNhcHR1cmVTdGFja1RyYWNlKSBFcnJvci5jYXB0dXJlU3RhY2tUcmFjZSh0aGlzLCBET01FeGNlcHRpb24pO1xuXHR9XG5cdGVycm9yLmNvZGUgPSBjb2RlO1xuXHRpZihtZXNzYWdlKSB0aGlzLm1lc3NhZ2UgPSB0aGlzLm1lc3NhZ2UgKyBcIjogXCIgKyBtZXNzYWdlO1xuXHRyZXR1cm4gZXJyb3I7XG59O1xuRE9NRXhjZXB0aW9uLnByb3RvdHlwZSA9IEVycm9yLnByb3RvdHlwZTtcbmNvcHkoRXhjZXB0aW9uQ29kZSxET01FeGNlcHRpb24pXG5cbi8qKlxuICogQHNlZSBodHRwOi8vd3d3LnczLm9yZy9UUi8yMDAwL1JFQy1ET00tTGV2ZWwtMi1Db3JlLTIwMDAxMTEzL2NvcmUuaHRtbCNJRC01MzYyOTcxNzdcbiAqIFRoZSBOb2RlTGlzdCBpbnRlcmZhY2UgcHJvdmlkZXMgdGhlIGFic3RyYWN0aW9uIG9mIGFuIG9yZGVyZWQgY29sbGVjdGlvbiBvZiBub2Rlcywgd2l0aG91dCBkZWZpbmluZyBvciBjb25zdHJhaW5pbmcgaG93IHRoaXMgY29sbGVjdGlvbiBpcyBpbXBsZW1lbnRlZC4gTm9kZUxpc3Qgb2JqZWN0cyBpbiB0aGUgRE9NIGFyZSBsaXZlLlxuICogVGhlIGl0ZW1zIGluIHRoZSBOb2RlTGlzdCBhcmUgYWNjZXNzaWJsZSB2aWEgYW4gaW50ZWdyYWwgaW5kZXgsIHN0YXJ0aW5nIGZyb20gMC5cbiAqL1xuZnVuY3Rpb24gTm9kZUxpc3QoKSB7XG59O1xuTm9kZUxpc3QucHJvdG90eXBlID0ge1xuXHQvKipcblx0ICogVGhlIG51bWJlciBvZiBub2RlcyBpbiB0aGUgbGlzdC4gVGhlIHJhbmdlIG9mIHZhbGlkIGNoaWxkIG5vZGUgaW5kaWNlcyBpcyAwIHRvIGxlbmd0aC0xIGluY2x1c2l2ZS5cblx0ICogQHN0YW5kYXJkIGxldmVsMVxuXHQgKi9cblx0bGVuZ3RoOjAsXG5cdC8qKlxuXHQgKiBSZXR1cm5zIHRoZSBpbmRleHRoIGl0ZW0gaW4gdGhlIGNvbGxlY3Rpb24uIElmIGluZGV4IGlzIGdyZWF0ZXIgdGhhbiBvciBlcXVhbCB0byB0aGUgbnVtYmVyIG9mIG5vZGVzIGluIHRoZSBsaXN0LCB0aGlzIHJldHVybnMgbnVsbC5cblx0ICogQHN0YW5kYXJkIGxldmVsMVxuXHQgKiBAcGFyYW0gaW5kZXggIHVuc2lnbmVkIGxvbmdcblx0ICogICBJbmRleCBpbnRvIHRoZSBjb2xsZWN0aW9uLlxuXHQgKiBAcmV0dXJuIE5vZGVcblx0ICogXHRUaGUgbm9kZSBhdCB0aGUgaW5kZXh0aCBwb3NpdGlvbiBpbiB0aGUgTm9kZUxpc3QsIG9yIG51bGwgaWYgdGhhdCBpcyBub3QgYSB2YWxpZCBpbmRleC5cblx0ICovXG5cdGl0ZW06IGZ1bmN0aW9uKGluZGV4KSB7XG5cdFx0cmV0dXJuIGluZGV4ID49IDAgJiYgaW5kZXggPCB0aGlzLmxlbmd0aCA/IHRoaXNbaW5kZXhdIDogbnVsbDtcblx0fSxcblx0dG9TdHJpbmc6ZnVuY3Rpb24oaXNIVE1MLG5vZGVGaWx0ZXIpe1xuXHRcdGZvcih2YXIgYnVmID0gW10sIGkgPSAwO2k8dGhpcy5sZW5ndGg7aSsrKXtcblx0XHRcdHNlcmlhbGl6ZVRvU3RyaW5nKHRoaXNbaV0sYnVmLGlzSFRNTCxub2RlRmlsdGVyKTtcblx0XHR9XG5cdFx0cmV0dXJuIGJ1Zi5qb2luKCcnKTtcblx0fSxcblx0LyoqXG5cdCAqIEBwcml2YXRlXG5cdCAqIEBwYXJhbSB7ZnVuY3Rpb24gKE5vZGUpOmJvb2xlYW59IHByZWRpY2F0ZVxuXHQgKiBAcmV0dXJucyB7Tm9kZVtdfVxuXHQgKi9cblx0ZmlsdGVyOiBmdW5jdGlvbiAocHJlZGljYXRlKSB7XG5cdFx0cmV0dXJuIEFycmF5LnByb3RvdHlwZS5maWx0ZXIuY2FsbCh0aGlzLCBwcmVkaWNhdGUpO1xuXHR9LFxuXHQvKipcblx0ICogQHByaXZhdGVcblx0ICogQHBhcmFtIHtOb2RlfSBpdGVtXG5cdCAqIEByZXR1cm5zIHtudW1iZXJ9XG5cdCAqL1xuXHRpbmRleE9mOiBmdW5jdGlvbiAoaXRlbSkge1xuXHRcdHJldHVybiBBcnJheS5wcm90b3R5cGUuaW5kZXhPZi5jYWxsKHRoaXMsIGl0ZW0pO1xuXHR9LFxufTtcblxuZnVuY3Rpb24gTGl2ZU5vZGVMaXN0KG5vZGUscmVmcmVzaCl7XG5cdHRoaXMuX25vZGUgPSBub2RlO1xuXHR0aGlzLl9yZWZyZXNoID0gcmVmcmVzaFxuXHRfdXBkYXRlTGl2ZUxpc3QodGhpcyk7XG59XG5mdW5jdGlvbiBfdXBkYXRlTGl2ZUxpc3QobGlzdCl7XG5cdHZhciBpbmMgPSBsaXN0Ll9ub2RlLl9pbmMgfHwgbGlzdC5fbm9kZS5vd25lckRvY3VtZW50Ll9pbmM7XG5cdGlmIChsaXN0Ll9pbmMgIT09IGluYykge1xuXHRcdHZhciBscyA9IGxpc3QuX3JlZnJlc2gobGlzdC5fbm9kZSk7XG5cdFx0X19zZXRfXyhsaXN0LCdsZW5ndGgnLGxzLmxlbmd0aCk7XG5cdFx0aWYgKCFsaXN0LiQkbGVuZ3RoIHx8IGxzLmxlbmd0aCA8IGxpc3QuJCRsZW5ndGgpIHtcblx0XHRcdGZvciAodmFyIGkgPSBscy5sZW5ndGg7IGkgaW4gbGlzdDsgaSsrKSB7XG5cdFx0XHRcdGlmIChPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwobGlzdCwgaSkpIHtcblx0XHRcdFx0XHRkZWxldGUgbGlzdFtpXTtcblx0XHRcdFx0fVxuXHRcdFx0fVxuXHRcdH1cblx0XHRjb3B5KGxzLGxpc3QpO1xuXHRcdGxpc3QuX2luYyA9IGluYztcblx0fVxufVxuTGl2ZU5vZGVMaXN0LnByb3RvdHlwZS5pdGVtID0gZnVuY3Rpb24oaSl7XG5cdF91cGRhdGVMaXZlTGlzdCh0aGlzKTtcblx0cmV0dXJuIHRoaXNbaV0gfHwgbnVsbDtcbn1cblxuX2V4dGVuZHMoTGl2ZU5vZGVMaXN0LE5vZGVMaXN0KTtcblxuLyoqXG4gKiBPYmplY3RzIGltcGxlbWVudGluZyB0aGUgTmFtZWROb2RlTWFwIGludGVyZmFjZSBhcmUgdXNlZFxuICogdG8gcmVwcmVzZW50IGNvbGxlY3Rpb25zIG9mIG5vZGVzIHRoYXQgY2FuIGJlIGFjY2Vzc2VkIGJ5IG5hbWUuXG4gKiBOb3RlIHRoYXQgTmFtZWROb2RlTWFwIGRvZXMgbm90IGluaGVyaXQgZnJvbSBOb2RlTGlzdDtcbiAqIE5hbWVkTm9kZU1hcHMgYXJlIG5vdCBtYWludGFpbmVkIGluIGFueSBwYXJ0aWN1bGFyIG9yZGVyLlxuICogT2JqZWN0cyBjb250YWluZWQgaW4gYW4gb2JqZWN0IGltcGxlbWVudGluZyBOYW1lZE5vZGVNYXAgbWF5IGFsc28gYmUgYWNjZXNzZWQgYnkgYW4gb3JkaW5hbCBpbmRleCxcbiAqIGJ1dCB0aGlzIGlzIHNpbXBseSB0byBhbGxvdyBjb252ZW5pZW50IGVudW1lcmF0aW9uIG9mIHRoZSBjb250ZW50cyBvZiBhIE5hbWVkTm9kZU1hcCxcbiAqIGFuZCBkb2VzIG5vdCBpbXBseSB0aGF0IHRoZSBET00gc3BlY2lmaWVzIGFuIG9yZGVyIHRvIHRoZXNlIE5vZGVzLlxuICogTmFtZWROb2RlTWFwIG9iamVjdHMgaW4gdGhlIERPTSBhcmUgbGl2ZS5cbiAqIHVzZWQgZm9yIGF0dHJpYnV0ZXMgb3IgRG9jdW1lbnRUeXBlIGVudGl0aWVzXG4gKi9cbmZ1bmN0aW9uIE5hbWVkTm9kZU1hcCgpIHtcbn07XG5cbmZ1bmN0aW9uIF9maW5kTm9kZUluZGV4KGxpc3Qsbm9kZSl7XG5cdHZhciBpID0gbGlzdC5sZW5ndGg7XG5cdHdoaWxlKGktLSl7XG5cdFx0aWYobGlzdFtpXSA9PT0gbm9kZSl7cmV0dXJuIGl9XG5cdH1cbn1cblxuZnVuY3Rpb24gX2FkZE5hbWVkTm9kZShlbCxsaXN0LG5ld0F0dHIsb2xkQXR0cil7XG5cdGlmKG9sZEF0dHIpe1xuXHRcdGxpc3RbX2ZpbmROb2RlSW5kZXgobGlzdCxvbGRBdHRyKV0gPSBuZXdBdHRyO1xuXHR9ZWxzZXtcblx0XHRsaXN0W2xpc3QubGVuZ3RoKytdID0gbmV3QXR0cjtcblx0fVxuXHRpZihlbCl7XG5cdFx0bmV3QXR0ci5vd25lckVsZW1lbnQgPSBlbDtcblx0XHR2YXIgZG9jID0gZWwub3duZXJEb2N1bWVudDtcblx0XHRpZihkb2Mpe1xuXHRcdFx0b2xkQXR0ciAmJiBfb25SZW1vdmVBdHRyaWJ1dGUoZG9jLGVsLG9sZEF0dHIpO1xuXHRcdFx0X29uQWRkQXR0cmlidXRlKGRvYyxlbCxuZXdBdHRyKTtcblx0XHR9XG5cdH1cbn1cbmZ1bmN0aW9uIF9yZW1vdmVOYW1lZE5vZGUoZWwsbGlzdCxhdHRyKXtcblx0Ly9jb25zb2xlLmxvZygncmVtb3ZlIGF0dHI6JythdHRyKVxuXHR2YXIgaSA9IF9maW5kTm9kZUluZGV4KGxpc3QsYXR0cik7XG5cdGlmKGk+PTApe1xuXHRcdHZhciBsYXN0SW5kZXggPSBsaXN0Lmxlbmd0aC0xXG5cdFx0d2hpbGUoaTxsYXN0SW5kZXgpe1xuXHRcdFx0bGlzdFtpXSA9IGxpc3RbKytpXVxuXHRcdH1cblx0XHRsaXN0Lmxlbmd0aCA9IGxhc3RJbmRleDtcblx0XHRpZihlbCl7XG5cdFx0XHR2YXIgZG9jID0gZWwub3duZXJEb2N1bWVudDtcblx0XHRcdGlmKGRvYyl7XG5cdFx0XHRcdF9vblJlbW92ZUF0dHJpYnV0ZShkb2MsZWwsYXR0cik7XG5cdFx0XHRcdGF0dHIub3duZXJFbGVtZW50ID0gbnVsbDtcblx0XHRcdH1cblx0XHR9XG5cdH1lbHNle1xuXHRcdHRocm93IG5ldyBET01FeGNlcHRpb24oTk9UX0ZPVU5EX0VSUixuZXcgRXJyb3IoZWwudGFnTmFtZSsnQCcrYXR0cikpXG5cdH1cbn1cbk5hbWVkTm9kZU1hcC5wcm90b3R5cGUgPSB7XG5cdGxlbmd0aDowLFxuXHRpdGVtOk5vZGVMaXN0LnByb3RvdHlwZS5pdGVtLFxuXHRnZXROYW1lZEl0ZW06IGZ1bmN0aW9uKGtleSkge1xuLy9cdFx0aWYoa2V5LmluZGV4T2YoJzonKT4wIHx8IGtleSA9PSAneG1sbnMnKXtcbi8vXHRcdFx0cmV0dXJuIG51bGw7XG4vL1x0XHR9XG5cdFx0Ly9jb25zb2xlLmxvZygpXG5cdFx0dmFyIGkgPSB0aGlzLmxlbmd0aDtcblx0XHR3aGlsZShpLS0pe1xuXHRcdFx0dmFyIGF0dHIgPSB0aGlzW2ldO1xuXHRcdFx0Ly9jb25zb2xlLmxvZyhhdHRyLm5vZGVOYW1lLGtleSlcblx0XHRcdGlmKGF0dHIubm9kZU5hbWUgPT0ga2V5KXtcblx0XHRcdFx0cmV0dXJuIGF0dHI7XG5cdFx0XHR9XG5cdFx0fVxuXHR9LFxuXHRzZXROYW1lZEl0ZW06IGZ1bmN0aW9uKGF0dHIpIHtcblx0XHR2YXIgZWwgPSBhdHRyLm93bmVyRWxlbWVudDtcblx0XHRpZihlbCAmJiBlbCE9dGhpcy5fb3duZXJFbGVtZW50KXtcblx0XHRcdHRocm93IG5ldyBET01FeGNlcHRpb24oSU5VU0VfQVRUUklCVVRFX0VSUik7XG5cdFx0fVxuXHRcdHZhciBvbGRBdHRyID0gdGhpcy5nZXROYW1lZEl0ZW0oYXR0ci5ub2RlTmFtZSk7XG5cdFx0X2FkZE5hbWVkTm9kZSh0aGlzLl9vd25lckVsZW1lbnQsdGhpcyxhdHRyLG9sZEF0dHIpO1xuXHRcdHJldHVybiBvbGRBdHRyO1xuXHR9LFxuXHQvKiByZXR1cm5zIE5vZGUgKi9cblx0c2V0TmFtZWRJdGVtTlM6IGZ1bmN0aW9uKGF0dHIpIHsvLyByYWlzZXM6IFdST05HX0RPQ1VNRU5UX0VSUixOT19NT0RJRklDQVRJT05fQUxMT1dFRF9FUlIsSU5VU0VfQVRUUklCVVRFX0VSUlxuXHRcdHZhciBlbCA9IGF0dHIub3duZXJFbGVtZW50LCBvbGRBdHRyO1xuXHRcdGlmKGVsICYmIGVsIT10aGlzLl9vd25lckVsZW1lbnQpe1xuXHRcdFx0dGhyb3cgbmV3IERPTUV4Y2VwdGlvbihJTlVTRV9BVFRSSUJVVEVfRVJSKTtcblx0XHR9XG5cdFx0b2xkQXR0ciA9IHRoaXMuZ2V0TmFtZWRJdGVtTlMoYXR0ci5uYW1lc3BhY2VVUkksYXR0ci5sb2NhbE5hbWUpO1xuXHRcdF9hZGROYW1lZE5vZGUodGhpcy5fb3duZXJFbGVtZW50LHRoaXMsYXR0cixvbGRBdHRyKTtcblx0XHRyZXR1cm4gb2xkQXR0cjtcblx0fSxcblxuXHQvKiByZXR1cm5zIE5vZGUgKi9cblx0cmVtb3ZlTmFtZWRJdGVtOiBmdW5jdGlvbihrZXkpIHtcblx0XHR2YXIgYXR0ciA9IHRoaXMuZ2V0TmFtZWRJdGVtKGtleSk7XG5cdFx0X3JlbW92ZU5hbWVkTm9kZSh0aGlzLl9vd25lckVsZW1lbnQsdGhpcyxhdHRyKTtcblx0XHRyZXR1cm4gYXR0cjtcblxuXG5cdH0sLy8gcmFpc2VzOiBOT1RfRk9VTkRfRVJSLE5PX01PRElGSUNBVElPTl9BTExPV0VEX0VSUlxuXG5cdC8vZm9yIGxldmVsMlxuXHRyZW1vdmVOYW1lZEl0ZW1OUzpmdW5jdGlvbihuYW1lc3BhY2VVUkksbG9jYWxOYW1lKXtcblx0XHR2YXIgYXR0ciA9IHRoaXMuZ2V0TmFtZWRJdGVtTlMobmFtZXNwYWNlVVJJLGxvY2FsTmFtZSk7XG5cdFx0X3JlbW92ZU5hbWVkTm9kZSh0aGlzLl9vd25lckVsZW1lbnQsdGhpcyxhdHRyKTtcblx0XHRyZXR1cm4gYXR0cjtcblx0fSxcblx0Z2V0TmFtZWRJdGVtTlM6IGZ1bmN0aW9uKG5hbWVzcGFjZVVSSSwgbG9jYWxOYW1lKSB7XG5cdFx0dmFyIGkgPSB0aGlzLmxlbmd0aDtcblx0XHR3aGlsZShpLS0pe1xuXHRcdFx0dmFyIG5vZGUgPSB0aGlzW2ldO1xuXHRcdFx0aWYobm9kZS5sb2NhbE5hbWUgPT0gbG9jYWxOYW1lICYmIG5vZGUubmFtZXNwYWNlVVJJID09IG5hbWVzcGFjZVVSSSl7XG5cdFx0XHRcdHJldHVybiBub2RlO1xuXHRcdFx0fVxuXHRcdH1cblx0XHRyZXR1cm4gbnVsbDtcblx0fVxufTtcblxuLyoqXG4gKiBUaGUgRE9NSW1wbGVtZW50YXRpb24gaW50ZXJmYWNlIHJlcHJlc2VudHMgYW4gb2JqZWN0IHByb3ZpZGluZyBtZXRob2RzXG4gKiB3aGljaCBhcmUgbm90IGRlcGVuZGVudCBvbiBhbnkgcGFydGljdWxhciBkb2N1bWVudC5cbiAqIFN1Y2ggYW4gb2JqZWN0IGlzIHJldHVybmVkIGJ5IHRoZSBgRG9jdW1lbnQuaW1wbGVtZW50YXRpb25gIHByb3BlcnR5LlxuICpcbiAqIF9fVGhlIGluZGl2aWR1YWwgbWV0aG9kcyBkZXNjcmliZSB0aGUgZGlmZmVyZW5jZXMgY29tcGFyZWQgdG8gdGhlIHNwZWNzLl9fXG4gKlxuICogQGNvbnN0cnVjdG9yXG4gKlxuICogQHNlZSBodHRwczovL2RldmVsb3Blci5tb3ppbGxhLm9yZy9lbi1VUy9kb2NzL1dlYi9BUEkvRE9NSW1wbGVtZW50YXRpb24gTUROXG4gKiBAc2VlIGh0dHBzOi8vd3d3LnczLm9yZy9UUi9SRUMtRE9NLUxldmVsLTEvbGV2ZWwtb25lLWNvcmUuaHRtbCNJRC0xMDIxNjE0OTAgRE9NIExldmVsIDEgQ29yZSAoSW5pdGlhbClcbiAqIEBzZWUgaHR0cHM6Ly93d3cudzMub3JnL1RSL0RPTS1MZXZlbC0yLUNvcmUvY29yZS5odG1sI0lELTEwMjE2MTQ5MCBET00gTGV2ZWwgMiBDb3JlXG4gKiBAc2VlIGh0dHBzOi8vd3d3LnczLm9yZy9UUi9ET00tTGV2ZWwtMy1Db3JlL2NvcmUuaHRtbCNJRC0xMDIxNjE0OTAgRE9NIExldmVsIDMgQ29yZVxuICogQHNlZSBodHRwczovL2RvbS5zcGVjLndoYXR3Zy5vcmcvI2RvbWltcGxlbWVudGF0aW9uIERPTSBMaXZpbmcgU3RhbmRhcmRcbiAqL1xuZnVuY3Rpb24gRE9NSW1wbGVtZW50YXRpb24oKSB7XG59XG5cbkRPTUltcGxlbWVudGF0aW9uLnByb3RvdHlwZSA9IHtcblx0LyoqXG5cdCAqIFRoZSBET01JbXBsZW1lbnRhdGlvbi5oYXNGZWF0dXJlKCkgbWV0aG9kIHJldHVybnMgYSBCb29sZWFuIGZsYWcgaW5kaWNhdGluZyBpZiBhIGdpdmVuIGZlYXR1cmUgaXMgc3VwcG9ydGVkLlxuXHQgKiBUaGUgZGlmZmVyZW50IGltcGxlbWVudGF0aW9ucyBmYWlybHkgZGl2ZXJnZWQgaW4gd2hhdCBraW5kIG9mIGZlYXR1cmVzIHdlcmUgcmVwb3J0ZWQuXG5cdCAqIFRoZSBsYXRlc3QgdmVyc2lvbiBvZiB0aGUgc3BlYyBzZXR0bGVkIHRvIGZvcmNlIHRoaXMgbWV0aG9kIHRvIGFsd2F5cyByZXR1cm4gdHJ1ZSwgd2hlcmUgdGhlIGZ1bmN0aW9uYWxpdHkgd2FzIGFjY3VyYXRlIGFuZCBpbiB1c2UuXG5cdCAqXG5cdCAqIEBkZXByZWNhdGVkIEl0IGlzIGRlcHJlY2F0ZWQgYW5kIG1vZGVybiBicm93c2VycyByZXR1cm4gdHJ1ZSBpbiBhbGwgY2FzZXMuXG5cdCAqXG5cdCAqIEBwYXJhbSB7c3RyaW5nfSBmZWF0dXJlXG5cdCAqIEBwYXJhbSB7c3RyaW5nfSBbdmVyc2lvbl1cblx0ICogQHJldHVybnMge2Jvb2xlYW59IGFsd2F5cyB0cnVlXG5cdCAqXG5cdCAqIEBzZWUgaHR0cHM6Ly9kZXZlbG9wZXIubW96aWxsYS5vcmcvZW4tVVMvZG9jcy9XZWIvQVBJL0RPTUltcGxlbWVudGF0aW9uL2hhc0ZlYXR1cmUgTUROXG5cdCAqIEBzZWUgaHR0cHM6Ly93d3cudzMub3JnL1RSL1JFQy1ET00tTGV2ZWwtMS9sZXZlbC1vbmUtY29yZS5odG1sI0lELTVDRUQ5NEQ3IERPTSBMZXZlbCAxIENvcmVcblx0ICogQHNlZSBodHRwczovL2RvbS5zcGVjLndoYXR3Zy5vcmcvI2RvbS1kb21pbXBsZW1lbnRhdGlvbi1oYXNmZWF0dXJlIERPTSBMaXZpbmcgU3RhbmRhcmRcblx0ICovXG5cdGhhc0ZlYXR1cmU6IGZ1bmN0aW9uKGZlYXR1cmUsIHZlcnNpb24pIHtcblx0XHRcdHJldHVybiB0cnVlO1xuXHR9LFxuXHQvKipcblx0ICogQ3JlYXRlcyBhbiBYTUwgRG9jdW1lbnQgb2JqZWN0IG9mIHRoZSBzcGVjaWZpZWQgdHlwZSB3aXRoIGl0cyBkb2N1bWVudCBlbGVtZW50LlxuXHQgKlxuXHQgKiBfX0l0IGJlaGF2ZXMgc2xpZ2h0bHkgZGlmZmVyZW50IGZyb20gdGhlIGRlc2NyaXB0aW9uIGluIHRoZSBsaXZpbmcgc3RhbmRhcmRfXzpcblx0ICogLSBUaGVyZSBpcyBubyBpbnRlcmZhY2UvY2xhc3MgYFhNTERvY3VtZW50YCwgaXQgcmV0dXJucyBhIGBEb2N1bWVudGAgaW5zdGFuY2UuXG5cdCAqIC0gYGNvbnRlbnRUeXBlYCwgYGVuY29kaW5nYCwgYG1vZGVgLCBgb3JpZ2luYCwgYHVybGAgZmllbGRzIGFyZSBjdXJyZW50bHkgbm90IGRlY2xhcmVkLlxuXHQgKiAtIHRoaXMgaW1wbGVtZW50YXRpb24gaXMgbm90IHZhbGlkYXRpbmcgbmFtZXMgb3IgcXVhbGlmaWVkIG5hbWVzXG5cdCAqICAgKHdoZW4gcGFyc2luZyBYTUwgc3RyaW5ncywgdGhlIFNBWCBwYXJzZXIgdGFrZXMgY2FyZSBvZiB0aGF0KVxuXHQgKlxuXHQgKiBAcGFyYW0ge3N0cmluZ3xudWxsfSBuYW1lc3BhY2VVUklcblx0ICogQHBhcmFtIHtzdHJpbmd9IHF1YWxpZmllZE5hbWVcblx0ICogQHBhcmFtIHtEb2N1bWVudFR5cGU9bnVsbH0gZG9jdHlwZVxuXHQgKiBAcmV0dXJucyB7RG9jdW1lbnR9XG5cdCAqXG5cdCAqIEBzZWUgaHR0cHM6Ly9kZXZlbG9wZXIubW96aWxsYS5vcmcvZW4tVVMvZG9jcy9XZWIvQVBJL0RPTUltcGxlbWVudGF0aW9uL2NyZWF0ZURvY3VtZW50IE1ETlxuXHQgKiBAc2VlIGh0dHBzOi8vd3d3LnczLm9yZy9UUi9ET00tTGV2ZWwtMi1Db3JlL2NvcmUuaHRtbCNMZXZlbC0yLUNvcmUtRE9NLWNyZWF0ZURvY3VtZW50IERPTSBMZXZlbCAyIENvcmUgKGluaXRpYWwpXG5cdCAqIEBzZWUgaHR0cHM6Ly9kb20uc3BlYy53aGF0d2cub3JnLyNkb20tZG9taW1wbGVtZW50YXRpb24tY3JlYXRlZG9jdW1lbnQgIERPTSBMZXZlbCAyIENvcmVcblx0ICpcblx0ICogQHNlZSBodHRwczovL2RvbS5zcGVjLndoYXR3Zy5vcmcvI3ZhbGlkYXRlLWFuZC1leHRyYWN0IERPTTogVmFsaWRhdGUgYW5kIGV4dHJhY3Rcblx0ICogQHNlZSBodHRwczovL3d3dy53My5vcmcvVFIveG1sLyNOVC1OYW1lU3RhcnRDaGFyIFhNTCBTcGVjOiBOYW1lc1xuXHQgKiBAc2VlIGh0dHBzOi8vd3d3LnczLm9yZy9UUi94bWwtbmFtZXMvI25zLXF1YWxuYW1lcyBYTUwgTmFtZXNwYWNlczogUXVhbGlmaWVkIG5hbWVzXG5cdCAqL1xuXHRjcmVhdGVEb2N1bWVudDogZnVuY3Rpb24obmFtZXNwYWNlVVJJLCAgcXVhbGlmaWVkTmFtZSwgZG9jdHlwZSl7XG5cdFx0dmFyIGRvYyA9IG5ldyBEb2N1bWVudCgpO1xuXHRcdGRvYy5pbXBsZW1lbnRhdGlvbiA9IHRoaXM7XG5cdFx0ZG9jLmNoaWxkTm9kZXMgPSBuZXcgTm9kZUxpc3QoKTtcblx0XHRkb2MuZG9jdHlwZSA9IGRvY3R5cGUgfHwgbnVsbDtcblx0XHRpZiAoZG9jdHlwZSl7XG5cdFx0XHRkb2MuYXBwZW5kQ2hpbGQoZG9jdHlwZSk7XG5cdFx0fVxuXHRcdGlmIChxdWFsaWZpZWROYW1lKXtcblx0XHRcdHZhciByb290ID0gZG9jLmNyZWF0ZUVsZW1lbnROUyhuYW1lc3BhY2VVUkksIHF1YWxpZmllZE5hbWUpO1xuXHRcdFx0ZG9jLmFwcGVuZENoaWxkKHJvb3QpO1xuXHRcdH1cblx0XHRyZXR1cm4gZG9jO1xuXHR9LFxuXHQvKipcblx0ICogUmV0dXJucyBhIGRvY3R5cGUsIHdpdGggdGhlIGdpdmVuIGBxdWFsaWZpZWROYW1lYCwgYHB1YmxpY0lkYCwgYW5kIGBzeXN0ZW1JZGAuXG5cdCAqXG5cdCAqIF9fVGhpcyBiZWhhdmlvciBpcyBzbGlnaHRseSBkaWZmZXJlbnQgZnJvbSB0aGUgaW4gdGhlIHNwZWNzX186XG5cdCAqIC0gdGhpcyBpbXBsZW1lbnRhdGlvbiBpcyBub3QgdmFsaWRhdGluZyBuYW1lcyBvciBxdWFsaWZpZWQgbmFtZXNcblx0ICogICAod2hlbiBwYXJzaW5nIFhNTCBzdHJpbmdzLCB0aGUgU0FYIHBhcnNlciB0YWtlcyBjYXJlIG9mIHRoYXQpXG5cdCAqXG5cdCAqIEBwYXJhbSB7c3RyaW5nfSBxdWFsaWZpZWROYW1lXG5cdCAqIEBwYXJhbSB7c3RyaW5nfSBbcHVibGljSWRdXG5cdCAqIEBwYXJhbSB7c3RyaW5nfSBbc3lzdGVtSWRdXG5cdCAqIEByZXR1cm5zIHtEb2N1bWVudFR5cGV9IHdoaWNoIGNhbiBlaXRoZXIgYmUgdXNlZCB3aXRoIGBET01JbXBsZW1lbnRhdGlvbi5jcmVhdGVEb2N1bWVudGAgdXBvbiBkb2N1bWVudCBjcmVhdGlvblxuXHQgKiBcdFx0XHRcdCAgb3IgY2FuIGJlIHB1dCBpbnRvIHRoZSBkb2N1bWVudCB2aWEgbWV0aG9kcyBsaWtlIGBOb2RlLmluc2VydEJlZm9yZSgpYCBvciBgTm9kZS5yZXBsYWNlQ2hpbGQoKWBcblx0ICpcblx0ICogQHNlZSBodHRwczovL2RldmVsb3Blci5tb3ppbGxhLm9yZy9lbi1VUy9kb2NzL1dlYi9BUEkvRE9NSW1wbGVtZW50YXRpb24vY3JlYXRlRG9jdW1lbnRUeXBlIE1ETlxuXHQgKiBAc2VlIGh0dHBzOi8vd3d3LnczLm9yZy9UUi9ET00tTGV2ZWwtMi1Db3JlL2NvcmUuaHRtbCNMZXZlbC0yLUNvcmUtRE9NLWNyZWF0ZURvY1R5cGUgRE9NIExldmVsIDIgQ29yZVxuXHQgKiBAc2VlIGh0dHBzOi8vZG9tLnNwZWMud2hhdHdnLm9yZy8jZG9tLWRvbWltcGxlbWVudGF0aW9uLWNyZWF0ZWRvY3VtZW50dHlwZSBET00gTGl2aW5nIFN0YW5kYXJkXG5cdCAqXG5cdCAqIEBzZWUgaHR0cHM6Ly9kb20uc3BlYy53aGF0d2cub3JnLyN2YWxpZGF0ZS1hbmQtZXh0cmFjdCBET006IFZhbGlkYXRlIGFuZCBleHRyYWN0XG5cdCAqIEBzZWUgaHR0cHM6Ly93d3cudzMub3JnL1RSL3htbC8jTlQtTmFtZVN0YXJ0Q2hhciBYTUwgU3BlYzogTmFtZXNcblx0ICogQHNlZSBodHRwczovL3d3dy53My5vcmcvVFIveG1sLW5hbWVzLyNucy1xdWFsbmFtZXMgWE1MIE5hbWVzcGFjZXM6IFF1YWxpZmllZCBuYW1lc1xuXHQgKi9cblx0Y3JlYXRlRG9jdW1lbnRUeXBlOiBmdW5jdGlvbihxdWFsaWZpZWROYW1lLCBwdWJsaWNJZCwgc3lzdGVtSWQpe1xuXHRcdHZhciBub2RlID0gbmV3IERvY3VtZW50VHlwZSgpO1xuXHRcdG5vZGUubmFtZSA9IHF1YWxpZmllZE5hbWU7XG5cdFx0bm9kZS5ub2RlTmFtZSA9IHF1YWxpZmllZE5hbWU7XG5cdFx0bm9kZS5wdWJsaWNJZCA9IHB1YmxpY0lkIHx8ICcnO1xuXHRcdG5vZGUuc3lzdGVtSWQgPSBzeXN0ZW1JZCB8fCAnJztcblxuXHRcdHJldHVybiBub2RlO1xuXHR9XG59O1xuXG5cbi8qKlxuICogQHNlZSBodHRwOi8vd3d3LnczLm9yZy9UUi8yMDAwL1JFQy1ET00tTGV2ZWwtMi1Db3JlLTIwMDAxMTEzL2NvcmUuaHRtbCNJRC0xOTUwNjQxMjQ3XG4gKi9cblxuZnVuY3Rpb24gTm9kZSgpIHtcbn07XG5cbk5vZGUucHJvdG90eXBlID0ge1xuXHRmaXJzdENoaWxkIDogbnVsbCxcblx0bGFzdENoaWxkIDogbnVsbCxcblx0cHJldmlvdXNTaWJsaW5nIDogbnVsbCxcblx0bmV4dFNpYmxpbmcgOiBudWxsLFxuXHRhdHRyaWJ1dGVzIDogbnVsbCxcblx0cGFyZW50Tm9kZSA6IG51bGwsXG5cdGNoaWxkTm9kZXMgOiBudWxsLFxuXHRvd25lckRvY3VtZW50IDogbnVsbCxcblx0bm9kZVZhbHVlIDogbnVsbCxcblx0bmFtZXNwYWNlVVJJIDogbnVsbCxcblx0cHJlZml4IDogbnVsbCxcblx0bG9jYWxOYW1lIDogbnVsbCxcblx0Ly8gTW9kaWZpZWQgaW4gRE9NIExldmVsIDI6XG5cdGluc2VydEJlZm9yZTpmdW5jdGlvbihuZXdDaGlsZCwgcmVmQ2hpbGQpey8vcmFpc2VzXG5cdFx0cmV0dXJuIF9pbnNlcnRCZWZvcmUodGhpcyxuZXdDaGlsZCxyZWZDaGlsZCk7XG5cdH0sXG5cdHJlcGxhY2VDaGlsZDpmdW5jdGlvbihuZXdDaGlsZCwgb2xkQ2hpbGQpey8vcmFpc2VzXG5cdFx0X2luc2VydEJlZm9yZSh0aGlzLCBuZXdDaGlsZCxvbGRDaGlsZCwgYXNzZXJ0UHJlUmVwbGFjZW1lbnRWYWxpZGl0eUluRG9jdW1lbnQpO1xuXHRcdGlmKG9sZENoaWxkKXtcblx0XHRcdHRoaXMucmVtb3ZlQ2hpbGQob2xkQ2hpbGQpO1xuXHRcdH1cblx0fSxcblx0cmVtb3ZlQ2hpbGQ6ZnVuY3Rpb24ob2xkQ2hpbGQpe1xuXHRcdHJldHVybiBfcmVtb3ZlQ2hpbGQodGhpcyxvbGRDaGlsZCk7XG5cdH0sXG5cdGFwcGVuZENoaWxkOmZ1bmN0aW9uKG5ld0NoaWxkKXtcblx0XHRyZXR1cm4gdGhpcy5pbnNlcnRCZWZvcmUobmV3Q2hpbGQsbnVsbCk7XG5cdH0sXG5cdGhhc0NoaWxkTm9kZXM6ZnVuY3Rpb24oKXtcblx0XHRyZXR1cm4gdGhpcy5maXJzdENoaWxkICE9IG51bGw7XG5cdH0sXG5cdGNsb25lTm9kZTpmdW5jdGlvbihkZWVwKXtcblx0XHRyZXR1cm4gY2xvbmVOb2RlKHRoaXMub3duZXJEb2N1bWVudHx8dGhpcyx0aGlzLGRlZXApO1xuXHR9LFxuXHQvLyBNb2RpZmllZCBpbiBET00gTGV2ZWwgMjpcblx0bm9ybWFsaXplOmZ1bmN0aW9uKCl7XG5cdFx0dmFyIGNoaWxkID0gdGhpcy5maXJzdENoaWxkO1xuXHRcdHdoaWxlKGNoaWxkKXtcblx0XHRcdHZhciBuZXh0ID0gY2hpbGQubmV4dFNpYmxpbmc7XG5cdFx0XHRpZihuZXh0ICYmIG5leHQubm9kZVR5cGUgPT0gVEVYVF9OT0RFICYmIGNoaWxkLm5vZGVUeXBlID09IFRFWFRfTk9ERSl7XG5cdFx0XHRcdHRoaXMucmVtb3ZlQ2hpbGQobmV4dCk7XG5cdFx0XHRcdGNoaWxkLmFwcGVuZERhdGEobmV4dC5kYXRhKTtcblx0XHRcdH1lbHNle1xuXHRcdFx0XHRjaGlsZC5ub3JtYWxpemUoKTtcblx0XHRcdFx0Y2hpbGQgPSBuZXh0O1xuXHRcdFx0fVxuXHRcdH1cblx0fSxcbiAgXHQvLyBJbnRyb2R1Y2VkIGluIERPTSBMZXZlbCAyOlxuXHRpc1N1cHBvcnRlZDpmdW5jdGlvbihmZWF0dXJlLCB2ZXJzaW9uKXtcblx0XHRyZXR1cm4gdGhpcy5vd25lckRvY3VtZW50LmltcGxlbWVudGF0aW9uLmhhc0ZlYXR1cmUoZmVhdHVyZSx2ZXJzaW9uKTtcblx0fSxcbiAgICAvLyBJbnRyb2R1Y2VkIGluIERPTSBMZXZlbCAyOlxuICAgIGhhc0F0dHJpYnV0ZXM6ZnVuY3Rpb24oKXtcbiAgICBcdHJldHVybiB0aGlzLmF0dHJpYnV0ZXMubGVuZ3RoPjA7XG4gICAgfSxcblx0LyoqXG5cdCAqIExvb2sgdXAgdGhlIHByZWZpeCBhc3NvY2lhdGVkIHRvIHRoZSBnaXZlbiBuYW1lc3BhY2UgVVJJLCBzdGFydGluZyBmcm9tIHRoaXMgbm9kZS5cblx0ICogKipUaGUgZGVmYXVsdCBuYW1lc3BhY2UgZGVjbGFyYXRpb25zIGFyZSBpZ25vcmVkIGJ5IHRoaXMgbWV0aG9kLioqXG5cdCAqIFNlZSBOYW1lc3BhY2UgUHJlZml4IExvb2t1cCBmb3IgZGV0YWlscyBvbiB0aGUgYWxnb3JpdGhtIHVzZWQgYnkgdGhpcyBtZXRob2QuXG5cdCAqXG5cdCAqIF9Ob3RlOiBUaGUgaW1wbGVtZW50YXRpb24gc2VlbXMgdG8gYmUgaW5jb21wbGV0ZSB3aGVuIGNvbXBhcmVkIHRvIHRoZSBhbGdvcml0aG0gZGVzY3JpYmVkIGluIHRoZSBzcGVjcy5fXG5cdCAqXG5cdCAqIEBwYXJhbSB7c3RyaW5nIHwgbnVsbH0gbmFtZXNwYWNlVVJJXG5cdCAqIEByZXR1cm5zIHtzdHJpbmcgfCBudWxsfVxuXHQgKiBAc2VlIGh0dHBzOi8vd3d3LnczLm9yZy9UUi9ET00tTGV2ZWwtMy1Db3JlL2NvcmUuaHRtbCNOb2RlMy1sb29rdXBOYW1lc3BhY2VQcmVmaXhcblx0ICogQHNlZSBodHRwczovL3d3dy53My5vcmcvVFIvRE9NLUxldmVsLTMtQ29yZS9uYW1lc3BhY2VzLWFsZ29yaXRobXMuaHRtbCNsb29rdXBOYW1lc3BhY2VQcmVmaXhBbGdvXG5cdCAqIEBzZWUgaHR0cHM6Ly9kb20uc3BlYy53aGF0d2cub3JnLyNkb20tbm9kZS1sb29rdXBwcmVmaXhcblx0ICogQHNlZSBodHRwczovL2dpdGh1Yi5jb20veG1sZG9tL3htbGRvbS9pc3N1ZXMvMzIyXG5cdCAqL1xuICAgIGxvb2t1cFByZWZpeDpmdW5jdGlvbihuYW1lc3BhY2VVUkkpe1xuICAgIFx0dmFyIGVsID0gdGhpcztcbiAgICBcdHdoaWxlKGVsKXtcbiAgICBcdFx0dmFyIG1hcCA9IGVsLl9uc01hcDtcbiAgICBcdFx0Ly9jb25zb2xlLmRpcihtYXApXG4gICAgXHRcdGlmKG1hcCl7XG4gICAgXHRcdFx0Zm9yKHZhciBuIGluIG1hcCl7XG5cdFx0XHRcdFx0XHRpZiAoT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKG1hcCwgbikgJiYgbWFwW25dID09PSBuYW1lc3BhY2VVUkkpIHtcblx0XHRcdFx0XHRcdFx0cmV0dXJuIG47XG5cdFx0XHRcdFx0XHR9XG4gICAgXHRcdFx0fVxuICAgIFx0XHR9XG4gICAgXHRcdGVsID0gZWwubm9kZVR5cGUgPT0gQVRUUklCVVRFX05PREU/ZWwub3duZXJEb2N1bWVudCA6IGVsLnBhcmVudE5vZGU7XG4gICAgXHR9XG4gICAgXHRyZXR1cm4gbnVsbDtcbiAgICB9LFxuICAgIC8vIEludHJvZHVjZWQgaW4gRE9NIExldmVsIDM6XG4gICAgbG9va3VwTmFtZXNwYWNlVVJJOmZ1bmN0aW9uKHByZWZpeCl7XG4gICAgXHR2YXIgZWwgPSB0aGlzO1xuICAgIFx0d2hpbGUoZWwpe1xuICAgIFx0XHR2YXIgbWFwID0gZWwuX25zTWFwO1xuICAgIFx0XHQvL2NvbnNvbGUuZGlyKG1hcClcbiAgICBcdFx0aWYobWFwKXtcbiAgICBcdFx0XHRpZihPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwobWFwLCBwcmVmaXgpKXtcbiAgICBcdFx0XHRcdHJldHVybiBtYXBbcHJlZml4XSA7XG4gICAgXHRcdFx0fVxuICAgIFx0XHR9XG4gICAgXHRcdGVsID0gZWwubm9kZVR5cGUgPT0gQVRUUklCVVRFX05PREU/ZWwub3duZXJEb2N1bWVudCA6IGVsLnBhcmVudE5vZGU7XG4gICAgXHR9XG4gICAgXHRyZXR1cm4gbnVsbDtcbiAgICB9LFxuICAgIC8vIEludHJvZHVjZWQgaW4gRE9NIExldmVsIDM6XG4gICAgaXNEZWZhdWx0TmFtZXNwYWNlOmZ1bmN0aW9uKG5hbWVzcGFjZVVSSSl7XG4gICAgXHR2YXIgcHJlZml4ID0gdGhpcy5sb29rdXBQcmVmaXgobmFtZXNwYWNlVVJJKTtcbiAgICBcdHJldHVybiBwcmVmaXggPT0gbnVsbDtcbiAgICB9XG59O1xuXG5cbmZ1bmN0aW9uIF94bWxFbmNvZGVyKGMpe1xuXHRyZXR1cm4gYyA9PSAnPCcgJiYgJyZsdDsnIHx8XG4gICAgICAgICBjID09ICc+JyAmJiAnJmd0OycgfHxcbiAgICAgICAgIGMgPT0gJyYnICYmICcmYW1wOycgfHxcbiAgICAgICAgIGMgPT0gJ1wiJyAmJiAnJnF1b3Q7JyB8fFxuICAgICAgICAgJyYjJytjLmNoYXJDb2RlQXQoKSsnOydcbn1cblxuXG5jb3B5KE5vZGVUeXBlLE5vZGUpO1xuY29weShOb2RlVHlwZSxOb2RlLnByb3RvdHlwZSk7XG5cbi8qKlxuICogQHBhcmFtIGNhbGxiYWNrIHJldHVybiB0cnVlIGZvciBjb250aW51ZSxmYWxzZSBmb3IgYnJlYWtcbiAqIEByZXR1cm4gYm9vbGVhbiB0cnVlOiBicmVhayB2aXNpdDtcbiAqL1xuZnVuY3Rpb24gX3Zpc2l0Tm9kZShub2RlLGNhbGxiYWNrKXtcblx0aWYoY2FsbGJhY2sobm9kZSkpe1xuXHRcdHJldHVybiB0cnVlO1xuXHR9XG5cdGlmKG5vZGUgPSBub2RlLmZpcnN0Q2hpbGQpe1xuXHRcdGRve1xuXHRcdFx0aWYoX3Zpc2l0Tm9kZShub2RlLGNhbGxiYWNrKSl7cmV0dXJuIHRydWV9XG4gICAgICAgIH13aGlsZShub2RlPW5vZGUubmV4dFNpYmxpbmcpXG4gICAgfVxufVxuXG5cblxuZnVuY3Rpb24gRG9jdW1lbnQoKXtcblx0dGhpcy5vd25lckRvY3VtZW50ID0gdGhpcztcbn1cblxuZnVuY3Rpb24gX29uQWRkQXR0cmlidXRlKGRvYyxlbCxuZXdBdHRyKXtcblx0ZG9jICYmIGRvYy5faW5jKys7XG5cdHZhciBucyA9IG5ld0F0dHIubmFtZXNwYWNlVVJJIDtcblx0aWYobnMgPT09IE5BTUVTUEFDRS5YTUxOUyl7XG5cdFx0Ly91cGRhdGUgbmFtZXNwYWNlXG5cdFx0ZWwuX25zTWFwW25ld0F0dHIucHJlZml4P25ld0F0dHIubG9jYWxOYW1lOicnXSA9IG5ld0F0dHIudmFsdWVcblx0fVxufVxuXG5mdW5jdGlvbiBfb25SZW1vdmVBdHRyaWJ1dGUoZG9jLGVsLG5ld0F0dHIscmVtb3ZlKXtcblx0ZG9jICYmIGRvYy5faW5jKys7XG5cdHZhciBucyA9IG5ld0F0dHIubmFtZXNwYWNlVVJJIDtcblx0aWYobnMgPT09IE5BTUVTUEFDRS5YTUxOUyl7XG5cdFx0Ly91cGRhdGUgbmFtZXNwYWNlXG5cdFx0ZGVsZXRlIGVsLl9uc01hcFtuZXdBdHRyLnByZWZpeD9uZXdBdHRyLmxvY2FsTmFtZTonJ11cblx0fVxufVxuXG4vKipcbiAqIFVwZGF0ZXMgYGVsLmNoaWxkTm9kZXNgLCB1cGRhdGluZyB0aGUgaW5kZXhlZCBpdGVtcyBhbmQgaXQncyBgbGVuZ3RoYC5cbiAqIFBhc3NpbmcgYG5ld0NoaWxkYCBtZWFucyBpdCB3aWxsIGJlIGFwcGVuZGVkLlxuICogT3RoZXJ3aXNlIGl0J3MgYXNzdW1lZCB0aGF0IGFuIGl0ZW0gaGFzIGJlZW4gcmVtb3ZlZCxcbiAqIGFuZCBgZWwuZmlyc3ROb2RlYCBhbmQgaXQncyBgLm5leHRTaWJsaW5nYCBhcmUgdXNlZFxuICogdG8gd2FsayB0aGUgY3VycmVudCBsaXN0IG9mIGNoaWxkIG5vZGVzLlxuICpcbiAqIEBwYXJhbSB7RG9jdW1lbnR9IGRvY1xuICogQHBhcmFtIHtOb2RlfSBlbFxuICogQHBhcmFtIHtOb2RlfSBbbmV3Q2hpbGRdXG4gKiBAcHJpdmF0ZVxuICovXG5mdW5jdGlvbiBfb25VcGRhdGVDaGlsZCAoZG9jLCBlbCwgbmV3Q2hpbGQpIHtcblx0aWYoZG9jICYmIGRvYy5faW5jKXtcblx0XHRkb2MuX2luYysrO1xuXHRcdC8vdXBkYXRlIGNoaWxkTm9kZXNcblx0XHR2YXIgY3MgPSBlbC5jaGlsZE5vZGVzO1xuXHRcdGlmIChuZXdDaGlsZCkge1xuXHRcdFx0Y3NbY3MubGVuZ3RoKytdID0gbmV3Q2hpbGQ7XG5cdFx0fSBlbHNlIHtcblx0XHRcdHZhciBjaGlsZCA9IGVsLmZpcnN0Q2hpbGQ7XG5cdFx0XHR2YXIgaSA9IDA7XG5cdFx0XHR3aGlsZSAoY2hpbGQpIHtcblx0XHRcdFx0Y3NbaSsrXSA9IGNoaWxkO1xuXHRcdFx0XHRjaGlsZCA9IGNoaWxkLm5leHRTaWJsaW5nO1xuXHRcdFx0fVxuXHRcdFx0Y3MubGVuZ3RoID0gaTtcblx0XHRcdGRlbGV0ZSBjc1tjcy5sZW5ndGhdO1xuXHRcdH1cblx0fVxufVxuXG4vKipcbiAqIFJlbW92ZXMgdGhlIGNvbm5lY3Rpb25zIGJldHdlZW4gYHBhcmVudE5vZGVgIGFuZCBgY2hpbGRgXG4gKiBhbmQgYW55IGV4aXN0aW5nIGBjaGlsZC5wcmV2aW91c1NpYmxpbmdgIG9yIGBjaGlsZC5uZXh0U2libGluZ2AuXG4gKlxuICogQHNlZSBodHRwczovL2dpdGh1Yi5jb20veG1sZG9tL3htbGRvbS9pc3N1ZXMvMTM1XG4gKiBAc2VlIGh0dHBzOi8vZ2l0aHViLmNvbS94bWxkb20veG1sZG9tL2lzc3Vlcy8xNDVcbiAqXG4gKiBAcGFyYW0ge05vZGV9IHBhcmVudE5vZGVcbiAqIEBwYXJhbSB7Tm9kZX0gY2hpbGRcbiAqIEByZXR1cm5zIHtOb2RlfSB0aGUgY2hpbGQgdGhhdCB3YXMgcmVtb3ZlZC5cbiAqIEBwcml2YXRlXG4gKi9cbmZ1bmN0aW9uIF9yZW1vdmVDaGlsZCAocGFyZW50Tm9kZSwgY2hpbGQpIHtcblx0dmFyIHByZXZpb3VzID0gY2hpbGQucHJldmlvdXNTaWJsaW5nO1xuXHR2YXIgbmV4dCA9IGNoaWxkLm5leHRTaWJsaW5nO1xuXHRpZiAocHJldmlvdXMpIHtcblx0XHRwcmV2aW91cy5uZXh0U2libGluZyA9IG5leHQ7XG5cdH0gZWxzZSB7XG5cdFx0cGFyZW50Tm9kZS5maXJzdENoaWxkID0gbmV4dDtcblx0fVxuXHRpZiAobmV4dCkge1xuXHRcdG5leHQucHJldmlvdXNTaWJsaW5nID0gcHJldmlvdXM7XG5cdH0gZWxzZSB7XG5cdFx0cGFyZW50Tm9kZS5sYXN0Q2hpbGQgPSBwcmV2aW91cztcblx0fVxuXHRjaGlsZC5wYXJlbnROb2RlID0gbnVsbDtcblx0Y2hpbGQucHJldmlvdXNTaWJsaW5nID0gbnVsbDtcblx0Y2hpbGQubmV4dFNpYmxpbmcgPSBudWxsO1xuXHRfb25VcGRhdGVDaGlsZChwYXJlbnROb2RlLm93bmVyRG9jdW1lbnQsIHBhcmVudE5vZGUpO1xuXHRyZXR1cm4gY2hpbGQ7XG59XG5cbi8qKlxuICogUmV0dXJucyBgdHJ1ZWAgaWYgYG5vZGVgIGNhbiBiZSBhIHBhcmVudCBmb3IgaW5zZXJ0aW9uLlxuICogQHBhcmFtIHtOb2RlfSBub2RlXG4gKiBAcmV0dXJucyB7Ym9vbGVhbn1cbiAqL1xuZnVuY3Rpb24gaGFzVmFsaWRQYXJlbnROb2RlVHlwZShub2RlKSB7XG5cdHJldHVybiAoXG5cdFx0bm9kZSAmJlxuXHRcdChub2RlLm5vZGVUeXBlID09PSBOb2RlLkRPQ1VNRU5UX05PREUgfHwgbm9kZS5ub2RlVHlwZSA9PT0gTm9kZS5ET0NVTUVOVF9GUkFHTUVOVF9OT0RFIHx8IG5vZGUubm9kZVR5cGUgPT09IE5vZGUuRUxFTUVOVF9OT0RFKVxuXHQpO1xufVxuXG4vKipcbiAqIFJldHVybnMgYHRydWVgIGlmIGBub2RlYCBjYW4gYmUgaW5zZXJ0ZWQgYWNjb3JkaW5nIHRvIGl0J3MgYG5vZGVUeXBlYC5cbiAqIEBwYXJhbSB7Tm9kZX0gbm9kZVxuICogQHJldHVybnMge2Jvb2xlYW59XG4gKi9cbmZ1bmN0aW9uIGhhc0luc2VydGFibGVOb2RlVHlwZShub2RlKSB7XG5cdHJldHVybiAoXG5cdFx0bm9kZSAmJlxuXHRcdChpc0VsZW1lbnROb2RlKG5vZGUpIHx8XG5cdFx0XHRpc1RleHROb2RlKG5vZGUpIHx8XG5cdFx0XHRpc0RvY1R5cGVOb2RlKG5vZGUpIHx8XG5cdFx0XHRub2RlLm5vZGVUeXBlID09PSBOb2RlLkRPQ1VNRU5UX0ZSQUdNRU5UX05PREUgfHxcblx0XHRcdG5vZGUubm9kZVR5cGUgPT09IE5vZGUuQ09NTUVOVF9OT0RFIHx8XG5cdFx0XHRub2RlLm5vZGVUeXBlID09PSBOb2RlLlBST0NFU1NJTkdfSU5TVFJVQ1RJT05fTk9ERSlcblx0KTtcbn1cblxuLyoqXG4gKiBSZXR1cm5zIHRydWUgaWYgYG5vZGVgIGlzIGEgRE9DVFlQRSBub2RlXG4gKiBAcGFyYW0ge05vZGV9IG5vZGVcbiAqIEByZXR1cm5zIHtib29sZWFufVxuICovXG5mdW5jdGlvbiBpc0RvY1R5cGVOb2RlKG5vZGUpIHtcblx0cmV0dXJuIG5vZGUgJiYgbm9kZS5ub2RlVHlwZSA9PT0gTm9kZS5ET0NVTUVOVF9UWVBFX05PREU7XG59XG5cbi8qKlxuICogUmV0dXJucyB0cnVlIGlmIHRoZSBub2RlIGlzIGFuIGVsZW1lbnRcbiAqIEBwYXJhbSB7Tm9kZX0gbm9kZVxuICogQHJldHVybnMge2Jvb2xlYW59XG4gKi9cbmZ1bmN0aW9uIGlzRWxlbWVudE5vZGUobm9kZSkge1xuXHRyZXR1cm4gbm9kZSAmJiBub2RlLm5vZGVUeXBlID09PSBOb2RlLkVMRU1FTlRfTk9ERTtcbn1cbi8qKlxuICogUmV0dXJucyB0cnVlIGlmIGBub2RlYCBpcyBhIHRleHQgbm9kZVxuICogQHBhcmFtIHtOb2RlfSBub2RlXG4gKiBAcmV0dXJucyB7Ym9vbGVhbn1cbiAqL1xuZnVuY3Rpb24gaXNUZXh0Tm9kZShub2RlKSB7XG5cdHJldHVybiBub2RlICYmIG5vZGUubm9kZVR5cGUgPT09IE5vZGUuVEVYVF9OT0RFO1xufVxuXG4vKipcbiAqIENoZWNrIGlmIGVuIGVsZW1lbnQgbm9kZSBjYW4gYmUgaW5zZXJ0ZWQgYmVmb3JlIGBjaGlsZGAsIG9yIGF0IHRoZSBlbmQgaWYgY2hpbGQgaXMgZmFsc3ksXG4gKiBhY2NvcmRpbmcgdG8gdGhlIHByZXNlbmNlIGFuZCBwb3NpdGlvbiBvZiBhIGRvY3R5cGUgbm9kZSBvbiB0aGUgc2FtZSBsZXZlbC5cbiAqXG4gKiBAcGFyYW0ge0RvY3VtZW50fSBkb2MgVGhlIGRvY3VtZW50IG5vZGVcbiAqIEBwYXJhbSB7Tm9kZX0gY2hpbGQgdGhlIG5vZGUgdGhhdCB3b3VsZCBiZWNvbWUgdGhlIG5leHRTaWJsaW5nIGlmIHRoZSBlbGVtZW50IHdvdWxkIGJlIGluc2VydGVkXG4gKiBAcmV0dXJucyB7Ym9vbGVhbn0gYHRydWVgIGlmIGFuIGVsZW1lbnQgY2FuIGJlIGluc2VydGVkIGJlZm9yZSBjaGlsZFxuICogQHByaXZhdGVcbiAqIGh0dHBzOi8vZG9tLnNwZWMud2hhdHdnLm9yZy8jY29uY2VwdC1ub2RlLWVuc3VyZS1wcmUtaW5zZXJ0aW9uLXZhbGlkaXR5XG4gKi9cbmZ1bmN0aW9uIGlzRWxlbWVudEluc2VydGlvblBvc3NpYmxlKGRvYywgY2hpbGQpIHtcblx0dmFyIHBhcmVudENoaWxkTm9kZXMgPSBkb2MuY2hpbGROb2RlcyB8fCBbXTtcblx0aWYgKGZpbmQocGFyZW50Q2hpbGROb2RlcywgaXNFbGVtZW50Tm9kZSkgfHwgaXNEb2NUeXBlTm9kZShjaGlsZCkpIHtcblx0XHRyZXR1cm4gZmFsc2U7XG5cdH1cblx0dmFyIGRvY1R5cGVOb2RlID0gZmluZChwYXJlbnRDaGlsZE5vZGVzLCBpc0RvY1R5cGVOb2RlKTtcblx0cmV0dXJuICEoY2hpbGQgJiYgZG9jVHlwZU5vZGUgJiYgcGFyZW50Q2hpbGROb2Rlcy5pbmRleE9mKGRvY1R5cGVOb2RlKSA+IHBhcmVudENoaWxkTm9kZXMuaW5kZXhPZihjaGlsZCkpO1xufVxuXG4vKipcbiAqIENoZWNrIGlmIGVuIGVsZW1lbnQgbm9kZSBjYW4gYmUgaW5zZXJ0ZWQgYmVmb3JlIGBjaGlsZGAsIG9yIGF0IHRoZSBlbmQgaWYgY2hpbGQgaXMgZmFsc3ksXG4gKiBhY2NvcmRpbmcgdG8gdGhlIHByZXNlbmNlIGFuZCBwb3NpdGlvbiBvZiBhIGRvY3R5cGUgbm9kZSBvbiB0aGUgc2FtZSBsZXZlbC5cbiAqXG4gKiBAcGFyYW0ge05vZGV9IGRvYyBUaGUgZG9jdW1lbnQgbm9kZVxuICogQHBhcmFtIHtOb2RlfSBjaGlsZCB0aGUgbm9kZSB0aGF0IHdvdWxkIGJlY29tZSB0aGUgbmV4dFNpYmxpbmcgaWYgdGhlIGVsZW1lbnQgd291bGQgYmUgaW5zZXJ0ZWRcbiAqIEByZXR1cm5zIHtib29sZWFufSBgdHJ1ZWAgaWYgYW4gZWxlbWVudCBjYW4gYmUgaW5zZXJ0ZWQgYmVmb3JlIGNoaWxkXG4gKiBAcHJpdmF0ZVxuICogaHR0cHM6Ly9kb20uc3BlYy53aGF0d2cub3JnLyNjb25jZXB0LW5vZGUtZW5zdXJlLXByZS1pbnNlcnRpb24tdmFsaWRpdHlcbiAqL1xuZnVuY3Rpb24gaXNFbGVtZW50UmVwbGFjZW1lbnRQb3NzaWJsZShkb2MsIGNoaWxkKSB7XG5cdHZhciBwYXJlbnRDaGlsZE5vZGVzID0gZG9jLmNoaWxkTm9kZXMgfHwgW107XG5cblx0ZnVuY3Rpb24gaGFzRWxlbWVudENoaWxkVGhhdElzTm90Q2hpbGQobm9kZSkge1xuXHRcdHJldHVybiBpc0VsZW1lbnROb2RlKG5vZGUpICYmIG5vZGUgIT09IGNoaWxkO1xuXHR9XG5cblx0aWYgKGZpbmQocGFyZW50Q2hpbGROb2RlcywgaGFzRWxlbWVudENoaWxkVGhhdElzTm90Q2hpbGQpKSB7XG5cdFx0cmV0dXJuIGZhbHNlO1xuXHR9XG5cdHZhciBkb2NUeXBlTm9kZSA9IGZpbmQocGFyZW50Q2hpbGROb2RlcywgaXNEb2NUeXBlTm9kZSk7XG5cdHJldHVybiAhKGNoaWxkICYmIGRvY1R5cGVOb2RlICYmIHBhcmVudENoaWxkTm9kZXMuaW5kZXhPZihkb2NUeXBlTm9kZSkgPiBwYXJlbnRDaGlsZE5vZGVzLmluZGV4T2YoY2hpbGQpKTtcbn1cblxuLyoqXG4gKiBAcHJpdmF0ZVxuICogU3RlcHMgMS01IG9mIHRoZSBjaGVja3MgYmVmb3JlIGluc2VydGluZyBhbmQgYmVmb3JlIHJlcGxhY2luZyBhIGNoaWxkIGFyZSB0aGUgc2FtZS5cbiAqXG4gKiBAcGFyYW0ge05vZGV9IHBhcmVudCB0aGUgcGFyZW50IG5vZGUgdG8gaW5zZXJ0IGBub2RlYCBpbnRvXG4gKiBAcGFyYW0ge05vZGV9IG5vZGUgdGhlIG5vZGUgdG8gaW5zZXJ0XG4gKiBAcGFyYW0ge05vZGU9fSBjaGlsZCB0aGUgbm9kZSB0aGF0IHNob3VsZCBiZWNvbWUgdGhlIGBuZXh0U2libGluZ2Agb2YgYG5vZGVgXG4gKiBAcmV0dXJucyB7Tm9kZX1cbiAqIEB0aHJvd3MgRE9NRXhjZXB0aW9uIGZvciBzZXZlcmFsIG5vZGUgY29tYmluYXRpb25zIHRoYXQgd291bGQgY3JlYXRlIGEgRE9NIHRoYXQgaXMgbm90IHdlbGwtZm9ybWVkLlxuICogQHRocm93cyBET01FeGNlcHRpb24gaWYgYGNoaWxkYCBpcyBwcm92aWRlZCBidXQgaXMgbm90IGEgY2hpbGQgb2YgYHBhcmVudGAuXG4gKiBAc2VlIGh0dHBzOi8vZG9tLnNwZWMud2hhdHdnLm9yZy8jY29uY2VwdC1ub2RlLWVuc3VyZS1wcmUtaW5zZXJ0aW9uLXZhbGlkaXR5XG4gKiBAc2VlIGh0dHBzOi8vZG9tLnNwZWMud2hhdHdnLm9yZy8jY29uY2VwdC1ub2RlLXJlcGxhY2VcbiAqL1xuZnVuY3Rpb24gYXNzZXJ0UHJlSW5zZXJ0aW9uVmFsaWRpdHkxdG81KHBhcmVudCwgbm9kZSwgY2hpbGQpIHtcblx0Ly8gMS4gSWYgYHBhcmVudGAgaXMgbm90IGEgRG9jdW1lbnQsIERvY3VtZW50RnJhZ21lbnQsIG9yIEVsZW1lbnQgbm9kZSwgdGhlbiB0aHJvdyBhIFwiSGllcmFyY2h5UmVxdWVzdEVycm9yXCIgRE9NRXhjZXB0aW9uLlxuXHRpZiAoIWhhc1ZhbGlkUGFyZW50Tm9kZVR5cGUocGFyZW50KSkge1xuXHRcdHRocm93IG5ldyBET01FeGNlcHRpb24oSElFUkFSQ0hZX1JFUVVFU1RfRVJSLCAnVW5leHBlY3RlZCBwYXJlbnQgbm9kZSB0eXBlICcgKyBwYXJlbnQubm9kZVR5cGUpO1xuXHR9XG5cdC8vIDIuIElmIGBub2RlYCBpcyBhIGhvc3QtaW5jbHVkaW5nIGluY2x1c2l2ZSBhbmNlc3RvciBvZiBgcGFyZW50YCwgdGhlbiB0aHJvdyBhIFwiSGllcmFyY2h5UmVxdWVzdEVycm9yXCIgRE9NRXhjZXB0aW9uLlxuXHQvLyBub3QgaW1wbGVtZW50ZWQhXG5cdC8vIDMuIElmIGBjaGlsZGAgaXMgbm9uLW51bGwgYW5kIGl0cyBwYXJlbnQgaXMgbm90IGBwYXJlbnRgLCB0aGVuIHRocm93IGEgXCJOb3RGb3VuZEVycm9yXCIgRE9NRXhjZXB0aW9uLlxuXHRpZiAoY2hpbGQgJiYgY2hpbGQucGFyZW50Tm9kZSAhPT0gcGFyZW50KSB7XG5cdFx0dGhyb3cgbmV3IERPTUV4Y2VwdGlvbihOT1RfRk9VTkRfRVJSLCAnY2hpbGQgbm90IGluIHBhcmVudCcpO1xuXHR9XG5cdGlmIChcblx0XHQvLyA0LiBJZiBgbm9kZWAgaXMgbm90IGEgRG9jdW1lbnRGcmFnbWVudCwgRG9jdW1lbnRUeXBlLCBFbGVtZW50LCBvciBDaGFyYWN0ZXJEYXRhIG5vZGUsIHRoZW4gdGhyb3cgYSBcIkhpZXJhcmNoeVJlcXVlc3RFcnJvclwiIERPTUV4Y2VwdGlvbi5cblx0XHQhaGFzSW5zZXJ0YWJsZU5vZGVUeXBlKG5vZGUpIHx8XG5cdFx0Ly8gNS4gSWYgZWl0aGVyIGBub2RlYCBpcyBhIFRleHQgbm9kZSBhbmQgYHBhcmVudGAgaXMgYSBkb2N1bWVudCxcblx0XHQvLyB0aGUgc2F4IHBhcnNlciBjdXJyZW50bHkgYWRkcyB0b3AgbGV2ZWwgdGV4dCBub2RlcywgdGhpcyB3aWxsIGJlIGZpeGVkIGluIDAuOS4wXG5cdFx0Ly8gfHwgKG5vZGUubm9kZVR5cGUgPT09IE5vZGUuVEVYVF9OT0RFICYmIHBhcmVudC5ub2RlVHlwZSA9PT0gTm9kZS5ET0NVTUVOVF9OT0RFKVxuXHRcdC8vIG9yIGBub2RlYCBpcyBhIGRvY3R5cGUgYW5kIGBwYXJlbnRgIGlzIG5vdCBhIGRvY3VtZW50LCB0aGVuIHRocm93IGEgXCJIaWVyYXJjaHlSZXF1ZXN0RXJyb3JcIiBET01FeGNlcHRpb24uXG5cdFx0KGlzRG9jVHlwZU5vZGUobm9kZSkgJiYgcGFyZW50Lm5vZGVUeXBlICE9PSBOb2RlLkRPQ1VNRU5UX05PREUpXG5cdCkge1xuXHRcdHRocm93IG5ldyBET01FeGNlcHRpb24oXG5cdFx0XHRISUVSQVJDSFlfUkVRVUVTVF9FUlIsXG5cdFx0XHQnVW5leHBlY3RlZCBub2RlIHR5cGUgJyArIG5vZGUubm9kZVR5cGUgKyAnIGZvciBwYXJlbnQgbm9kZSB0eXBlICcgKyBwYXJlbnQubm9kZVR5cGVcblx0XHQpO1xuXHR9XG59XG5cbi8qKlxuICogQHByaXZhdGVcbiAqIFN0ZXAgNiBvZiB0aGUgY2hlY2tzIGJlZm9yZSBpbnNlcnRpbmcgYW5kIGJlZm9yZSByZXBsYWNpbmcgYSBjaGlsZCBhcmUgZGlmZmVyZW50LlxuICpcbiAqIEBwYXJhbSB7RG9jdW1lbnR9IHBhcmVudCB0aGUgcGFyZW50IG5vZGUgdG8gaW5zZXJ0IGBub2RlYCBpbnRvXG4gKiBAcGFyYW0ge05vZGV9IG5vZGUgdGhlIG5vZGUgdG8gaW5zZXJ0XG4gKiBAcGFyYW0ge05vZGUgfCB1bmRlZmluZWR9IGNoaWxkIHRoZSBub2RlIHRoYXQgc2hvdWxkIGJlY29tZSB0aGUgYG5leHRTaWJsaW5nYCBvZiBgbm9kZWBcbiAqIEByZXR1cm5zIHtOb2RlfVxuICogQHRocm93cyBET01FeGNlcHRpb24gZm9yIHNldmVyYWwgbm9kZSBjb21iaW5hdGlvbnMgdGhhdCB3b3VsZCBjcmVhdGUgYSBET00gdGhhdCBpcyBub3Qgd2VsbC1mb3JtZWQuXG4gKiBAdGhyb3dzIERPTUV4Y2VwdGlvbiBpZiBgY2hpbGRgIGlzIHByb3ZpZGVkIGJ1dCBpcyBub3QgYSBjaGlsZCBvZiBgcGFyZW50YC5cbiAqIEBzZWUgaHR0cHM6Ly9kb20uc3BlYy53aGF0d2cub3JnLyNjb25jZXB0LW5vZGUtZW5zdXJlLXByZS1pbnNlcnRpb24tdmFsaWRpdHlcbiAqIEBzZWUgaHR0cHM6Ly9kb20uc3BlYy53aGF0d2cub3JnLyNjb25jZXB0LW5vZGUtcmVwbGFjZVxuICovXG5mdW5jdGlvbiBhc3NlcnRQcmVJbnNlcnRpb25WYWxpZGl0eUluRG9jdW1lbnQocGFyZW50LCBub2RlLCBjaGlsZCkge1xuXHR2YXIgcGFyZW50Q2hpbGROb2RlcyA9IHBhcmVudC5jaGlsZE5vZGVzIHx8IFtdO1xuXHR2YXIgbm9kZUNoaWxkTm9kZXMgPSBub2RlLmNoaWxkTm9kZXMgfHwgW107XG5cblx0Ly8gRG9jdW1lbnRGcmFnbWVudFxuXHRpZiAobm9kZS5ub2RlVHlwZSA9PT0gTm9kZS5ET0NVTUVOVF9GUkFHTUVOVF9OT0RFKSB7XG5cdFx0dmFyIG5vZGVDaGlsZEVsZW1lbnRzID0gbm9kZUNoaWxkTm9kZXMuZmlsdGVyKGlzRWxlbWVudE5vZGUpO1xuXHRcdC8vIElmIG5vZGUgaGFzIG1vcmUgdGhhbiBvbmUgZWxlbWVudCBjaGlsZCBvciBoYXMgYSBUZXh0IG5vZGUgY2hpbGQuXG5cdFx0aWYgKG5vZGVDaGlsZEVsZW1lbnRzLmxlbmd0aCA+IDEgfHwgZmluZChub2RlQ2hpbGROb2RlcywgaXNUZXh0Tm9kZSkpIHtcblx0XHRcdHRocm93IG5ldyBET01FeGNlcHRpb24oSElFUkFSQ0hZX1JFUVVFU1RfRVJSLCAnTW9yZSB0aGFuIG9uZSBlbGVtZW50IG9yIHRleHQgaW4gZnJhZ21lbnQnKTtcblx0XHR9XG5cdFx0Ly8gT3RoZXJ3aXNlLCBpZiBgbm9kZWAgaGFzIG9uZSBlbGVtZW50IGNoaWxkIGFuZCBlaXRoZXIgYHBhcmVudGAgaGFzIGFuIGVsZW1lbnQgY2hpbGQsXG5cdFx0Ly8gYGNoaWxkYCBpcyBhIGRvY3R5cGUsIG9yIGBjaGlsZGAgaXMgbm9uLW51bGwgYW5kIGEgZG9jdHlwZSBpcyBmb2xsb3dpbmcgYGNoaWxkYC5cblx0XHRpZiAobm9kZUNoaWxkRWxlbWVudHMubGVuZ3RoID09PSAxICYmICFpc0VsZW1lbnRJbnNlcnRpb25Qb3NzaWJsZShwYXJlbnQsIGNoaWxkKSkge1xuXHRcdFx0dGhyb3cgbmV3IERPTUV4Y2VwdGlvbihISUVSQVJDSFlfUkVRVUVTVF9FUlIsICdFbGVtZW50IGluIGZyYWdtZW50IGNhbiBub3QgYmUgaW5zZXJ0ZWQgYmVmb3JlIGRvY3R5cGUnKTtcblx0XHR9XG5cdH1cblx0Ly8gRWxlbWVudFxuXHRpZiAoaXNFbGVtZW50Tm9kZShub2RlKSkge1xuXHRcdC8vIGBwYXJlbnRgIGhhcyBhbiBlbGVtZW50IGNoaWxkLCBgY2hpbGRgIGlzIGEgZG9jdHlwZSxcblx0XHQvLyBvciBgY2hpbGRgIGlzIG5vbi1udWxsIGFuZCBhIGRvY3R5cGUgaXMgZm9sbG93aW5nIGBjaGlsZGAuXG5cdFx0aWYgKCFpc0VsZW1lbnRJbnNlcnRpb25Qb3NzaWJsZShwYXJlbnQsIGNoaWxkKSkge1xuXHRcdFx0dGhyb3cgbmV3IERPTUV4Y2VwdGlvbihISUVSQVJDSFlfUkVRVUVTVF9FUlIsICdPbmx5IG9uZSBlbGVtZW50IGNhbiBiZSBhZGRlZCBhbmQgb25seSBhZnRlciBkb2N0eXBlJyk7XG5cdFx0fVxuXHR9XG5cdC8vIERvY3VtZW50VHlwZVxuXHRpZiAoaXNEb2NUeXBlTm9kZShub2RlKSkge1xuXHRcdC8vIGBwYXJlbnRgIGhhcyBhIGRvY3R5cGUgY2hpbGQsXG5cdFx0aWYgKGZpbmQocGFyZW50Q2hpbGROb2RlcywgaXNEb2NUeXBlTm9kZSkpIHtcblx0XHRcdHRocm93IG5ldyBET01FeGNlcHRpb24oSElFUkFSQ0hZX1JFUVVFU1RfRVJSLCAnT25seSBvbmUgZG9jdHlwZSBpcyBhbGxvd2VkJyk7XG5cdFx0fVxuXHRcdHZhciBwYXJlbnRFbGVtZW50Q2hpbGQgPSBmaW5kKHBhcmVudENoaWxkTm9kZXMsIGlzRWxlbWVudE5vZGUpO1xuXHRcdC8vIGBjaGlsZGAgaXMgbm9uLW51bGwgYW5kIGFuIGVsZW1lbnQgaXMgcHJlY2VkaW5nIGBjaGlsZGAsXG5cdFx0aWYgKGNoaWxkICYmIHBhcmVudENoaWxkTm9kZXMuaW5kZXhPZihwYXJlbnRFbGVtZW50Q2hpbGQpIDwgcGFyZW50Q2hpbGROb2Rlcy5pbmRleE9mKGNoaWxkKSkge1xuXHRcdFx0dGhyb3cgbmV3IERPTUV4Y2VwdGlvbihISUVSQVJDSFlfUkVRVUVTVF9FUlIsICdEb2N0eXBlIGNhbiBvbmx5IGJlIGluc2VydGVkIGJlZm9yZSBhbiBlbGVtZW50Jyk7XG5cdFx0fVxuXHRcdC8vIG9yIGBjaGlsZGAgaXMgbnVsbCBhbmQgYHBhcmVudGAgaGFzIGFuIGVsZW1lbnQgY2hpbGQuXG5cdFx0aWYgKCFjaGlsZCAmJiBwYXJlbnRFbGVtZW50Q2hpbGQpIHtcblx0XHRcdHRocm93IG5ldyBET01FeGNlcHRpb24oSElFUkFSQ0hZX1JFUVVFU1RfRVJSLCAnRG9jdHlwZSBjYW4gbm90IGJlIGFwcGVuZGVkIHNpbmNlIGVsZW1lbnQgaXMgcHJlc2VudCcpO1xuXHRcdH1cblx0fVxufVxuXG4vKipcbiAqIEBwcml2YXRlXG4gKiBTdGVwIDYgb2YgdGhlIGNoZWNrcyBiZWZvcmUgaW5zZXJ0aW5nIGFuZCBiZWZvcmUgcmVwbGFjaW5nIGEgY2hpbGQgYXJlIGRpZmZlcmVudC5cbiAqXG4gKiBAcGFyYW0ge0RvY3VtZW50fSBwYXJlbnQgdGhlIHBhcmVudCBub2RlIHRvIGluc2VydCBgbm9kZWAgaW50b1xuICogQHBhcmFtIHtOb2RlfSBub2RlIHRoZSBub2RlIHRvIGluc2VydFxuICogQHBhcmFtIHtOb2RlIHwgdW5kZWZpbmVkfSBjaGlsZCB0aGUgbm9kZSB0aGF0IHNob3VsZCBiZWNvbWUgdGhlIGBuZXh0U2libGluZ2Agb2YgYG5vZGVgXG4gKiBAcmV0dXJucyB7Tm9kZX1cbiAqIEB0aHJvd3MgRE9NRXhjZXB0aW9uIGZvciBzZXZlcmFsIG5vZGUgY29tYmluYXRpb25zIHRoYXQgd291bGQgY3JlYXRlIGEgRE9NIHRoYXQgaXMgbm90IHdlbGwtZm9ybWVkLlxuICogQHRocm93cyBET01FeGNlcHRpb24gaWYgYGNoaWxkYCBpcyBwcm92aWRlZCBidXQgaXMgbm90IGEgY2hpbGQgb2YgYHBhcmVudGAuXG4gKiBAc2VlIGh0dHBzOi8vZG9tLnNwZWMud2hhdHdnLm9yZy8jY29uY2VwdC1ub2RlLWVuc3VyZS1wcmUtaW5zZXJ0aW9uLXZhbGlkaXR5XG4gKiBAc2VlIGh0dHBzOi8vZG9tLnNwZWMud2hhdHdnLm9yZy8jY29uY2VwdC1ub2RlLXJlcGxhY2VcbiAqL1xuZnVuY3Rpb24gYXNzZXJ0UHJlUmVwbGFjZW1lbnRWYWxpZGl0eUluRG9jdW1lbnQocGFyZW50LCBub2RlLCBjaGlsZCkge1xuXHR2YXIgcGFyZW50Q2hpbGROb2RlcyA9IHBhcmVudC5jaGlsZE5vZGVzIHx8IFtdO1xuXHR2YXIgbm9kZUNoaWxkTm9kZXMgPSBub2RlLmNoaWxkTm9kZXMgfHwgW107XG5cblx0Ly8gRG9jdW1lbnRGcmFnbWVudFxuXHRpZiAobm9kZS5ub2RlVHlwZSA9PT0gTm9kZS5ET0NVTUVOVF9GUkFHTUVOVF9OT0RFKSB7XG5cdFx0dmFyIG5vZGVDaGlsZEVsZW1lbnRzID0gbm9kZUNoaWxkTm9kZXMuZmlsdGVyKGlzRWxlbWVudE5vZGUpO1xuXHRcdC8vIElmIGBub2RlYCBoYXMgbW9yZSB0aGFuIG9uZSBlbGVtZW50IGNoaWxkIG9yIGhhcyBhIFRleHQgbm9kZSBjaGlsZC5cblx0XHRpZiAobm9kZUNoaWxkRWxlbWVudHMubGVuZ3RoID4gMSB8fCBmaW5kKG5vZGVDaGlsZE5vZGVzLCBpc1RleHROb2RlKSkge1xuXHRcdFx0dGhyb3cgbmV3IERPTUV4Y2VwdGlvbihISUVSQVJDSFlfUkVRVUVTVF9FUlIsICdNb3JlIHRoYW4gb25lIGVsZW1lbnQgb3IgdGV4dCBpbiBmcmFnbWVudCcpO1xuXHRcdH1cblx0XHQvLyBPdGhlcndpc2UsIGlmIGBub2RlYCBoYXMgb25lIGVsZW1lbnQgY2hpbGQgYW5kIGVpdGhlciBgcGFyZW50YCBoYXMgYW4gZWxlbWVudCBjaGlsZCB0aGF0IGlzIG5vdCBgY2hpbGRgIG9yIGEgZG9jdHlwZSBpcyBmb2xsb3dpbmcgYGNoaWxkYC5cblx0XHRpZiAobm9kZUNoaWxkRWxlbWVudHMubGVuZ3RoID09PSAxICYmICFpc0VsZW1lbnRSZXBsYWNlbWVudFBvc3NpYmxlKHBhcmVudCwgY2hpbGQpKSB7XG5cdFx0XHR0aHJvdyBuZXcgRE9NRXhjZXB0aW9uKEhJRVJBUkNIWV9SRVFVRVNUX0VSUiwgJ0VsZW1lbnQgaW4gZnJhZ21lbnQgY2FuIG5vdCBiZSBpbnNlcnRlZCBiZWZvcmUgZG9jdHlwZScpO1xuXHRcdH1cblx0fVxuXHQvLyBFbGVtZW50XG5cdGlmIChpc0VsZW1lbnROb2RlKG5vZGUpKSB7XG5cdFx0Ly8gYHBhcmVudGAgaGFzIGFuIGVsZW1lbnQgY2hpbGQgdGhhdCBpcyBub3QgYGNoaWxkYCBvciBhIGRvY3R5cGUgaXMgZm9sbG93aW5nIGBjaGlsZGAuXG5cdFx0aWYgKCFpc0VsZW1lbnRSZXBsYWNlbWVudFBvc3NpYmxlKHBhcmVudCwgY2hpbGQpKSB7XG5cdFx0XHR0aHJvdyBuZXcgRE9NRXhjZXB0aW9uKEhJRVJBUkNIWV9SRVFVRVNUX0VSUiwgJ09ubHkgb25lIGVsZW1lbnQgY2FuIGJlIGFkZGVkIGFuZCBvbmx5IGFmdGVyIGRvY3R5cGUnKTtcblx0XHR9XG5cdH1cblx0Ly8gRG9jdW1lbnRUeXBlXG5cdGlmIChpc0RvY1R5cGVOb2RlKG5vZGUpKSB7XG5cdFx0ZnVuY3Rpb24gaGFzRG9jdHlwZUNoaWxkVGhhdElzTm90Q2hpbGQobm9kZSkge1xuXHRcdFx0cmV0dXJuIGlzRG9jVHlwZU5vZGUobm9kZSkgJiYgbm9kZSAhPT0gY2hpbGQ7XG5cdFx0fVxuXG5cdFx0Ly8gYHBhcmVudGAgaGFzIGEgZG9jdHlwZSBjaGlsZCB0aGF0IGlzIG5vdCBgY2hpbGRgLFxuXHRcdGlmIChmaW5kKHBhcmVudENoaWxkTm9kZXMsIGhhc0RvY3R5cGVDaGlsZFRoYXRJc05vdENoaWxkKSkge1xuXHRcdFx0dGhyb3cgbmV3IERPTUV4Y2VwdGlvbihISUVSQVJDSFlfUkVRVUVTVF9FUlIsICdPbmx5IG9uZSBkb2N0eXBlIGlzIGFsbG93ZWQnKTtcblx0XHR9XG5cdFx0dmFyIHBhcmVudEVsZW1lbnRDaGlsZCA9IGZpbmQocGFyZW50Q2hpbGROb2RlcywgaXNFbGVtZW50Tm9kZSk7XG5cdFx0Ly8gb3IgYW4gZWxlbWVudCBpcyBwcmVjZWRpbmcgYGNoaWxkYC5cblx0XHRpZiAoY2hpbGQgJiYgcGFyZW50Q2hpbGROb2Rlcy5pbmRleE9mKHBhcmVudEVsZW1lbnRDaGlsZCkgPCBwYXJlbnRDaGlsZE5vZGVzLmluZGV4T2YoY2hpbGQpKSB7XG5cdFx0XHR0aHJvdyBuZXcgRE9NRXhjZXB0aW9uKEhJRVJBUkNIWV9SRVFVRVNUX0VSUiwgJ0RvY3R5cGUgY2FuIG9ubHkgYmUgaW5zZXJ0ZWQgYmVmb3JlIGFuIGVsZW1lbnQnKTtcblx0XHR9XG5cdH1cbn1cblxuLyoqXG4gKiBAcHJpdmF0ZVxuICogQHBhcmFtIHtOb2RlfSBwYXJlbnQgdGhlIHBhcmVudCBub2RlIHRvIGluc2VydCBgbm9kZWAgaW50b1xuICogQHBhcmFtIHtOb2RlfSBub2RlIHRoZSBub2RlIHRvIGluc2VydFxuICogQHBhcmFtIHtOb2RlPX0gY2hpbGQgdGhlIG5vZGUgdGhhdCBzaG91bGQgYmVjb21lIHRoZSBgbmV4dFNpYmxpbmdgIG9mIGBub2RlYFxuICogQHJldHVybnMge05vZGV9XG4gKiBAdGhyb3dzIERPTUV4Y2VwdGlvbiBmb3Igc2V2ZXJhbCBub2RlIGNvbWJpbmF0aW9ucyB0aGF0IHdvdWxkIGNyZWF0ZSBhIERPTSB0aGF0IGlzIG5vdCB3ZWxsLWZvcm1lZC5cbiAqIEB0aHJvd3MgRE9NRXhjZXB0aW9uIGlmIGBjaGlsZGAgaXMgcHJvdmlkZWQgYnV0IGlzIG5vdCBhIGNoaWxkIG9mIGBwYXJlbnRgLlxuICogQHNlZSBodHRwczovL2RvbS5zcGVjLndoYXR3Zy5vcmcvI2NvbmNlcHQtbm9kZS1lbnN1cmUtcHJlLWluc2VydGlvbi12YWxpZGl0eVxuICovXG5mdW5jdGlvbiBfaW5zZXJ0QmVmb3JlKHBhcmVudCwgbm9kZSwgY2hpbGQsIF9pbkRvY3VtZW50QXNzZXJ0aW9uKSB7XG5cdC8vIFRvIGVuc3VyZSBwcmUtaW5zZXJ0aW9uIHZhbGlkaXR5IG9mIGEgbm9kZSBpbnRvIGEgcGFyZW50IGJlZm9yZSBhIGNoaWxkLCBydW4gdGhlc2Ugc3RlcHM6XG5cdGFzc2VydFByZUluc2VydGlvblZhbGlkaXR5MXRvNShwYXJlbnQsIG5vZGUsIGNoaWxkKTtcblxuXHQvLyBJZiBwYXJlbnQgaXMgYSBkb2N1bWVudCwgYW5kIGFueSBvZiB0aGUgc3RhdGVtZW50cyBiZWxvdywgc3dpdGNoZWQgb24gdGhlIGludGVyZmFjZSBub2RlIGltcGxlbWVudHMsXG5cdC8vIGFyZSB0cnVlLCB0aGVuIHRocm93IGEgXCJIaWVyYXJjaHlSZXF1ZXN0RXJyb3JcIiBET01FeGNlcHRpb24uXG5cdGlmIChwYXJlbnQubm9kZVR5cGUgPT09IE5vZGUuRE9DVU1FTlRfTk9ERSkge1xuXHRcdChfaW5Eb2N1bWVudEFzc2VydGlvbiB8fCBhc3NlcnRQcmVJbnNlcnRpb25WYWxpZGl0eUluRG9jdW1lbnQpKHBhcmVudCwgbm9kZSwgY2hpbGQpO1xuXHR9XG5cblx0dmFyIGNwID0gbm9kZS5wYXJlbnROb2RlO1xuXHRpZihjcCl7XG5cdFx0Y3AucmVtb3ZlQ2hpbGQobm9kZSk7Ly9yZW1vdmUgYW5kIHVwZGF0ZVxuXHR9XG5cdGlmKG5vZGUubm9kZVR5cGUgPT09IERPQ1VNRU5UX0ZSQUdNRU5UX05PREUpe1xuXHRcdHZhciBuZXdGaXJzdCA9IG5vZGUuZmlyc3RDaGlsZDtcblx0XHRpZiAobmV3Rmlyc3QgPT0gbnVsbCkge1xuXHRcdFx0cmV0dXJuIG5vZGU7XG5cdFx0fVxuXHRcdHZhciBuZXdMYXN0ID0gbm9kZS5sYXN0Q2hpbGQ7XG5cdH1lbHNle1xuXHRcdG5ld0ZpcnN0ID0gbmV3TGFzdCA9IG5vZGU7XG5cdH1cblx0dmFyIHByZSA9IGNoaWxkID8gY2hpbGQucHJldmlvdXNTaWJsaW5nIDogcGFyZW50Lmxhc3RDaGlsZDtcblxuXHRuZXdGaXJzdC5wcmV2aW91c1NpYmxpbmcgPSBwcmU7XG5cdG5ld0xhc3QubmV4dFNpYmxpbmcgPSBjaGlsZDtcblxuXG5cdGlmKHByZSl7XG5cdFx0cHJlLm5leHRTaWJsaW5nID0gbmV3Rmlyc3Q7XG5cdH1lbHNle1xuXHRcdHBhcmVudC5maXJzdENoaWxkID0gbmV3Rmlyc3Q7XG5cdH1cblx0aWYoY2hpbGQgPT0gbnVsbCl7XG5cdFx0cGFyZW50Lmxhc3RDaGlsZCA9IG5ld0xhc3Q7XG5cdH1lbHNle1xuXHRcdGNoaWxkLnByZXZpb3VzU2libGluZyA9IG5ld0xhc3Q7XG5cdH1cblx0ZG97XG5cdFx0bmV3Rmlyc3QucGFyZW50Tm9kZSA9IHBhcmVudDtcblx0fXdoaWxlKG5ld0ZpcnN0ICE9PSBuZXdMYXN0ICYmIChuZXdGaXJzdD0gbmV3Rmlyc3QubmV4dFNpYmxpbmcpKVxuXHRfb25VcGRhdGVDaGlsZChwYXJlbnQub3duZXJEb2N1bWVudHx8cGFyZW50LCBwYXJlbnQpO1xuXHQvL2NvbnNvbGUubG9nKHBhcmVudC5sYXN0Q2hpbGQubmV4dFNpYmxpbmcgPT0gbnVsbClcblx0aWYgKG5vZGUubm9kZVR5cGUgPT0gRE9DVU1FTlRfRlJBR01FTlRfTk9ERSkge1xuXHRcdG5vZGUuZmlyc3RDaGlsZCA9IG5vZGUubGFzdENoaWxkID0gbnVsbDtcblx0fVxuXHRyZXR1cm4gbm9kZTtcbn1cblxuLyoqXG4gKiBBcHBlbmRzIGBuZXdDaGlsZGAgdG8gYHBhcmVudE5vZGVgLlxuICogSWYgYG5ld0NoaWxkYCBpcyBhbHJlYWR5IGNvbm5lY3RlZCB0byBhIGBwYXJlbnROb2RlYCBpdCBpcyBmaXJzdCByZW1vdmVkIGZyb20gaXQuXG4gKlxuICogQHNlZSBodHRwczovL2dpdGh1Yi5jb20veG1sZG9tL3htbGRvbS9pc3N1ZXMvMTM1XG4gKiBAc2VlIGh0dHBzOi8vZ2l0aHViLmNvbS94bWxkb20veG1sZG9tL2lzc3Vlcy8xNDVcbiAqIEBwYXJhbSB7Tm9kZX0gcGFyZW50Tm9kZVxuICogQHBhcmFtIHtOb2RlfSBuZXdDaGlsZFxuICogQHJldHVybnMge05vZGV9XG4gKiBAcHJpdmF0ZVxuICovXG5mdW5jdGlvbiBfYXBwZW5kU2luZ2xlQ2hpbGQgKHBhcmVudE5vZGUsIG5ld0NoaWxkKSB7XG5cdGlmIChuZXdDaGlsZC5wYXJlbnROb2RlKSB7XG5cdFx0bmV3Q2hpbGQucGFyZW50Tm9kZS5yZW1vdmVDaGlsZChuZXdDaGlsZCk7XG5cdH1cblx0bmV3Q2hpbGQucGFyZW50Tm9kZSA9IHBhcmVudE5vZGU7XG5cdG5ld0NoaWxkLnByZXZpb3VzU2libGluZyA9IHBhcmVudE5vZGUubGFzdENoaWxkO1xuXHRuZXdDaGlsZC5uZXh0U2libGluZyA9IG51bGw7XG5cdGlmIChuZXdDaGlsZC5wcmV2aW91c1NpYmxpbmcpIHtcblx0XHRuZXdDaGlsZC5wcmV2aW91c1NpYmxpbmcubmV4dFNpYmxpbmcgPSBuZXdDaGlsZDtcblx0fSBlbHNlIHtcblx0XHRwYXJlbnROb2RlLmZpcnN0Q2hpbGQgPSBuZXdDaGlsZDtcblx0fVxuXHRwYXJlbnROb2RlLmxhc3RDaGlsZCA9IG5ld0NoaWxkO1xuXHRfb25VcGRhdGVDaGlsZChwYXJlbnROb2RlLm93bmVyRG9jdW1lbnQsIHBhcmVudE5vZGUsIG5ld0NoaWxkKTtcblx0cmV0dXJuIG5ld0NoaWxkO1xufVxuXG5Eb2N1bWVudC5wcm90b3R5cGUgPSB7XG5cdC8vaW1wbGVtZW50YXRpb24gOiBudWxsLFxuXHRub2RlTmFtZSA6ICAnI2RvY3VtZW50Jyxcblx0bm9kZVR5cGUgOiAgRE9DVU1FTlRfTk9ERSxcblx0LyoqXG5cdCAqIFRoZSBEb2N1bWVudFR5cGUgbm9kZSBvZiB0aGUgZG9jdW1lbnQuXG5cdCAqXG5cdCAqIEByZWFkb25seVxuXHQgKiBAdHlwZSBEb2N1bWVudFR5cGVcblx0ICovXG5cdGRvY3R5cGUgOiAgbnVsbCxcblx0ZG9jdW1lbnRFbGVtZW50IDogIG51bGwsXG5cdF9pbmMgOiAxLFxuXG5cdGluc2VydEJlZm9yZSA6ICBmdW5jdGlvbihuZXdDaGlsZCwgcmVmQ2hpbGQpey8vcmFpc2VzXG5cdFx0aWYobmV3Q2hpbGQubm9kZVR5cGUgPT0gRE9DVU1FTlRfRlJBR01FTlRfTk9ERSl7XG5cdFx0XHR2YXIgY2hpbGQgPSBuZXdDaGlsZC5maXJzdENoaWxkO1xuXHRcdFx0d2hpbGUoY2hpbGQpe1xuXHRcdFx0XHR2YXIgbmV4dCA9IGNoaWxkLm5leHRTaWJsaW5nO1xuXHRcdFx0XHR0aGlzLmluc2VydEJlZm9yZShjaGlsZCxyZWZDaGlsZCk7XG5cdFx0XHRcdGNoaWxkID0gbmV4dDtcblx0XHRcdH1cblx0XHRcdHJldHVybiBuZXdDaGlsZDtcblx0XHR9XG5cdFx0X2luc2VydEJlZm9yZSh0aGlzLCBuZXdDaGlsZCwgcmVmQ2hpbGQpO1xuXHRcdG5ld0NoaWxkLm93bmVyRG9jdW1lbnQgPSB0aGlzO1xuXHRcdGlmICh0aGlzLmRvY3VtZW50RWxlbWVudCA9PT0gbnVsbCAmJiBuZXdDaGlsZC5ub2RlVHlwZSA9PT0gRUxFTUVOVF9OT0RFKSB7XG5cdFx0XHR0aGlzLmRvY3VtZW50RWxlbWVudCA9IG5ld0NoaWxkO1xuXHRcdH1cblxuXHRcdHJldHVybiBuZXdDaGlsZDtcblx0fSxcblx0cmVtb3ZlQ2hpbGQgOiAgZnVuY3Rpb24ob2xkQ2hpbGQpe1xuXHRcdGlmKHRoaXMuZG9jdW1lbnRFbGVtZW50ID09IG9sZENoaWxkKXtcblx0XHRcdHRoaXMuZG9jdW1lbnRFbGVtZW50ID0gbnVsbDtcblx0XHR9XG5cdFx0cmV0dXJuIF9yZW1vdmVDaGlsZCh0aGlzLG9sZENoaWxkKTtcblx0fSxcblx0cmVwbGFjZUNoaWxkOiBmdW5jdGlvbiAobmV3Q2hpbGQsIG9sZENoaWxkKSB7XG5cdFx0Ly9yYWlzZXNcblx0XHRfaW5zZXJ0QmVmb3JlKHRoaXMsIG5ld0NoaWxkLCBvbGRDaGlsZCwgYXNzZXJ0UHJlUmVwbGFjZW1lbnRWYWxpZGl0eUluRG9jdW1lbnQpO1xuXHRcdG5ld0NoaWxkLm93bmVyRG9jdW1lbnQgPSB0aGlzO1xuXHRcdGlmIChvbGRDaGlsZCkge1xuXHRcdFx0dGhpcy5yZW1vdmVDaGlsZChvbGRDaGlsZCk7XG5cdFx0fVxuXHRcdGlmIChpc0VsZW1lbnROb2RlKG5ld0NoaWxkKSkge1xuXHRcdFx0dGhpcy5kb2N1bWVudEVsZW1lbnQgPSBuZXdDaGlsZDtcblx0XHR9XG5cdH0sXG5cdC8vIEludHJvZHVjZWQgaW4gRE9NIExldmVsIDI6XG5cdGltcG9ydE5vZGUgOiBmdW5jdGlvbihpbXBvcnRlZE5vZGUsZGVlcCl7XG5cdFx0cmV0dXJuIGltcG9ydE5vZGUodGhpcyxpbXBvcnRlZE5vZGUsZGVlcCk7XG5cdH0sXG5cdC8vIEludHJvZHVjZWQgaW4gRE9NIExldmVsIDI6XG5cdGdldEVsZW1lbnRCeUlkIDpcdGZ1bmN0aW9uKGlkKXtcblx0XHR2YXIgcnR2ID0gbnVsbDtcblx0XHRfdmlzaXROb2RlKHRoaXMuZG9jdW1lbnRFbGVtZW50LGZ1bmN0aW9uKG5vZGUpe1xuXHRcdFx0aWYobm9kZS5ub2RlVHlwZSA9PSBFTEVNRU5UX05PREUpe1xuXHRcdFx0XHRpZihub2RlLmdldEF0dHJpYnV0ZSgnaWQnKSA9PSBpZCl7XG5cdFx0XHRcdFx0cnR2ID0gbm9kZTtcblx0XHRcdFx0XHRyZXR1cm4gdHJ1ZTtcblx0XHRcdFx0fVxuXHRcdFx0fVxuXHRcdH0pXG5cdFx0cmV0dXJuIHJ0djtcblx0fSxcblxuXHQvKipcblx0ICogVGhlIGBnZXRFbGVtZW50c0J5Q2xhc3NOYW1lYCBtZXRob2Qgb2YgYERvY3VtZW50YCBpbnRlcmZhY2UgcmV0dXJucyBhbiBhcnJheS1saWtlIG9iamVjdFxuXHQgKiBvZiBhbGwgY2hpbGQgZWxlbWVudHMgd2hpY2ggaGF2ZSAqKmFsbCoqIG9mIHRoZSBnaXZlbiBjbGFzcyBuYW1lKHMpLlxuXHQgKlxuXHQgKiBSZXR1cm5zIGFuIGVtcHR5IGxpc3QgaWYgYGNsYXNzZU5hbWVzYCBpcyBhbiBlbXB0eSBzdHJpbmcgb3Igb25seSBjb250YWlucyBIVE1MIHdoaXRlIHNwYWNlIGNoYXJhY3RlcnMuXG5cdCAqXG5cdCAqXG5cdCAqIFdhcm5pbmc6IFRoaXMgaXMgYSBsaXZlIExpdmVOb2RlTGlzdC5cblx0ICogQ2hhbmdlcyBpbiB0aGUgRE9NIHdpbGwgcmVmbGVjdCBpbiB0aGUgYXJyYXkgYXMgdGhlIGNoYW5nZXMgb2NjdXIuXG5cdCAqIElmIGFuIGVsZW1lbnQgc2VsZWN0ZWQgYnkgdGhpcyBhcnJheSBubyBsb25nZXIgcXVhbGlmaWVzIGZvciB0aGUgc2VsZWN0b3IsXG5cdCAqIGl0IHdpbGwgYXV0b21hdGljYWxseSBiZSByZW1vdmVkLiBCZSBhd2FyZSBvZiB0aGlzIGZvciBpdGVyYXRpb24gcHVycG9zZXMuXG5cdCAqXG5cdCAqIEBwYXJhbSB7c3RyaW5nfSBjbGFzc05hbWVzIGlzIGEgc3RyaW5nIHJlcHJlc2VudGluZyB0aGUgY2xhc3MgbmFtZShzKSB0byBtYXRjaDsgbXVsdGlwbGUgY2xhc3MgbmFtZXMgYXJlIHNlcGFyYXRlZCBieSAoQVNDSUktKXdoaXRlc3BhY2Vcblx0ICpcblx0ICogQHNlZSBodHRwczovL2RldmVsb3Blci5tb3ppbGxhLm9yZy9lbi1VUy9kb2NzL1dlYi9BUEkvRG9jdW1lbnQvZ2V0RWxlbWVudHNCeUNsYXNzTmFtZVxuXHQgKiBAc2VlIGh0dHBzOi8vZG9tLnNwZWMud2hhdHdnLm9yZy8jY29uY2VwdC1nZXRlbGVtZW50c2J5Y2xhc3NuYW1lXG5cdCAqL1xuXHRnZXRFbGVtZW50c0J5Q2xhc3NOYW1lOiBmdW5jdGlvbihjbGFzc05hbWVzKSB7XG5cdFx0dmFyIGNsYXNzTmFtZXNTZXQgPSB0b09yZGVyZWRTZXQoY2xhc3NOYW1lcylcblx0XHRyZXR1cm4gbmV3IExpdmVOb2RlTGlzdCh0aGlzLCBmdW5jdGlvbihiYXNlKSB7XG5cdFx0XHR2YXIgbHMgPSBbXTtcblx0XHRcdGlmIChjbGFzc05hbWVzU2V0Lmxlbmd0aCA+IDApIHtcblx0XHRcdFx0X3Zpc2l0Tm9kZShiYXNlLmRvY3VtZW50RWxlbWVudCwgZnVuY3Rpb24obm9kZSkge1xuXHRcdFx0XHRcdGlmKG5vZGUgIT09IGJhc2UgJiYgbm9kZS5ub2RlVHlwZSA9PT0gRUxFTUVOVF9OT0RFKSB7XG5cdFx0XHRcdFx0XHR2YXIgbm9kZUNsYXNzTmFtZXMgPSBub2RlLmdldEF0dHJpYnV0ZSgnY2xhc3MnKVxuXHRcdFx0XHRcdFx0Ly8gY2FuIGJlIG51bGwgaWYgdGhlIGF0dHJpYnV0ZSBkb2VzIG5vdCBleGlzdFxuXHRcdFx0XHRcdFx0aWYgKG5vZGVDbGFzc05hbWVzKSB7XG5cdFx0XHRcdFx0XHRcdC8vIGJlZm9yZSBzcGxpdHRpbmcgYW5kIGl0ZXJhdGluZyBqdXN0IGNvbXBhcmUgdGhlbSBmb3IgdGhlIG1vc3QgY29tbW9uIGNhc2Vcblx0XHRcdFx0XHRcdFx0dmFyIG1hdGNoZXMgPSBjbGFzc05hbWVzID09PSBub2RlQ2xhc3NOYW1lcztcblx0XHRcdFx0XHRcdFx0aWYgKCFtYXRjaGVzKSB7XG5cdFx0XHRcdFx0XHRcdFx0dmFyIG5vZGVDbGFzc05hbWVzU2V0ID0gdG9PcmRlcmVkU2V0KG5vZGVDbGFzc05hbWVzKVxuXHRcdFx0XHRcdFx0XHRcdG1hdGNoZXMgPSBjbGFzc05hbWVzU2V0LmV2ZXJ5KGFycmF5SW5jbHVkZXMobm9kZUNsYXNzTmFtZXNTZXQpKVxuXHRcdFx0XHRcdFx0XHR9XG5cdFx0XHRcdFx0XHRcdGlmKG1hdGNoZXMpIHtcblx0XHRcdFx0XHRcdFx0XHRscy5wdXNoKG5vZGUpO1xuXHRcdFx0XHRcdFx0XHR9XG5cdFx0XHRcdFx0XHR9XG5cdFx0XHRcdFx0fVxuXHRcdFx0XHR9KTtcblx0XHRcdH1cblx0XHRcdHJldHVybiBscztcblx0XHR9KTtcblx0fSxcblxuXHQvL2RvY3VtZW50IGZhY3RvcnkgbWV0aG9kOlxuXHRjcmVhdGVFbGVtZW50IDpcdGZ1bmN0aW9uKHRhZ05hbWUpe1xuXHRcdHZhciBub2RlID0gbmV3IEVsZW1lbnQoKTtcblx0XHRub2RlLm93bmVyRG9jdW1lbnQgPSB0aGlzO1xuXHRcdG5vZGUubm9kZU5hbWUgPSB0YWdOYW1lO1xuXHRcdG5vZGUudGFnTmFtZSA9IHRhZ05hbWU7XG5cdFx0bm9kZS5sb2NhbE5hbWUgPSB0YWdOYW1lO1xuXHRcdG5vZGUuY2hpbGROb2RlcyA9IG5ldyBOb2RlTGlzdCgpO1xuXHRcdHZhciBhdHRyc1x0PSBub2RlLmF0dHJpYnV0ZXMgPSBuZXcgTmFtZWROb2RlTWFwKCk7XG5cdFx0YXR0cnMuX293bmVyRWxlbWVudCA9IG5vZGU7XG5cdFx0cmV0dXJuIG5vZGU7XG5cdH0sXG5cdGNyZWF0ZURvY3VtZW50RnJhZ21lbnQgOlx0ZnVuY3Rpb24oKXtcblx0XHR2YXIgbm9kZSA9IG5ldyBEb2N1bWVudEZyYWdtZW50KCk7XG5cdFx0bm9kZS5vd25lckRvY3VtZW50ID0gdGhpcztcblx0XHRub2RlLmNoaWxkTm9kZXMgPSBuZXcgTm9kZUxpc3QoKTtcblx0XHRyZXR1cm4gbm9kZTtcblx0fSxcblx0Y3JlYXRlVGV4dE5vZGUgOlx0ZnVuY3Rpb24oZGF0YSl7XG5cdFx0dmFyIG5vZGUgPSBuZXcgVGV4dCgpO1xuXHRcdG5vZGUub3duZXJEb2N1bWVudCA9IHRoaXM7XG5cdFx0bm9kZS5hcHBlbmREYXRhKGRhdGEpXG5cdFx0cmV0dXJuIG5vZGU7XG5cdH0sXG5cdGNyZWF0ZUNvbW1lbnQgOlx0ZnVuY3Rpb24oZGF0YSl7XG5cdFx0dmFyIG5vZGUgPSBuZXcgQ29tbWVudCgpO1xuXHRcdG5vZGUub3duZXJEb2N1bWVudCA9IHRoaXM7XG5cdFx0bm9kZS5hcHBlbmREYXRhKGRhdGEpXG5cdFx0cmV0dXJuIG5vZGU7XG5cdH0sXG5cdGNyZWF0ZUNEQVRBU2VjdGlvbiA6XHRmdW5jdGlvbihkYXRhKXtcblx0XHR2YXIgbm9kZSA9IG5ldyBDREFUQVNlY3Rpb24oKTtcblx0XHRub2RlLm93bmVyRG9jdW1lbnQgPSB0aGlzO1xuXHRcdG5vZGUuYXBwZW5kRGF0YShkYXRhKVxuXHRcdHJldHVybiBub2RlO1xuXHR9LFxuXHRjcmVhdGVQcm9jZXNzaW5nSW5zdHJ1Y3Rpb24gOlx0ZnVuY3Rpb24odGFyZ2V0LGRhdGEpe1xuXHRcdHZhciBub2RlID0gbmV3IFByb2Nlc3NpbmdJbnN0cnVjdGlvbigpO1xuXHRcdG5vZGUub3duZXJEb2N1bWVudCA9IHRoaXM7XG5cdFx0bm9kZS50YWdOYW1lID0gbm9kZS5ub2RlTmFtZSA9IG5vZGUudGFyZ2V0ID0gdGFyZ2V0O1xuXHRcdG5vZGUubm9kZVZhbHVlID0gbm9kZS5kYXRhID0gZGF0YTtcblx0XHRyZXR1cm4gbm9kZTtcblx0fSxcblx0Y3JlYXRlQXR0cmlidXRlIDpcdGZ1bmN0aW9uKG5hbWUpe1xuXHRcdHZhciBub2RlID0gbmV3IEF0dHIoKTtcblx0XHRub2RlLm93bmVyRG9jdW1lbnRcdD0gdGhpcztcblx0XHRub2RlLm5hbWUgPSBuYW1lO1xuXHRcdG5vZGUubm9kZU5hbWVcdD0gbmFtZTtcblx0XHRub2RlLmxvY2FsTmFtZSA9IG5hbWU7XG5cdFx0bm9kZS5zcGVjaWZpZWQgPSB0cnVlO1xuXHRcdHJldHVybiBub2RlO1xuXHR9LFxuXHRjcmVhdGVFbnRpdHlSZWZlcmVuY2UgOlx0ZnVuY3Rpb24obmFtZSl7XG5cdFx0dmFyIG5vZGUgPSBuZXcgRW50aXR5UmVmZXJlbmNlKCk7XG5cdFx0bm9kZS5vd25lckRvY3VtZW50XHQ9IHRoaXM7XG5cdFx0bm9kZS5ub2RlTmFtZVx0PSBuYW1lO1xuXHRcdHJldHVybiBub2RlO1xuXHR9LFxuXHQvLyBJbnRyb2R1Y2VkIGluIERPTSBMZXZlbCAyOlxuXHRjcmVhdGVFbGVtZW50TlMgOlx0ZnVuY3Rpb24obmFtZXNwYWNlVVJJLHF1YWxpZmllZE5hbWUpe1xuXHRcdHZhciBub2RlID0gbmV3IEVsZW1lbnQoKTtcblx0XHR2YXIgcGwgPSBxdWFsaWZpZWROYW1lLnNwbGl0KCc6Jyk7XG5cdFx0dmFyIGF0dHJzXHQ9IG5vZGUuYXR0cmlidXRlcyA9IG5ldyBOYW1lZE5vZGVNYXAoKTtcblx0XHRub2RlLmNoaWxkTm9kZXMgPSBuZXcgTm9kZUxpc3QoKTtcblx0XHRub2RlLm93bmVyRG9jdW1lbnQgPSB0aGlzO1xuXHRcdG5vZGUubm9kZU5hbWUgPSBxdWFsaWZpZWROYW1lO1xuXHRcdG5vZGUudGFnTmFtZSA9IHF1YWxpZmllZE5hbWU7XG5cdFx0bm9kZS5uYW1lc3BhY2VVUkkgPSBuYW1lc3BhY2VVUkk7XG5cdFx0aWYocGwubGVuZ3RoID09IDIpe1xuXHRcdFx0bm9kZS5wcmVmaXggPSBwbFswXTtcblx0XHRcdG5vZGUubG9jYWxOYW1lID0gcGxbMV07XG5cdFx0fWVsc2V7XG5cdFx0XHQvL2VsLnByZWZpeCA9IG51bGw7XG5cdFx0XHRub2RlLmxvY2FsTmFtZSA9IHF1YWxpZmllZE5hbWU7XG5cdFx0fVxuXHRcdGF0dHJzLl9vd25lckVsZW1lbnQgPSBub2RlO1xuXHRcdHJldHVybiBub2RlO1xuXHR9LFxuXHQvLyBJbnRyb2R1Y2VkIGluIERPTSBMZXZlbCAyOlxuXHRjcmVhdGVBdHRyaWJ1dGVOUyA6XHRmdW5jdGlvbihuYW1lc3BhY2VVUkkscXVhbGlmaWVkTmFtZSl7XG5cdFx0dmFyIG5vZGUgPSBuZXcgQXR0cigpO1xuXHRcdHZhciBwbCA9IHF1YWxpZmllZE5hbWUuc3BsaXQoJzonKTtcblx0XHRub2RlLm93bmVyRG9jdW1lbnQgPSB0aGlzO1xuXHRcdG5vZGUubm9kZU5hbWUgPSBxdWFsaWZpZWROYW1lO1xuXHRcdG5vZGUubmFtZSA9IHF1YWxpZmllZE5hbWU7XG5cdFx0bm9kZS5uYW1lc3BhY2VVUkkgPSBuYW1lc3BhY2VVUkk7XG5cdFx0bm9kZS5zcGVjaWZpZWQgPSB0cnVlO1xuXHRcdGlmKHBsLmxlbmd0aCA9PSAyKXtcblx0XHRcdG5vZGUucHJlZml4ID0gcGxbMF07XG5cdFx0XHRub2RlLmxvY2FsTmFtZSA9IHBsWzFdO1xuXHRcdH1lbHNle1xuXHRcdFx0Ly9lbC5wcmVmaXggPSBudWxsO1xuXHRcdFx0bm9kZS5sb2NhbE5hbWUgPSBxdWFsaWZpZWROYW1lO1xuXHRcdH1cblx0XHRyZXR1cm4gbm9kZTtcblx0fVxufTtcbl9leHRlbmRzKERvY3VtZW50LE5vZGUpO1xuXG5cbmZ1bmN0aW9uIEVsZW1lbnQoKSB7XG5cdHRoaXMuX25zTWFwID0ge307XG59O1xuRWxlbWVudC5wcm90b3R5cGUgPSB7XG5cdG5vZGVUeXBlIDogRUxFTUVOVF9OT0RFLFxuXHRoYXNBdHRyaWJ1dGUgOiBmdW5jdGlvbihuYW1lKXtcblx0XHRyZXR1cm4gdGhpcy5nZXRBdHRyaWJ1dGVOb2RlKG5hbWUpIT1udWxsO1xuXHR9LFxuXHRnZXRBdHRyaWJ1dGUgOiBmdW5jdGlvbihuYW1lKXtcblx0XHR2YXIgYXR0ciA9IHRoaXMuZ2V0QXR0cmlidXRlTm9kZShuYW1lKTtcblx0XHRyZXR1cm4gYXR0ciAmJiBhdHRyLnZhbHVlIHx8ICcnO1xuXHR9LFxuXHRnZXRBdHRyaWJ1dGVOb2RlIDogZnVuY3Rpb24obmFtZSl7XG5cdFx0cmV0dXJuIHRoaXMuYXR0cmlidXRlcy5nZXROYW1lZEl0ZW0obmFtZSk7XG5cdH0sXG5cdHNldEF0dHJpYnV0ZSA6IGZ1bmN0aW9uKG5hbWUsIHZhbHVlKXtcblx0XHR2YXIgYXR0ciA9IHRoaXMub3duZXJEb2N1bWVudC5jcmVhdGVBdHRyaWJ1dGUobmFtZSk7XG5cdFx0YXR0ci52YWx1ZSA9IGF0dHIubm9kZVZhbHVlID0gXCJcIiArIHZhbHVlO1xuXHRcdHRoaXMuc2V0QXR0cmlidXRlTm9kZShhdHRyKVxuXHR9LFxuXHRyZW1vdmVBdHRyaWJ1dGUgOiBmdW5jdGlvbihuYW1lKXtcblx0XHR2YXIgYXR0ciA9IHRoaXMuZ2V0QXR0cmlidXRlTm9kZShuYW1lKVxuXHRcdGF0dHIgJiYgdGhpcy5yZW1vdmVBdHRyaWJ1dGVOb2RlKGF0dHIpO1xuXHR9LFxuXG5cdC8vZm91ciByZWFsIG9wZWFydGlvbiBtZXRob2Rcblx0YXBwZW5kQ2hpbGQ6ZnVuY3Rpb24obmV3Q2hpbGQpe1xuXHRcdGlmKG5ld0NoaWxkLm5vZGVUeXBlID09PSBET0NVTUVOVF9GUkFHTUVOVF9OT0RFKXtcblx0XHRcdHJldHVybiB0aGlzLmluc2VydEJlZm9yZShuZXdDaGlsZCxudWxsKTtcblx0XHR9ZWxzZXtcblx0XHRcdHJldHVybiBfYXBwZW5kU2luZ2xlQ2hpbGQodGhpcyxuZXdDaGlsZCk7XG5cdFx0fVxuXHR9LFxuXHRzZXRBdHRyaWJ1dGVOb2RlIDogZnVuY3Rpb24obmV3QXR0cil7XG5cdFx0cmV0dXJuIHRoaXMuYXR0cmlidXRlcy5zZXROYW1lZEl0ZW0obmV3QXR0cik7XG5cdH0sXG5cdHNldEF0dHJpYnV0ZU5vZGVOUyA6IGZ1bmN0aW9uKG5ld0F0dHIpe1xuXHRcdHJldHVybiB0aGlzLmF0dHJpYnV0ZXMuc2V0TmFtZWRJdGVtTlMobmV3QXR0cik7XG5cdH0sXG5cdHJlbW92ZUF0dHJpYnV0ZU5vZGUgOiBmdW5jdGlvbihvbGRBdHRyKXtcblx0XHQvL2NvbnNvbGUubG9nKHRoaXMgPT0gb2xkQXR0ci5vd25lckVsZW1lbnQpXG5cdFx0cmV0dXJuIHRoaXMuYXR0cmlidXRlcy5yZW1vdmVOYW1lZEl0ZW0ob2xkQXR0ci5ub2RlTmFtZSk7XG5cdH0sXG5cdC8vZ2V0IHJlYWwgYXR0cmlidXRlIG5hbWUsYW5kIHJlbW92ZSBpdCBieSByZW1vdmVBdHRyaWJ1dGVOb2RlXG5cdHJlbW92ZUF0dHJpYnV0ZU5TIDogZnVuY3Rpb24obmFtZXNwYWNlVVJJLCBsb2NhbE5hbWUpe1xuXHRcdHZhciBvbGQgPSB0aGlzLmdldEF0dHJpYnV0ZU5vZGVOUyhuYW1lc3BhY2VVUkksIGxvY2FsTmFtZSk7XG5cdFx0b2xkICYmIHRoaXMucmVtb3ZlQXR0cmlidXRlTm9kZShvbGQpO1xuXHR9LFxuXG5cdGhhc0F0dHJpYnV0ZU5TIDogZnVuY3Rpb24obmFtZXNwYWNlVVJJLCBsb2NhbE5hbWUpe1xuXHRcdHJldHVybiB0aGlzLmdldEF0dHJpYnV0ZU5vZGVOUyhuYW1lc3BhY2VVUkksIGxvY2FsTmFtZSkhPW51bGw7XG5cdH0sXG5cdGdldEF0dHJpYnV0ZU5TIDogZnVuY3Rpb24obmFtZXNwYWNlVVJJLCBsb2NhbE5hbWUpe1xuXHRcdHZhciBhdHRyID0gdGhpcy5nZXRBdHRyaWJ1dGVOb2RlTlMobmFtZXNwYWNlVVJJLCBsb2NhbE5hbWUpO1xuXHRcdHJldHVybiBhdHRyICYmIGF0dHIudmFsdWUgfHwgJyc7XG5cdH0sXG5cdHNldEF0dHJpYnV0ZU5TIDogZnVuY3Rpb24obmFtZXNwYWNlVVJJLCBxdWFsaWZpZWROYW1lLCB2YWx1ZSl7XG5cdFx0dmFyIGF0dHIgPSB0aGlzLm93bmVyRG9jdW1lbnQuY3JlYXRlQXR0cmlidXRlTlMobmFtZXNwYWNlVVJJLCBxdWFsaWZpZWROYW1lKTtcblx0XHRhdHRyLnZhbHVlID0gYXR0ci5ub2RlVmFsdWUgPSBcIlwiICsgdmFsdWU7XG5cdFx0dGhpcy5zZXRBdHRyaWJ1dGVOb2RlKGF0dHIpXG5cdH0sXG5cdGdldEF0dHJpYnV0ZU5vZGVOUyA6IGZ1bmN0aW9uKG5hbWVzcGFjZVVSSSwgbG9jYWxOYW1lKXtcblx0XHRyZXR1cm4gdGhpcy5hdHRyaWJ1dGVzLmdldE5hbWVkSXRlbU5TKG5hbWVzcGFjZVVSSSwgbG9jYWxOYW1lKTtcblx0fSxcblxuXHRnZXRFbGVtZW50c0J5VGFnTmFtZSA6IGZ1bmN0aW9uKHRhZ05hbWUpe1xuXHRcdHJldHVybiBuZXcgTGl2ZU5vZGVMaXN0KHRoaXMsZnVuY3Rpb24oYmFzZSl7XG5cdFx0XHR2YXIgbHMgPSBbXTtcblx0XHRcdF92aXNpdE5vZGUoYmFzZSxmdW5jdGlvbihub2RlKXtcblx0XHRcdFx0aWYobm9kZSAhPT0gYmFzZSAmJiBub2RlLm5vZGVUeXBlID09IEVMRU1FTlRfTk9ERSAmJiAodGFnTmFtZSA9PT0gJyonIHx8IG5vZGUudGFnTmFtZSA9PSB0YWdOYW1lKSl7XG5cdFx0XHRcdFx0bHMucHVzaChub2RlKTtcblx0XHRcdFx0fVxuXHRcdFx0fSk7XG5cdFx0XHRyZXR1cm4gbHM7XG5cdFx0fSk7XG5cdH0sXG5cdGdldEVsZW1lbnRzQnlUYWdOYW1lTlMgOiBmdW5jdGlvbihuYW1lc3BhY2VVUkksIGxvY2FsTmFtZSl7XG5cdFx0cmV0dXJuIG5ldyBMaXZlTm9kZUxpc3QodGhpcyxmdW5jdGlvbihiYXNlKXtcblx0XHRcdHZhciBscyA9IFtdO1xuXHRcdFx0X3Zpc2l0Tm9kZShiYXNlLGZ1bmN0aW9uKG5vZGUpe1xuXHRcdFx0XHRpZihub2RlICE9PSBiYXNlICYmIG5vZGUubm9kZVR5cGUgPT09IEVMRU1FTlRfTk9ERSAmJiAobmFtZXNwYWNlVVJJID09PSAnKicgfHwgbm9kZS5uYW1lc3BhY2VVUkkgPT09IG5hbWVzcGFjZVVSSSkgJiYgKGxvY2FsTmFtZSA9PT0gJyonIHx8IG5vZGUubG9jYWxOYW1lID09IGxvY2FsTmFtZSkpe1xuXHRcdFx0XHRcdGxzLnB1c2gobm9kZSk7XG5cdFx0XHRcdH1cblx0XHRcdH0pO1xuXHRcdFx0cmV0dXJuIGxzO1xuXG5cdFx0fSk7XG5cdH1cbn07XG5Eb2N1bWVudC5wcm90b3R5cGUuZ2V0RWxlbWVudHNCeVRhZ05hbWUgPSBFbGVtZW50LnByb3RvdHlwZS5nZXRFbGVtZW50c0J5VGFnTmFtZTtcbkRvY3VtZW50LnByb3RvdHlwZS5nZXRFbGVtZW50c0J5VGFnTmFtZU5TID0gRWxlbWVudC5wcm90b3R5cGUuZ2V0RWxlbWVudHNCeVRhZ05hbWVOUztcblxuXG5fZXh0ZW5kcyhFbGVtZW50LE5vZGUpO1xuZnVuY3Rpb24gQXR0cigpIHtcbn07XG5BdHRyLnByb3RvdHlwZS5ub2RlVHlwZSA9IEFUVFJJQlVURV9OT0RFO1xuX2V4dGVuZHMoQXR0cixOb2RlKTtcblxuXG5mdW5jdGlvbiBDaGFyYWN0ZXJEYXRhKCkge1xufTtcbkNoYXJhY3RlckRhdGEucHJvdG90eXBlID0ge1xuXHRkYXRhIDogJycsXG5cdHN1YnN0cmluZ0RhdGEgOiBmdW5jdGlvbihvZmZzZXQsIGNvdW50KSB7XG5cdFx0cmV0dXJuIHRoaXMuZGF0YS5zdWJzdHJpbmcob2Zmc2V0LCBvZmZzZXQrY291bnQpO1xuXHR9LFxuXHRhcHBlbmREYXRhOiBmdW5jdGlvbih0ZXh0KSB7XG5cdFx0dGV4dCA9IHRoaXMuZGF0YSt0ZXh0O1xuXHRcdHRoaXMubm9kZVZhbHVlID0gdGhpcy5kYXRhID0gdGV4dDtcblx0XHR0aGlzLmxlbmd0aCA9IHRleHQubGVuZ3RoO1xuXHR9LFxuXHRpbnNlcnREYXRhOiBmdW5jdGlvbihvZmZzZXQsdGV4dCkge1xuXHRcdHRoaXMucmVwbGFjZURhdGEob2Zmc2V0LDAsdGV4dCk7XG5cblx0fSxcblx0YXBwZW5kQ2hpbGQ6ZnVuY3Rpb24obmV3Q2hpbGQpe1xuXHRcdHRocm93IG5ldyBFcnJvcihFeGNlcHRpb25NZXNzYWdlW0hJRVJBUkNIWV9SRVFVRVNUX0VSUl0pXG5cdH0sXG5cdGRlbGV0ZURhdGE6IGZ1bmN0aW9uKG9mZnNldCwgY291bnQpIHtcblx0XHR0aGlzLnJlcGxhY2VEYXRhKG9mZnNldCxjb3VudCxcIlwiKTtcblx0fSxcblx0cmVwbGFjZURhdGE6IGZ1bmN0aW9uKG9mZnNldCwgY291bnQsIHRleHQpIHtcblx0XHR2YXIgc3RhcnQgPSB0aGlzLmRhdGEuc3Vic3RyaW5nKDAsb2Zmc2V0KTtcblx0XHR2YXIgZW5kID0gdGhpcy5kYXRhLnN1YnN0cmluZyhvZmZzZXQrY291bnQpO1xuXHRcdHRleHQgPSBzdGFydCArIHRleHQgKyBlbmQ7XG5cdFx0dGhpcy5ub2RlVmFsdWUgPSB0aGlzLmRhdGEgPSB0ZXh0O1xuXHRcdHRoaXMubGVuZ3RoID0gdGV4dC5sZW5ndGg7XG5cdH1cbn1cbl9leHRlbmRzKENoYXJhY3RlckRhdGEsTm9kZSk7XG5mdW5jdGlvbiBUZXh0KCkge1xufTtcblRleHQucHJvdG90eXBlID0ge1xuXHRub2RlTmFtZSA6IFwiI3RleHRcIixcblx0bm9kZVR5cGUgOiBURVhUX05PREUsXG5cdHNwbGl0VGV4dCA6IGZ1bmN0aW9uKG9mZnNldCkge1xuXHRcdHZhciB0ZXh0ID0gdGhpcy5kYXRhO1xuXHRcdHZhciBuZXdUZXh0ID0gdGV4dC5zdWJzdHJpbmcob2Zmc2V0KTtcblx0XHR0ZXh0ID0gdGV4dC5zdWJzdHJpbmcoMCwgb2Zmc2V0KTtcblx0XHR0aGlzLmRhdGEgPSB0aGlzLm5vZGVWYWx1ZSA9IHRleHQ7XG5cdFx0dGhpcy5sZW5ndGggPSB0ZXh0Lmxlbmd0aDtcblx0XHR2YXIgbmV3Tm9kZSA9IHRoaXMub3duZXJEb2N1bWVudC5jcmVhdGVUZXh0Tm9kZShuZXdUZXh0KTtcblx0XHRpZih0aGlzLnBhcmVudE5vZGUpe1xuXHRcdFx0dGhpcy5wYXJlbnROb2RlLmluc2VydEJlZm9yZShuZXdOb2RlLCB0aGlzLm5leHRTaWJsaW5nKTtcblx0XHR9XG5cdFx0cmV0dXJuIG5ld05vZGU7XG5cdH1cbn1cbl9leHRlbmRzKFRleHQsQ2hhcmFjdGVyRGF0YSk7XG5mdW5jdGlvbiBDb21tZW50KCkge1xufTtcbkNvbW1lbnQucHJvdG90eXBlID0ge1xuXHRub2RlTmFtZSA6IFwiI2NvbW1lbnRcIixcblx0bm9kZVR5cGUgOiBDT01NRU5UX05PREVcbn1cbl9leHRlbmRzKENvbW1lbnQsQ2hhcmFjdGVyRGF0YSk7XG5cbmZ1bmN0aW9uIENEQVRBU2VjdGlvbigpIHtcbn07XG5DREFUQVNlY3Rpb24ucHJvdG90eXBlID0ge1xuXHRub2RlTmFtZSA6IFwiI2NkYXRhLXNlY3Rpb25cIixcblx0bm9kZVR5cGUgOiBDREFUQV9TRUNUSU9OX05PREVcbn1cbl9leHRlbmRzKENEQVRBU2VjdGlvbixDaGFyYWN0ZXJEYXRhKTtcblxuXG5mdW5jdGlvbiBEb2N1bWVudFR5cGUoKSB7XG59O1xuRG9jdW1lbnRUeXBlLnByb3RvdHlwZS5ub2RlVHlwZSA9IERPQ1VNRU5UX1RZUEVfTk9ERTtcbl9leHRlbmRzKERvY3VtZW50VHlwZSxOb2RlKTtcblxuZnVuY3Rpb24gTm90YXRpb24oKSB7XG59O1xuTm90YXRpb24ucHJvdG90eXBlLm5vZGVUeXBlID0gTk9UQVRJT05fTk9ERTtcbl9leHRlbmRzKE5vdGF0aW9uLE5vZGUpO1xuXG5mdW5jdGlvbiBFbnRpdHkoKSB7XG59O1xuRW50aXR5LnByb3RvdHlwZS5ub2RlVHlwZSA9IEVOVElUWV9OT0RFO1xuX2V4dGVuZHMoRW50aXR5LE5vZGUpO1xuXG5mdW5jdGlvbiBFbnRpdHlSZWZlcmVuY2UoKSB7XG59O1xuRW50aXR5UmVmZXJlbmNlLnByb3RvdHlwZS5ub2RlVHlwZSA9IEVOVElUWV9SRUZFUkVOQ0VfTk9ERTtcbl9leHRlbmRzKEVudGl0eVJlZmVyZW5jZSxOb2RlKTtcblxuZnVuY3Rpb24gRG9jdW1lbnRGcmFnbWVudCgpIHtcbn07XG5Eb2N1bWVudEZyYWdtZW50LnByb3RvdHlwZS5ub2RlTmFtZSA9XHRcIiNkb2N1bWVudC1mcmFnbWVudFwiO1xuRG9jdW1lbnRGcmFnbWVudC5wcm90b3R5cGUubm9kZVR5cGUgPVx0RE9DVU1FTlRfRlJBR01FTlRfTk9ERTtcbl9leHRlbmRzKERvY3VtZW50RnJhZ21lbnQsTm9kZSk7XG5cblxuZnVuY3Rpb24gUHJvY2Vzc2luZ0luc3RydWN0aW9uKCkge1xufVxuUHJvY2Vzc2luZ0luc3RydWN0aW9uLnByb3RvdHlwZS5ub2RlVHlwZSA9IFBST0NFU1NJTkdfSU5TVFJVQ1RJT05fTk9ERTtcbl9leHRlbmRzKFByb2Nlc3NpbmdJbnN0cnVjdGlvbixOb2RlKTtcbmZ1bmN0aW9uIFhNTFNlcmlhbGl6ZXIoKXt9XG5YTUxTZXJpYWxpemVyLnByb3RvdHlwZS5zZXJpYWxpemVUb1N0cmluZyA9IGZ1bmN0aW9uKG5vZGUsaXNIdG1sLG5vZGVGaWx0ZXIpe1xuXHRyZXR1cm4gbm9kZVNlcmlhbGl6ZVRvU3RyaW5nLmNhbGwobm9kZSxpc0h0bWwsbm9kZUZpbHRlcik7XG59XG5Ob2RlLnByb3RvdHlwZS50b1N0cmluZyA9IG5vZGVTZXJpYWxpemVUb1N0cmluZztcbmZ1bmN0aW9uIG5vZGVTZXJpYWxpemVUb1N0cmluZyhpc0h0bWwsbm9kZUZpbHRlcil7XG5cdHZhciBidWYgPSBbXTtcblx0dmFyIHJlZk5vZGUgPSB0aGlzLm5vZGVUeXBlID09IDkgJiYgdGhpcy5kb2N1bWVudEVsZW1lbnQgfHwgdGhpcztcblx0dmFyIHByZWZpeCA9IHJlZk5vZGUucHJlZml4O1xuXHR2YXIgdXJpID0gcmVmTm9kZS5uYW1lc3BhY2VVUkk7XG5cblx0aWYodXJpICYmIHByZWZpeCA9PSBudWxsKXtcblx0XHQvL2NvbnNvbGUubG9nKHByZWZpeClcblx0XHR2YXIgcHJlZml4ID0gcmVmTm9kZS5sb29rdXBQcmVmaXgodXJpKTtcblx0XHRpZihwcmVmaXggPT0gbnVsbCl7XG5cdFx0XHQvL2lzSFRNTCA9IHRydWU7XG5cdFx0XHR2YXIgdmlzaWJsZU5hbWVzcGFjZXM9W1xuXHRcdFx0e25hbWVzcGFjZTp1cmkscHJlZml4Om51bGx9XG5cdFx0XHQvL3tuYW1lc3BhY2U6dXJpLHByZWZpeDonJ31cblx0XHRcdF1cblx0XHR9XG5cdH1cblx0c2VyaWFsaXplVG9TdHJpbmcodGhpcyxidWYsaXNIdG1sLG5vZGVGaWx0ZXIsdmlzaWJsZU5hbWVzcGFjZXMpO1xuXHQvL2NvbnNvbGUubG9nKCcjIyMnLHRoaXMubm9kZVR5cGUsdXJpLHByZWZpeCxidWYuam9pbignJykpXG5cdHJldHVybiBidWYuam9pbignJyk7XG59XG5cbmZ1bmN0aW9uIG5lZWROYW1lc3BhY2VEZWZpbmUobm9kZSwgaXNIVE1MLCB2aXNpYmxlTmFtZXNwYWNlcykge1xuXHR2YXIgcHJlZml4ID0gbm9kZS5wcmVmaXggfHwgJyc7XG5cdHZhciB1cmkgPSBub2RlLm5hbWVzcGFjZVVSSTtcblx0Ly8gQWNjb3JkaW5nIHRvIFtOYW1lc3BhY2VzIGluIFhNTCAxLjBdKGh0dHBzOi8vd3d3LnczLm9yZy9UUi9SRUMteG1sLW5hbWVzLyNucy11c2luZykgLFxuXHQvLyBhbmQgbW9yZSBzcGVjaWZpY2FsbHkgaHR0cHM6Ly93d3cudzMub3JnL1RSL1JFQy14bWwtbmFtZXMvI25zYy1Ob1ByZWZpeFVuZGVjbCA6XG5cdC8vID4gSW4gYSBuYW1lc3BhY2UgZGVjbGFyYXRpb24gZm9yIGEgcHJlZml4IFsuLi5dLCB0aGUgYXR0cmlidXRlIHZhbHVlIE1VU1QgTk9UIGJlIGVtcHR5LlxuXHQvLyBpbiBhIHNpbWlsYXIgbWFubmVyIFtOYW1lc3BhY2VzIGluIFhNTCAxLjFdKGh0dHBzOi8vd3d3LnczLm9yZy9UUi94bWwtbmFtZXMxMS8jbnMtdXNpbmcpXG5cdC8vIGFuZCBtb3JlIHNwZWNpZmljYWxseSBodHRwczovL3d3dy53My5vcmcvVFIveG1sLW5hbWVzMTEvI25zYy1OU0RlY2xhcmVkIDpcblx0Ly8gPiBbLi4uXSBGdXJ0aGVybW9yZSwgdGhlIGF0dHJpYnV0ZSB2YWx1ZSBbLi4uXSBtdXN0IG5vdCBiZSBhbiBlbXB0eSBzdHJpbmcuXG5cdC8vIHNvIHNlcmlhbGl6aW5nIGVtcHR5IG5hbWVzcGFjZSB2YWx1ZSBsaWtlIHhtbG5zOmRzPVwiXCIgd291bGQgcHJvZHVjZSBhbiBpbnZhbGlkIFhNTCBkb2N1bWVudC5cblx0aWYgKCF1cmkpIHtcblx0XHRyZXR1cm4gZmFsc2U7XG5cdH1cblx0aWYgKHByZWZpeCA9PT0gXCJ4bWxcIiAmJiB1cmkgPT09IE5BTUVTUEFDRS5YTUwgfHwgdXJpID09PSBOQU1FU1BBQ0UuWE1MTlMpIHtcblx0XHRyZXR1cm4gZmFsc2U7XG5cdH1cblxuXHR2YXIgaSA9IHZpc2libGVOYW1lc3BhY2VzLmxlbmd0aFxuXHR3aGlsZSAoaS0tKSB7XG5cdFx0dmFyIG5zID0gdmlzaWJsZU5hbWVzcGFjZXNbaV07XG5cdFx0Ly8gZ2V0IG5hbWVzcGFjZSBwcmVmaXhcblx0XHRpZiAobnMucHJlZml4ID09PSBwcmVmaXgpIHtcblx0XHRcdHJldHVybiBucy5uYW1lc3BhY2UgIT09IHVyaTtcblx0XHR9XG5cdH1cblx0cmV0dXJuIHRydWU7XG59XG4vKipcbiAqIFdlbGwtZm9ybWVkIGNvbnN0cmFpbnQ6IE5vIDwgaW4gQXR0cmlidXRlIFZhbHVlc1xuICogPiBUaGUgcmVwbGFjZW1lbnQgdGV4dCBvZiBhbnkgZW50aXR5IHJlZmVycmVkIHRvIGRpcmVjdGx5IG9yIGluZGlyZWN0bHlcbiAqID4gaW4gYW4gYXR0cmlidXRlIHZhbHVlIG11c3Qgbm90IGNvbnRhaW4gYSA8LlxuICogQHNlZSBodHRwczovL3d3dy53My5vcmcvVFIveG1sMTEvI0NsZWFuQXR0clZhbHNcbiAqIEBzZWUgaHR0cHM6Ly93d3cudzMub3JnL1RSL3htbDExLyNOVC1BdHRWYWx1ZVxuICpcbiAqIExpdGVyYWwgd2hpdGVzcGFjZSBvdGhlciB0aGFuIHNwYWNlIHRoYXQgYXBwZWFyIGluIGF0dHJpYnV0ZSB2YWx1ZXNcbiAqIGFyZSBzZXJpYWxpemVkIGFzIHRoZWlyIGVudGl0eSByZWZlcmVuY2VzLCBzbyB0aGV5IHdpbGwgYmUgcHJlc2VydmVkLlxuICogKEluIGNvbnRyYXN0IHRvIHdoaXRlc3BhY2UgbGl0ZXJhbHMgaW4gdGhlIGlucHV0IHdoaWNoIGFyZSBub3JtYWxpemVkIHRvIHNwYWNlcylcbiAqIEBzZWUgaHR0cHM6Ly93d3cudzMub3JnL1RSL3htbDExLyNBVk5vcm1hbGl6ZVxuICogQHNlZSBodHRwczovL3czYy5naXRodWIuaW8vRE9NLVBhcnNpbmcvI3NlcmlhbGl6aW5nLWFuLWVsZW1lbnQtcy1hdHRyaWJ1dGVzXG4gKi9cbmZ1bmN0aW9uIGFkZFNlcmlhbGl6ZWRBdHRyaWJ1dGUoYnVmLCBxdWFsaWZpZWROYW1lLCB2YWx1ZSkge1xuXHRidWYucHVzaCgnICcsIHF1YWxpZmllZE5hbWUsICc9XCInLCB2YWx1ZS5yZXBsYWNlKC9bPD4mXCJcXHRcXG5cXHJdL2csIF94bWxFbmNvZGVyKSwgJ1wiJylcbn1cblxuZnVuY3Rpb24gc2VyaWFsaXplVG9TdHJpbmcobm9kZSxidWYsaXNIVE1MLG5vZGVGaWx0ZXIsdmlzaWJsZU5hbWVzcGFjZXMpe1xuXHRpZiAoIXZpc2libGVOYW1lc3BhY2VzKSB7XG5cdFx0dmlzaWJsZU5hbWVzcGFjZXMgPSBbXTtcblx0fVxuXG5cdGlmKG5vZGVGaWx0ZXIpe1xuXHRcdG5vZGUgPSBub2RlRmlsdGVyKG5vZGUpO1xuXHRcdGlmKG5vZGUpe1xuXHRcdFx0aWYodHlwZW9mIG5vZGUgPT0gJ3N0cmluZycpe1xuXHRcdFx0XHRidWYucHVzaChub2RlKTtcblx0XHRcdFx0cmV0dXJuO1xuXHRcdFx0fVxuXHRcdH1lbHNle1xuXHRcdFx0cmV0dXJuO1xuXHRcdH1cblx0XHQvL2J1Zi5zb3J0LmFwcGx5KGF0dHJzLCBhdHRyaWJ1dGVTb3J0ZXIpO1xuXHR9XG5cblx0c3dpdGNoKG5vZGUubm9kZVR5cGUpe1xuXHRjYXNlIEVMRU1FTlRfTk9ERTpcblx0XHR2YXIgYXR0cnMgPSBub2RlLmF0dHJpYnV0ZXM7XG5cdFx0dmFyIGxlbiA9IGF0dHJzLmxlbmd0aDtcblx0XHR2YXIgY2hpbGQgPSBub2RlLmZpcnN0Q2hpbGQ7XG5cdFx0dmFyIG5vZGVOYW1lID0gbm9kZS50YWdOYW1lO1xuXG5cdFx0aXNIVE1MID0gTkFNRVNQQUNFLmlzSFRNTChub2RlLm5hbWVzcGFjZVVSSSkgfHwgaXNIVE1MXG5cblx0XHR2YXIgcHJlZml4ZWROb2RlTmFtZSA9IG5vZGVOYW1lXG5cdFx0aWYgKCFpc0hUTUwgJiYgIW5vZGUucHJlZml4ICYmIG5vZGUubmFtZXNwYWNlVVJJKSB7XG5cdFx0XHR2YXIgZGVmYXVsdE5TXG5cdFx0XHQvLyBsb29rdXAgY3VycmVudCBkZWZhdWx0IG5zIGZyb20gYHhtbG5zYCBhdHRyaWJ1dGVcblx0XHRcdGZvciAodmFyIGFpID0gMDsgYWkgPCBhdHRycy5sZW5ndGg7IGFpKyspIHtcblx0XHRcdFx0aWYgKGF0dHJzLml0ZW0oYWkpLm5hbWUgPT09ICd4bWxucycpIHtcblx0XHRcdFx0XHRkZWZhdWx0TlMgPSBhdHRycy5pdGVtKGFpKS52YWx1ZVxuXHRcdFx0XHRcdGJyZWFrXG5cdFx0XHRcdH1cblx0XHRcdH1cblx0XHRcdGlmICghZGVmYXVsdE5TKSB7XG5cdFx0XHRcdC8vIGxvb2t1cCBjdXJyZW50IGRlZmF1bHQgbnMgaW4gdmlzaWJsZU5hbWVzcGFjZXNcblx0XHRcdFx0Zm9yICh2YXIgbnNpID0gdmlzaWJsZU5hbWVzcGFjZXMubGVuZ3RoIC0gMTsgbnNpID49IDA7IG5zaS0tKSB7XG5cdFx0XHRcdFx0dmFyIG5hbWVzcGFjZSA9IHZpc2libGVOYW1lc3BhY2VzW25zaV1cblx0XHRcdFx0XHRpZiAobmFtZXNwYWNlLnByZWZpeCA9PT0gJycgJiYgbmFtZXNwYWNlLm5hbWVzcGFjZSA9PT0gbm9kZS5uYW1lc3BhY2VVUkkpIHtcblx0XHRcdFx0XHRcdGRlZmF1bHROUyA9IG5hbWVzcGFjZS5uYW1lc3BhY2Vcblx0XHRcdFx0XHRcdGJyZWFrXG5cdFx0XHRcdFx0fVxuXHRcdFx0XHR9XG5cdFx0XHR9XG5cdFx0XHRpZiAoZGVmYXVsdE5TICE9PSBub2RlLm5hbWVzcGFjZVVSSSkge1xuXHRcdFx0XHRmb3IgKHZhciBuc2kgPSB2aXNpYmxlTmFtZXNwYWNlcy5sZW5ndGggLSAxOyBuc2kgPj0gMDsgbnNpLS0pIHtcblx0XHRcdFx0XHR2YXIgbmFtZXNwYWNlID0gdmlzaWJsZU5hbWVzcGFjZXNbbnNpXVxuXHRcdFx0XHRcdGlmIChuYW1lc3BhY2UubmFtZXNwYWNlID09PSBub2RlLm5hbWVzcGFjZVVSSSkge1xuXHRcdFx0XHRcdFx0aWYgKG5hbWVzcGFjZS5wcmVmaXgpIHtcblx0XHRcdFx0XHRcdFx0cHJlZml4ZWROb2RlTmFtZSA9IG5hbWVzcGFjZS5wcmVmaXggKyAnOicgKyBub2RlTmFtZVxuXHRcdFx0XHRcdFx0fVxuXHRcdFx0XHRcdFx0YnJlYWtcblx0XHRcdFx0XHR9XG5cdFx0XHRcdH1cblx0XHRcdH1cblx0XHR9XG5cblx0XHRidWYucHVzaCgnPCcsIHByZWZpeGVkTm9kZU5hbWUpO1xuXG5cdFx0Zm9yKHZhciBpPTA7aTxsZW47aSsrKXtcblx0XHRcdC8vIGFkZCBuYW1lc3BhY2VzIGZvciBhdHRyaWJ1dGVzXG5cdFx0XHR2YXIgYXR0ciA9IGF0dHJzLml0ZW0oaSk7XG5cdFx0XHRpZiAoYXR0ci5wcmVmaXggPT0gJ3htbG5zJykge1xuXHRcdFx0XHR2aXNpYmxlTmFtZXNwYWNlcy5wdXNoKHsgcHJlZml4OiBhdHRyLmxvY2FsTmFtZSwgbmFtZXNwYWNlOiBhdHRyLnZhbHVlIH0pO1xuXHRcdFx0fWVsc2UgaWYoYXR0ci5ub2RlTmFtZSA9PSAneG1sbnMnKXtcblx0XHRcdFx0dmlzaWJsZU5hbWVzcGFjZXMucHVzaCh7IHByZWZpeDogJycsIG5hbWVzcGFjZTogYXR0ci52YWx1ZSB9KTtcblx0XHRcdH1cblx0XHR9XG5cblx0XHRmb3IodmFyIGk9MDtpPGxlbjtpKyspe1xuXHRcdFx0dmFyIGF0dHIgPSBhdHRycy5pdGVtKGkpO1xuXHRcdFx0aWYgKG5lZWROYW1lc3BhY2VEZWZpbmUoYXR0cixpc0hUTUwsIHZpc2libGVOYW1lc3BhY2VzKSkge1xuXHRcdFx0XHR2YXIgcHJlZml4ID0gYXR0ci5wcmVmaXh8fCcnO1xuXHRcdFx0XHR2YXIgdXJpID0gYXR0ci5uYW1lc3BhY2VVUkk7XG5cdFx0XHRcdGFkZFNlcmlhbGl6ZWRBdHRyaWJ1dGUoYnVmLCBwcmVmaXggPyAneG1sbnM6JyArIHByZWZpeCA6IFwieG1sbnNcIiwgdXJpKTtcblx0XHRcdFx0dmlzaWJsZU5hbWVzcGFjZXMucHVzaCh7IHByZWZpeDogcHJlZml4LCBuYW1lc3BhY2U6dXJpIH0pO1xuXHRcdFx0fVxuXHRcdFx0c2VyaWFsaXplVG9TdHJpbmcoYXR0cixidWYsaXNIVE1MLG5vZGVGaWx0ZXIsdmlzaWJsZU5hbWVzcGFjZXMpO1xuXHRcdH1cblxuXHRcdC8vIGFkZCBuYW1lc3BhY2UgZm9yIGN1cnJlbnQgbm9kZVxuXHRcdGlmIChub2RlTmFtZSA9PT0gcHJlZml4ZWROb2RlTmFtZSAmJiBuZWVkTmFtZXNwYWNlRGVmaW5lKG5vZGUsIGlzSFRNTCwgdmlzaWJsZU5hbWVzcGFjZXMpKSB7XG5cdFx0XHR2YXIgcHJlZml4ID0gbm9kZS5wcmVmaXh8fCcnO1xuXHRcdFx0dmFyIHVyaSA9IG5vZGUubmFtZXNwYWNlVVJJO1xuXHRcdFx0YWRkU2VyaWFsaXplZEF0dHJpYnV0ZShidWYsIHByZWZpeCA/ICd4bWxuczonICsgcHJlZml4IDogXCJ4bWxuc1wiLCB1cmkpO1xuXHRcdFx0dmlzaWJsZU5hbWVzcGFjZXMucHVzaCh7IHByZWZpeDogcHJlZml4LCBuYW1lc3BhY2U6dXJpIH0pO1xuXHRcdH1cblxuXHRcdGlmKGNoaWxkIHx8IGlzSFRNTCAmJiAhL14oPzptZXRhfGxpbmt8aW1nfGJyfGhyfGlucHV0KSQvaS50ZXN0KG5vZGVOYW1lKSl7XG5cdFx0XHRidWYucHVzaCgnPicpO1xuXHRcdFx0Ly9pZiBpcyBjZGF0YSBjaGlsZCBub2RlXG5cdFx0XHRpZihpc0hUTUwgJiYgL15zY3JpcHQkL2kudGVzdChub2RlTmFtZSkpe1xuXHRcdFx0XHR3aGlsZShjaGlsZCl7XG5cdFx0XHRcdFx0aWYoY2hpbGQuZGF0YSl7XG5cdFx0XHRcdFx0XHRidWYucHVzaChjaGlsZC5kYXRhKTtcblx0XHRcdFx0XHR9ZWxzZXtcblx0XHRcdFx0XHRcdHNlcmlhbGl6ZVRvU3RyaW5nKGNoaWxkLCBidWYsIGlzSFRNTCwgbm9kZUZpbHRlciwgdmlzaWJsZU5hbWVzcGFjZXMuc2xpY2UoKSk7XG5cdFx0XHRcdFx0fVxuXHRcdFx0XHRcdGNoaWxkID0gY2hpbGQubmV4dFNpYmxpbmc7XG5cdFx0XHRcdH1cblx0XHRcdH1lbHNlXG5cdFx0XHR7XG5cdFx0XHRcdHdoaWxlKGNoaWxkKXtcblx0XHRcdFx0XHRzZXJpYWxpemVUb1N0cmluZyhjaGlsZCwgYnVmLCBpc0hUTUwsIG5vZGVGaWx0ZXIsIHZpc2libGVOYW1lc3BhY2VzLnNsaWNlKCkpO1xuXHRcdFx0XHRcdGNoaWxkID0gY2hpbGQubmV4dFNpYmxpbmc7XG5cdFx0XHRcdH1cblx0XHRcdH1cblx0XHRcdGJ1Zi5wdXNoKCc8LycscHJlZml4ZWROb2RlTmFtZSwnPicpO1xuXHRcdH1lbHNle1xuXHRcdFx0YnVmLnB1c2goJy8+Jyk7XG5cdFx0fVxuXHRcdC8vIHJlbW92ZSBhZGRlZCB2aXNpYmxlIG5hbWVzcGFjZXNcblx0XHQvL3Zpc2libGVOYW1lc3BhY2VzLmxlbmd0aCA9IHN0YXJ0VmlzaWJsZU5hbWVzcGFjZXM7XG5cdFx0cmV0dXJuO1xuXHRjYXNlIERPQ1VNRU5UX05PREU6XG5cdGNhc2UgRE9DVU1FTlRfRlJBR01FTlRfTk9ERTpcblx0XHR2YXIgY2hpbGQgPSBub2RlLmZpcnN0Q2hpbGQ7XG5cdFx0d2hpbGUoY2hpbGQpe1xuXHRcdFx0c2VyaWFsaXplVG9TdHJpbmcoY2hpbGQsIGJ1ZiwgaXNIVE1MLCBub2RlRmlsdGVyLCB2aXNpYmxlTmFtZXNwYWNlcy5zbGljZSgpKTtcblx0XHRcdGNoaWxkID0gY2hpbGQubmV4dFNpYmxpbmc7XG5cdFx0fVxuXHRcdHJldHVybjtcblx0Y2FzZSBBVFRSSUJVVEVfTk9ERTpcblx0XHRyZXR1cm4gYWRkU2VyaWFsaXplZEF0dHJpYnV0ZShidWYsIG5vZGUubmFtZSwgbm9kZS52YWx1ZSk7XG5cdGNhc2UgVEVYVF9OT0RFOlxuXHRcdC8qKlxuXHRcdCAqIFRoZSBhbXBlcnNhbmQgY2hhcmFjdGVyICgmKSBhbmQgdGhlIGxlZnQgYW5nbGUgYnJhY2tldCAoPCkgbXVzdCBub3QgYXBwZWFyIGluIHRoZWlyIGxpdGVyYWwgZm9ybSxcblx0XHQgKiBleGNlcHQgd2hlbiB1c2VkIGFzIG1hcmt1cCBkZWxpbWl0ZXJzLCBvciB3aXRoaW4gYSBjb21tZW50LCBhIHByb2Nlc3NpbmcgaW5zdHJ1Y3Rpb24sIG9yIGEgQ0RBVEEgc2VjdGlvbi5cblx0XHQgKiBJZiB0aGV5IGFyZSBuZWVkZWQgZWxzZXdoZXJlLCB0aGV5IG11c3QgYmUgZXNjYXBlZCB1c2luZyBlaXRoZXIgbnVtZXJpYyBjaGFyYWN0ZXIgcmVmZXJlbmNlcyBvciB0aGUgc3RyaW5nc1xuXHRcdCAqIGAmYW1wO2AgYW5kIGAmbHQ7YCByZXNwZWN0aXZlbHkuXG5cdFx0ICogVGhlIHJpZ2h0IGFuZ2xlIGJyYWNrZXQgKD4pIG1heSBiZSByZXByZXNlbnRlZCB1c2luZyB0aGUgc3RyaW5nIFwiICZndDsgXCIsIGFuZCBtdXN0LCBmb3IgY29tcGF0aWJpbGl0eSxcblx0XHQgKiBiZSBlc2NhcGVkIHVzaW5nIGVpdGhlciBgJmd0O2Agb3IgYSBjaGFyYWN0ZXIgcmVmZXJlbmNlIHdoZW4gaXQgYXBwZWFycyBpbiB0aGUgc3RyaW5nIGBdXT5gIGluIGNvbnRlbnQsXG5cdFx0ICogd2hlbiB0aGF0IHN0cmluZyBpcyBub3QgbWFya2luZyB0aGUgZW5kIG9mIGEgQ0RBVEEgc2VjdGlvbi5cblx0XHQgKlxuXHRcdCAqIEluIHRoZSBjb250ZW50IG9mIGVsZW1lbnRzLCBjaGFyYWN0ZXIgZGF0YSBpcyBhbnkgc3RyaW5nIG9mIGNoYXJhY3RlcnNcblx0XHQgKiB3aGljaCBkb2VzIG5vdCBjb250YWluIHRoZSBzdGFydC1kZWxpbWl0ZXIgb2YgYW55IG1hcmt1cFxuXHRcdCAqIGFuZCBkb2VzIG5vdCBpbmNsdWRlIHRoZSBDREFUQS1zZWN0aW9uLWNsb3NlIGRlbGltaXRlciwgYF1dPmAuXG5cdFx0ICpcblx0XHQgKiBAc2VlIGh0dHBzOi8vd3d3LnczLm9yZy9UUi94bWwvI05ULUNoYXJEYXRhXG5cdFx0ICogQHNlZSBodHRwczovL3czYy5naXRodWIuaW8vRE9NLVBhcnNpbmcvI3htbC1zZXJpYWxpemluZy1hLXRleHQtbm9kZVxuXHRcdCAqL1xuXHRcdHJldHVybiBidWYucHVzaChub2RlLmRhdGFcblx0XHRcdC5yZXBsYWNlKC9bPCY+XS9nLF94bWxFbmNvZGVyKVxuXHRcdCk7XG5cdGNhc2UgQ0RBVEFfU0VDVElPTl9OT0RFOlxuXHRcdHJldHVybiBidWYucHVzaCggJzwhW0NEQVRBWycsbm9kZS5kYXRhLCddXT4nKTtcblx0Y2FzZSBDT01NRU5UX05PREU6XG5cdFx0cmV0dXJuIGJ1Zi5wdXNoKCBcIjwhLS1cIixub2RlLmRhdGEsXCItLT5cIik7XG5cdGNhc2UgRE9DVU1FTlRfVFlQRV9OT0RFOlxuXHRcdHZhciBwdWJpZCA9IG5vZGUucHVibGljSWQ7XG5cdFx0dmFyIHN5c2lkID0gbm9kZS5zeXN0ZW1JZDtcblx0XHRidWYucHVzaCgnPCFET0NUWVBFICcsbm9kZS5uYW1lKTtcblx0XHRpZihwdWJpZCl7XG5cdFx0XHRidWYucHVzaCgnIFBVQkxJQyAnLCBwdWJpZCk7XG5cdFx0XHRpZiAoc3lzaWQgJiYgc3lzaWQhPScuJykge1xuXHRcdFx0XHRidWYucHVzaCgnICcsIHN5c2lkKTtcblx0XHRcdH1cblx0XHRcdGJ1Zi5wdXNoKCc+Jyk7XG5cdFx0fWVsc2UgaWYoc3lzaWQgJiYgc3lzaWQhPScuJyl7XG5cdFx0XHRidWYucHVzaCgnIFNZU1RFTSAnLCBzeXNpZCwgJz4nKTtcblx0XHR9ZWxzZXtcblx0XHRcdHZhciBzdWIgPSBub2RlLmludGVybmFsU3Vic2V0O1xuXHRcdFx0aWYoc3ViKXtcblx0XHRcdFx0YnVmLnB1c2goXCIgW1wiLHN1YixcIl1cIik7XG5cdFx0XHR9XG5cdFx0XHRidWYucHVzaChcIj5cIik7XG5cdFx0fVxuXHRcdHJldHVybjtcblx0Y2FzZSBQUk9DRVNTSU5HX0lOU1RSVUNUSU9OX05PREU6XG5cdFx0cmV0dXJuIGJ1Zi5wdXNoKCBcIjw/XCIsbm9kZS50YXJnZXQsXCIgXCIsbm9kZS5kYXRhLFwiPz5cIik7XG5cdGNhc2UgRU5USVRZX1JFRkVSRU5DRV9OT0RFOlxuXHRcdHJldHVybiBidWYucHVzaCggJyYnLG5vZGUubm9kZU5hbWUsJzsnKTtcblx0Ly9jYXNlIEVOVElUWV9OT0RFOlxuXHQvL2Nhc2UgTk9UQVRJT05fTk9ERTpcblx0ZGVmYXVsdDpcblx0XHRidWYucHVzaCgnPz8nLG5vZGUubm9kZU5hbWUpO1xuXHR9XG59XG5mdW5jdGlvbiBpbXBvcnROb2RlKGRvYyxub2RlLGRlZXApe1xuXHR2YXIgbm9kZTI7XG5cdHN3aXRjaCAobm9kZS5ub2RlVHlwZSkge1xuXHRjYXNlIEVMRU1FTlRfTk9ERTpcblx0XHRub2RlMiA9IG5vZGUuY2xvbmVOb2RlKGZhbHNlKTtcblx0XHRub2RlMi5vd25lckRvY3VtZW50ID0gZG9jO1xuXHRcdC8vdmFyIGF0dHJzID0gbm9kZTIuYXR0cmlidXRlcztcblx0XHQvL3ZhciBsZW4gPSBhdHRycy5sZW5ndGg7XG5cdFx0Ly9mb3IodmFyIGk9MDtpPGxlbjtpKyspe1xuXHRcdFx0Ly9ub2RlMi5zZXRBdHRyaWJ1dGVOb2RlTlMoaW1wb3J0Tm9kZShkb2MsYXR0cnMuaXRlbShpKSxkZWVwKSk7XG5cdFx0Ly99XG5cdGNhc2UgRE9DVU1FTlRfRlJBR01FTlRfTk9ERTpcblx0XHRicmVhaztcblx0Y2FzZSBBVFRSSUJVVEVfTk9ERTpcblx0XHRkZWVwID0gdHJ1ZTtcblx0XHRicmVhaztcblx0Ly9jYXNlIEVOVElUWV9SRUZFUkVOQ0VfTk9ERTpcblx0Ly9jYXNlIFBST0NFU1NJTkdfSU5TVFJVQ1RJT05fTk9ERTpcblx0Ly8vL2Nhc2UgVEVYVF9OT0RFOlxuXHQvL2Nhc2UgQ0RBVEFfU0VDVElPTl9OT0RFOlxuXHQvL2Nhc2UgQ09NTUVOVF9OT0RFOlxuXHQvL1x0ZGVlcCA9IGZhbHNlO1xuXHQvL1x0YnJlYWs7XG5cdC8vY2FzZSBET0NVTUVOVF9OT0RFOlxuXHQvL2Nhc2UgRE9DVU1FTlRfVFlQRV9OT0RFOlxuXHQvL2Nhbm5vdCBiZSBpbXBvcnRlZC5cblx0Ly9jYXNlIEVOVElUWV9OT0RFOlxuXHQvL2Nhc2UgTk9UQVRJT05fTk9ERe+8mlxuXHQvL2NhbiBub3QgaGl0IGluIGxldmVsM1xuXHQvL2RlZmF1bHQ6dGhyb3cgZTtcblx0fVxuXHRpZighbm9kZTIpe1xuXHRcdG5vZGUyID0gbm9kZS5jbG9uZU5vZGUoZmFsc2UpOy8vZmFsc2Vcblx0fVxuXHRub2RlMi5vd25lckRvY3VtZW50ID0gZG9jO1xuXHRub2RlMi5wYXJlbnROb2RlID0gbnVsbDtcblx0aWYoZGVlcCl7XG5cdFx0dmFyIGNoaWxkID0gbm9kZS5maXJzdENoaWxkO1xuXHRcdHdoaWxlKGNoaWxkKXtcblx0XHRcdG5vZGUyLmFwcGVuZENoaWxkKGltcG9ydE5vZGUoZG9jLGNoaWxkLGRlZXApKTtcblx0XHRcdGNoaWxkID0gY2hpbGQubmV4dFNpYmxpbmc7XG5cdFx0fVxuXHR9XG5cdHJldHVybiBub2RlMjtcbn1cbi8vXG4vL3ZhciBfcmVsYXRpb25NYXAgPSB7Zmlyc3RDaGlsZDoxLGxhc3RDaGlsZDoxLHByZXZpb3VzU2libGluZzoxLG5leHRTaWJsaW5nOjEsXG4vL1x0XHRcdFx0XHRhdHRyaWJ1dGVzOjEsY2hpbGROb2RlczoxLHBhcmVudE5vZGU6MSxkb2N1bWVudEVsZW1lbnQ6MSxkb2N0eXBlLH07XG5mdW5jdGlvbiBjbG9uZU5vZGUoZG9jLG5vZGUsZGVlcCl7XG5cdHZhciBub2RlMiA9IG5ldyBub2RlLmNvbnN0cnVjdG9yKCk7XG5cdGZvciAodmFyIG4gaW4gbm9kZSkge1xuXHRcdGlmIChPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwobm9kZSwgbikpIHtcblx0XHRcdHZhciB2ID0gbm9kZVtuXTtcblx0XHRcdGlmICh0eXBlb2YgdiAhPSBcIm9iamVjdFwiKSB7XG5cdFx0XHRcdGlmICh2ICE9IG5vZGUyW25dKSB7XG5cdFx0XHRcdFx0bm9kZTJbbl0gPSB2O1xuXHRcdFx0XHR9XG5cdFx0XHR9XG5cdFx0fVxuXHR9XG5cdGlmKG5vZGUuY2hpbGROb2Rlcyl7XG5cdFx0bm9kZTIuY2hpbGROb2RlcyA9IG5ldyBOb2RlTGlzdCgpO1xuXHR9XG5cdG5vZGUyLm93bmVyRG9jdW1lbnQgPSBkb2M7XG5cdHN3aXRjaCAobm9kZTIubm9kZVR5cGUpIHtcblx0Y2FzZSBFTEVNRU5UX05PREU6XG5cdFx0dmFyIGF0dHJzXHQ9IG5vZGUuYXR0cmlidXRlcztcblx0XHR2YXIgYXR0cnMyXHQ9IG5vZGUyLmF0dHJpYnV0ZXMgPSBuZXcgTmFtZWROb2RlTWFwKCk7XG5cdFx0dmFyIGxlbiA9IGF0dHJzLmxlbmd0aFxuXHRcdGF0dHJzMi5fb3duZXJFbGVtZW50ID0gbm9kZTI7XG5cdFx0Zm9yKHZhciBpPTA7aTxsZW47aSsrKXtcblx0XHRcdG5vZGUyLnNldEF0dHJpYnV0ZU5vZGUoY2xvbmVOb2RlKGRvYyxhdHRycy5pdGVtKGkpLHRydWUpKTtcblx0XHR9XG5cdFx0YnJlYWs7O1xuXHRjYXNlIEFUVFJJQlVURV9OT0RFOlxuXHRcdGRlZXAgPSB0cnVlO1xuXHR9XG5cdGlmKGRlZXApe1xuXHRcdHZhciBjaGlsZCA9IG5vZGUuZmlyc3RDaGlsZDtcblx0XHR3aGlsZShjaGlsZCl7XG5cdFx0XHRub2RlMi5hcHBlbmRDaGlsZChjbG9uZU5vZGUoZG9jLGNoaWxkLGRlZXApKTtcblx0XHRcdGNoaWxkID0gY2hpbGQubmV4dFNpYmxpbmc7XG5cdFx0fVxuXHR9XG5cdHJldHVybiBub2RlMjtcbn1cblxuZnVuY3Rpb24gX19zZXRfXyhvYmplY3Qsa2V5LHZhbHVlKXtcblx0b2JqZWN0W2tleV0gPSB2YWx1ZVxufVxuLy9kbyBkeW5hbWljXG50cnl7XG5cdGlmKE9iamVjdC5kZWZpbmVQcm9wZXJ0eSl7XG5cdFx0T2JqZWN0LmRlZmluZVByb3BlcnR5KExpdmVOb2RlTGlzdC5wcm90b3R5cGUsJ2xlbmd0aCcse1xuXHRcdFx0Z2V0OmZ1bmN0aW9uKCl7XG5cdFx0XHRcdF91cGRhdGVMaXZlTGlzdCh0aGlzKTtcblx0XHRcdFx0cmV0dXJuIHRoaXMuJCRsZW5ndGg7XG5cdFx0XHR9XG5cdFx0fSk7XG5cblx0XHRPYmplY3QuZGVmaW5lUHJvcGVydHkoTm9kZS5wcm90b3R5cGUsJ3RleHRDb250ZW50Jyx7XG5cdFx0XHRnZXQ6ZnVuY3Rpb24oKXtcblx0XHRcdFx0cmV0dXJuIGdldFRleHRDb250ZW50KHRoaXMpO1xuXHRcdFx0fSxcblxuXHRcdFx0c2V0OmZ1bmN0aW9uKGRhdGEpe1xuXHRcdFx0XHRzd2l0Y2godGhpcy5ub2RlVHlwZSl7XG5cdFx0XHRcdGNhc2UgRUxFTUVOVF9OT0RFOlxuXHRcdFx0XHRjYXNlIERPQ1VNRU5UX0ZSQUdNRU5UX05PREU6XG5cdFx0XHRcdFx0d2hpbGUodGhpcy5maXJzdENoaWxkKXtcblx0XHRcdFx0XHRcdHRoaXMucmVtb3ZlQ2hpbGQodGhpcy5maXJzdENoaWxkKTtcblx0XHRcdFx0XHR9XG5cdFx0XHRcdFx0aWYoZGF0YSB8fCBTdHJpbmcoZGF0YSkpe1xuXHRcdFx0XHRcdFx0dGhpcy5hcHBlbmRDaGlsZCh0aGlzLm93bmVyRG9jdW1lbnQuY3JlYXRlVGV4dE5vZGUoZGF0YSkpO1xuXHRcdFx0XHRcdH1cblx0XHRcdFx0XHRicmVhaztcblxuXHRcdFx0XHRkZWZhdWx0OlxuXHRcdFx0XHRcdHRoaXMuZGF0YSA9IGRhdGE7XG5cdFx0XHRcdFx0dGhpcy52YWx1ZSA9IGRhdGE7XG5cdFx0XHRcdFx0dGhpcy5ub2RlVmFsdWUgPSBkYXRhO1xuXHRcdFx0XHR9XG5cdFx0XHR9XG5cdFx0fSlcblxuXHRcdGZ1bmN0aW9uIGdldFRleHRDb250ZW50KG5vZGUpe1xuXHRcdFx0c3dpdGNoKG5vZGUubm9kZVR5cGUpe1xuXHRcdFx0Y2FzZSBFTEVNRU5UX05PREU6XG5cdFx0XHRjYXNlIERPQ1VNRU5UX0ZSQUdNRU5UX05PREU6XG5cdFx0XHRcdHZhciBidWYgPSBbXTtcblx0XHRcdFx0bm9kZSA9IG5vZGUuZmlyc3RDaGlsZDtcblx0XHRcdFx0d2hpbGUobm9kZSl7XG5cdFx0XHRcdFx0aWYobm9kZS5ub2RlVHlwZSE9PTcgJiYgbm9kZS5ub2RlVHlwZSAhPT04KXtcblx0XHRcdFx0XHRcdGJ1Zi5wdXNoKGdldFRleHRDb250ZW50KG5vZGUpKTtcblx0XHRcdFx0XHR9XG5cdFx0XHRcdFx0bm9kZSA9IG5vZGUubmV4dFNpYmxpbmc7XG5cdFx0XHRcdH1cblx0XHRcdFx0cmV0dXJuIGJ1Zi5qb2luKCcnKTtcblx0XHRcdGRlZmF1bHQ6XG5cdFx0XHRcdHJldHVybiBub2RlLm5vZGVWYWx1ZTtcblx0XHRcdH1cblx0XHR9XG5cblx0XHRfX3NldF9fID0gZnVuY3Rpb24ob2JqZWN0LGtleSx2YWx1ZSl7XG5cdFx0XHQvL2NvbnNvbGUubG9nKHZhbHVlKVxuXHRcdFx0b2JqZWN0WyckJCcra2V5XSA9IHZhbHVlXG5cdFx0fVxuXHR9XG59Y2F0Y2goZSl7Ly9pZThcbn1cblxuLy9pZih0eXBlb2YgcmVxdWlyZSA9PSAnZnVuY3Rpb24nKXtcblx0ZXhwb3J0cy5Eb2N1bWVudFR5cGUgPSBEb2N1bWVudFR5cGU7XG5cdGV4cG9ydHMuRE9NRXhjZXB0aW9uID0gRE9NRXhjZXB0aW9uO1xuXHRleHBvcnRzLkRPTUltcGxlbWVudGF0aW9uID0gRE9NSW1wbGVtZW50YXRpb247XG5cdGV4cG9ydHMuRWxlbWVudCA9IEVsZW1lbnQ7XG5cdGV4cG9ydHMuTm9kZSA9IE5vZGU7XG5cdGV4cG9ydHMuTm9kZUxpc3QgPSBOb2RlTGlzdDtcblx0ZXhwb3J0cy5YTUxTZXJpYWxpemVyID0gWE1MU2VyaWFsaXplcjtcbi8vfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@xmldom/xmldom/lib/dom.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@xmldom/xmldom/lib/entities.js":
/*!*****************************************************!*\
  !*** ./node_modules/@xmldom/xmldom/lib/entities.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nvar freeze = (__webpack_require__(/*! ./conventions */ \"(ssr)/./node_modules/@xmldom/xmldom/lib/conventions.js\").freeze);\n\n/**\n * The entities that are predefined in every XML document.\n *\n * @see https://www.w3.org/TR/2006/REC-xml11-20060816/#sec-predefined-ent W3C XML 1.1\n * @see https://www.w3.org/TR/2008/REC-xml-20081126/#sec-predefined-ent W3C XML 1.0\n * @see https://en.wikipedia.org/wiki/List_of_XML_and_HTML_character_entity_references#Predefined_entities_in_XML Wikipedia\n */\nexports.XML_ENTITIES = freeze({\n\tamp: '&',\n\tapos: \"'\",\n\tgt: '>',\n\tlt: '<',\n\tquot: '\"',\n});\n\n/**\n * A map of all entities that are detected in an HTML document.\n * They contain all entries from `XML_ENTITIES`.\n *\n * @see XML_ENTITIES\n * @see DOMParser.parseFromString\n * @see DOMImplementation.prototype.createHTMLDocument\n * @see https://html.spec.whatwg.org/#named-character-references WHATWG HTML(5) Spec\n * @see https://html.spec.whatwg.org/entities.json JSON\n * @see https://www.w3.org/TR/xml-entity-names/ W3C XML Entity Names\n * @see https://www.w3.org/TR/html4/sgml/entities.html W3C HTML4/SGML\n * @see https://en.wikipedia.org/wiki/List_of_XML_and_HTML_character_entity_references#Character_entity_references_in_HTML Wikipedia (HTML)\n * @see https://en.wikipedia.org/wiki/List_of_XML_and_HTML_character_entity_references#Entities_representing_special_characters_in_XHTML Wikpedia (XHTML)\n */\nexports.HTML_ENTITIES = freeze({\n\tAacute: '\\u00C1',\n\taacute: '\\u00E1',\n\tAbreve: '\\u0102',\n\tabreve: '\\u0103',\n\tac: '\\u223E',\n\tacd: '\\u223F',\n\tacE: '\\u223E\\u0333',\n\tAcirc: '\\u00C2',\n\tacirc: '\\u00E2',\n\tacute: '\\u00B4',\n\tAcy: '\\u0410',\n\tacy: '\\u0430',\n\tAElig: '\\u00C6',\n\taelig: '\\u00E6',\n\taf: '\\u2061',\n\tAfr: '\\uD835\\uDD04',\n\tafr: '\\uD835\\uDD1E',\n\tAgrave: '\\u00C0',\n\tagrave: '\\u00E0',\n\talefsym: '\\u2135',\n\taleph: '\\u2135',\n\tAlpha: '\\u0391',\n\talpha: '\\u03B1',\n\tAmacr: '\\u0100',\n\tamacr: '\\u0101',\n\tamalg: '\\u2A3F',\n\tAMP: '\\u0026',\n\tamp: '\\u0026',\n\tAnd: '\\u2A53',\n\tand: '\\u2227',\n\tandand: '\\u2A55',\n\tandd: '\\u2A5C',\n\tandslope: '\\u2A58',\n\tandv: '\\u2A5A',\n\tang: '\\u2220',\n\tange: '\\u29A4',\n\tangle: '\\u2220',\n\tangmsd: '\\u2221',\n\tangmsdaa: '\\u29A8',\n\tangmsdab: '\\u29A9',\n\tangmsdac: '\\u29AA',\n\tangmsdad: '\\u29AB',\n\tangmsdae: '\\u29AC',\n\tangmsdaf: '\\u29AD',\n\tangmsdag: '\\u29AE',\n\tangmsdah: '\\u29AF',\n\tangrt: '\\u221F',\n\tangrtvb: '\\u22BE',\n\tangrtvbd: '\\u299D',\n\tangsph: '\\u2222',\n\tangst: '\\u00C5',\n\tangzarr: '\\u237C',\n\tAogon: '\\u0104',\n\taogon: '\\u0105',\n\tAopf: '\\uD835\\uDD38',\n\taopf: '\\uD835\\uDD52',\n\tap: '\\u2248',\n\tapacir: '\\u2A6F',\n\tapE: '\\u2A70',\n\tape: '\\u224A',\n\tapid: '\\u224B',\n\tapos: '\\u0027',\n\tApplyFunction: '\\u2061',\n\tapprox: '\\u2248',\n\tapproxeq: '\\u224A',\n\tAring: '\\u00C5',\n\taring: '\\u00E5',\n\tAscr: '\\uD835\\uDC9C',\n\tascr: '\\uD835\\uDCB6',\n\tAssign: '\\u2254',\n\tast: '\\u002A',\n\tasymp: '\\u2248',\n\tasympeq: '\\u224D',\n\tAtilde: '\\u00C3',\n\tatilde: '\\u00E3',\n\tAuml: '\\u00C4',\n\tauml: '\\u00E4',\n\tawconint: '\\u2233',\n\tawint: '\\u2A11',\n\tbackcong: '\\u224C',\n\tbackepsilon: '\\u03F6',\n\tbackprime: '\\u2035',\n\tbacksim: '\\u223D',\n\tbacksimeq: '\\u22CD',\n\tBackslash: '\\u2216',\n\tBarv: '\\u2AE7',\n\tbarvee: '\\u22BD',\n\tBarwed: '\\u2306',\n\tbarwed: '\\u2305',\n\tbarwedge: '\\u2305',\n\tbbrk: '\\u23B5',\n\tbbrktbrk: '\\u23B6',\n\tbcong: '\\u224C',\n\tBcy: '\\u0411',\n\tbcy: '\\u0431',\n\tbdquo: '\\u201E',\n\tbecaus: '\\u2235',\n\tBecause: '\\u2235',\n\tbecause: '\\u2235',\n\tbemptyv: '\\u29B0',\n\tbepsi: '\\u03F6',\n\tbernou: '\\u212C',\n\tBernoullis: '\\u212C',\n\tBeta: '\\u0392',\n\tbeta: '\\u03B2',\n\tbeth: '\\u2136',\n\tbetween: '\\u226C',\n\tBfr: '\\uD835\\uDD05',\n\tbfr: '\\uD835\\uDD1F',\n\tbigcap: '\\u22C2',\n\tbigcirc: '\\u25EF',\n\tbigcup: '\\u22C3',\n\tbigodot: '\\u2A00',\n\tbigoplus: '\\u2A01',\n\tbigotimes: '\\u2A02',\n\tbigsqcup: '\\u2A06',\n\tbigstar: '\\u2605',\n\tbigtriangledown: '\\u25BD',\n\tbigtriangleup: '\\u25B3',\n\tbiguplus: '\\u2A04',\n\tbigvee: '\\u22C1',\n\tbigwedge: '\\u22C0',\n\tbkarow: '\\u290D',\n\tblacklozenge: '\\u29EB',\n\tblacksquare: '\\u25AA',\n\tblacktriangle: '\\u25B4',\n\tblacktriangledown: '\\u25BE',\n\tblacktriangleleft: '\\u25C2',\n\tblacktriangleright: '\\u25B8',\n\tblank: '\\u2423',\n\tblk12: '\\u2592',\n\tblk14: '\\u2591',\n\tblk34: '\\u2593',\n\tblock: '\\u2588',\n\tbne: '\\u003D\\u20E5',\n\tbnequiv: '\\u2261\\u20E5',\n\tbNot: '\\u2AED',\n\tbnot: '\\u2310',\n\tBopf: '\\uD835\\uDD39',\n\tbopf: '\\uD835\\uDD53',\n\tbot: '\\u22A5',\n\tbottom: '\\u22A5',\n\tbowtie: '\\u22C8',\n\tboxbox: '\\u29C9',\n\tboxDL: '\\u2557',\n\tboxDl: '\\u2556',\n\tboxdL: '\\u2555',\n\tboxdl: '\\u2510',\n\tboxDR: '\\u2554',\n\tboxDr: '\\u2553',\n\tboxdR: '\\u2552',\n\tboxdr: '\\u250C',\n\tboxH: '\\u2550',\n\tboxh: '\\u2500',\n\tboxHD: '\\u2566',\n\tboxHd: '\\u2564',\n\tboxhD: '\\u2565',\n\tboxhd: '\\u252C',\n\tboxHU: '\\u2569',\n\tboxHu: '\\u2567',\n\tboxhU: '\\u2568',\n\tboxhu: '\\u2534',\n\tboxminus: '\\u229F',\n\tboxplus: '\\u229E',\n\tboxtimes: '\\u22A0',\n\tboxUL: '\\u255D',\n\tboxUl: '\\u255C',\n\tboxuL: '\\u255B',\n\tboxul: '\\u2518',\n\tboxUR: '\\u255A',\n\tboxUr: '\\u2559',\n\tboxuR: '\\u2558',\n\tboxur: '\\u2514',\n\tboxV: '\\u2551',\n\tboxv: '\\u2502',\n\tboxVH: '\\u256C',\n\tboxVh: '\\u256B',\n\tboxvH: '\\u256A',\n\tboxvh: '\\u253C',\n\tboxVL: '\\u2563',\n\tboxVl: '\\u2562',\n\tboxvL: '\\u2561',\n\tboxvl: '\\u2524',\n\tboxVR: '\\u2560',\n\tboxVr: '\\u255F',\n\tboxvR: '\\u255E',\n\tboxvr: '\\u251C',\n\tbprime: '\\u2035',\n\tBreve: '\\u02D8',\n\tbreve: '\\u02D8',\n\tbrvbar: '\\u00A6',\n\tBscr: '\\u212C',\n\tbscr: '\\uD835\\uDCB7',\n\tbsemi: '\\u204F',\n\tbsim: '\\u223D',\n\tbsime: '\\u22CD',\n\tbsol: '\\u005C',\n\tbsolb: '\\u29C5',\n\tbsolhsub: '\\u27C8',\n\tbull: '\\u2022',\n\tbullet: '\\u2022',\n\tbump: '\\u224E',\n\tbumpE: '\\u2AAE',\n\tbumpe: '\\u224F',\n\tBumpeq: '\\u224E',\n\tbumpeq: '\\u224F',\n\tCacute: '\\u0106',\n\tcacute: '\\u0107',\n\tCap: '\\u22D2',\n\tcap: '\\u2229',\n\tcapand: '\\u2A44',\n\tcapbrcup: '\\u2A49',\n\tcapcap: '\\u2A4B',\n\tcapcup: '\\u2A47',\n\tcapdot: '\\u2A40',\n\tCapitalDifferentialD: '\\u2145',\n\tcaps: '\\u2229\\uFE00',\n\tcaret: '\\u2041',\n\tcaron: '\\u02C7',\n\tCayleys: '\\u212D',\n\tccaps: '\\u2A4D',\n\tCcaron: '\\u010C',\n\tccaron: '\\u010D',\n\tCcedil: '\\u00C7',\n\tccedil: '\\u00E7',\n\tCcirc: '\\u0108',\n\tccirc: '\\u0109',\n\tCconint: '\\u2230',\n\tccups: '\\u2A4C',\n\tccupssm: '\\u2A50',\n\tCdot: '\\u010A',\n\tcdot: '\\u010B',\n\tcedil: '\\u00B8',\n\tCedilla: '\\u00B8',\n\tcemptyv: '\\u29B2',\n\tcent: '\\u00A2',\n\tCenterDot: '\\u00B7',\n\tcenterdot: '\\u00B7',\n\tCfr: '\\u212D',\n\tcfr: '\\uD835\\uDD20',\n\tCHcy: '\\u0427',\n\tchcy: '\\u0447',\n\tcheck: '\\u2713',\n\tcheckmark: '\\u2713',\n\tChi: '\\u03A7',\n\tchi: '\\u03C7',\n\tcir: '\\u25CB',\n\tcirc: '\\u02C6',\n\tcirceq: '\\u2257',\n\tcirclearrowleft: '\\u21BA',\n\tcirclearrowright: '\\u21BB',\n\tcircledast: '\\u229B',\n\tcircledcirc: '\\u229A',\n\tcircleddash: '\\u229D',\n\tCircleDot: '\\u2299',\n\tcircledR: '\\u00AE',\n\tcircledS: '\\u24C8',\n\tCircleMinus: '\\u2296',\n\tCirclePlus: '\\u2295',\n\tCircleTimes: '\\u2297',\n\tcirE: '\\u29C3',\n\tcire: '\\u2257',\n\tcirfnint: '\\u2A10',\n\tcirmid: '\\u2AEF',\n\tcirscir: '\\u29C2',\n\tClockwiseContourIntegral: '\\u2232',\n\tCloseCurlyDoubleQuote: '\\u201D',\n\tCloseCurlyQuote: '\\u2019',\n\tclubs: '\\u2663',\n\tclubsuit: '\\u2663',\n\tColon: '\\u2237',\n\tcolon: '\\u003A',\n\tColone: '\\u2A74',\n\tcolone: '\\u2254',\n\tcoloneq: '\\u2254',\n\tcomma: '\\u002C',\n\tcommat: '\\u0040',\n\tcomp: '\\u2201',\n\tcompfn: '\\u2218',\n\tcomplement: '\\u2201',\n\tcomplexes: '\\u2102',\n\tcong: '\\u2245',\n\tcongdot: '\\u2A6D',\n\tCongruent: '\\u2261',\n\tConint: '\\u222F',\n\tconint: '\\u222E',\n\tContourIntegral: '\\u222E',\n\tCopf: '\\u2102',\n\tcopf: '\\uD835\\uDD54',\n\tcoprod: '\\u2210',\n\tCoproduct: '\\u2210',\n\tCOPY: '\\u00A9',\n\tcopy: '\\u00A9',\n\tcopysr: '\\u2117',\n\tCounterClockwiseContourIntegral: '\\u2233',\n\tcrarr: '\\u21B5',\n\tCross: '\\u2A2F',\n\tcross: '\\u2717',\n\tCscr: '\\uD835\\uDC9E',\n\tcscr: '\\uD835\\uDCB8',\n\tcsub: '\\u2ACF',\n\tcsube: '\\u2AD1',\n\tcsup: '\\u2AD0',\n\tcsupe: '\\u2AD2',\n\tctdot: '\\u22EF',\n\tcudarrl: '\\u2938',\n\tcudarrr: '\\u2935',\n\tcuepr: '\\u22DE',\n\tcuesc: '\\u22DF',\n\tcularr: '\\u21B6',\n\tcularrp: '\\u293D',\n\tCup: '\\u22D3',\n\tcup: '\\u222A',\n\tcupbrcap: '\\u2A48',\n\tCupCap: '\\u224D',\n\tcupcap: '\\u2A46',\n\tcupcup: '\\u2A4A',\n\tcupdot: '\\u228D',\n\tcupor: '\\u2A45',\n\tcups: '\\u222A\\uFE00',\n\tcurarr: '\\u21B7',\n\tcurarrm: '\\u293C',\n\tcurlyeqprec: '\\u22DE',\n\tcurlyeqsucc: '\\u22DF',\n\tcurlyvee: '\\u22CE',\n\tcurlywedge: '\\u22CF',\n\tcurren: '\\u00A4',\n\tcurvearrowleft: '\\u21B6',\n\tcurvearrowright: '\\u21B7',\n\tcuvee: '\\u22CE',\n\tcuwed: '\\u22CF',\n\tcwconint: '\\u2232',\n\tcwint: '\\u2231',\n\tcylcty: '\\u232D',\n\tDagger: '\\u2021',\n\tdagger: '\\u2020',\n\tdaleth: '\\u2138',\n\tDarr: '\\u21A1',\n\tdArr: '\\u21D3',\n\tdarr: '\\u2193',\n\tdash: '\\u2010',\n\tDashv: '\\u2AE4',\n\tdashv: '\\u22A3',\n\tdbkarow: '\\u290F',\n\tdblac: '\\u02DD',\n\tDcaron: '\\u010E',\n\tdcaron: '\\u010F',\n\tDcy: '\\u0414',\n\tdcy: '\\u0434',\n\tDD: '\\u2145',\n\tdd: '\\u2146',\n\tddagger: '\\u2021',\n\tddarr: '\\u21CA',\n\tDDotrahd: '\\u2911',\n\tddotseq: '\\u2A77',\n\tdeg: '\\u00B0',\n\tDel: '\\u2207',\n\tDelta: '\\u0394',\n\tdelta: '\\u03B4',\n\tdemptyv: '\\u29B1',\n\tdfisht: '\\u297F',\n\tDfr: '\\uD835\\uDD07',\n\tdfr: '\\uD835\\uDD21',\n\tdHar: '\\u2965',\n\tdharl: '\\u21C3',\n\tdharr: '\\u21C2',\n\tDiacriticalAcute: '\\u00B4',\n\tDiacriticalDot: '\\u02D9',\n\tDiacriticalDoubleAcute: '\\u02DD',\n\tDiacriticalGrave: '\\u0060',\n\tDiacriticalTilde: '\\u02DC',\n\tdiam: '\\u22C4',\n\tDiamond: '\\u22C4',\n\tdiamond: '\\u22C4',\n\tdiamondsuit: '\\u2666',\n\tdiams: '\\u2666',\n\tdie: '\\u00A8',\n\tDifferentialD: '\\u2146',\n\tdigamma: '\\u03DD',\n\tdisin: '\\u22F2',\n\tdiv: '\\u00F7',\n\tdivide: '\\u00F7',\n\tdivideontimes: '\\u22C7',\n\tdivonx: '\\u22C7',\n\tDJcy: '\\u0402',\n\tdjcy: '\\u0452',\n\tdlcorn: '\\u231E',\n\tdlcrop: '\\u230D',\n\tdollar: '\\u0024',\n\tDopf: '\\uD835\\uDD3B',\n\tdopf: '\\uD835\\uDD55',\n\tDot: '\\u00A8',\n\tdot: '\\u02D9',\n\tDotDot: '\\u20DC',\n\tdoteq: '\\u2250',\n\tdoteqdot: '\\u2251',\n\tDotEqual: '\\u2250',\n\tdotminus: '\\u2238',\n\tdotplus: '\\u2214',\n\tdotsquare: '\\u22A1',\n\tdoublebarwedge: '\\u2306',\n\tDoubleContourIntegral: '\\u222F',\n\tDoubleDot: '\\u00A8',\n\tDoubleDownArrow: '\\u21D3',\n\tDoubleLeftArrow: '\\u21D0',\n\tDoubleLeftRightArrow: '\\u21D4',\n\tDoubleLeftTee: '\\u2AE4',\n\tDoubleLongLeftArrow: '\\u27F8',\n\tDoubleLongLeftRightArrow: '\\u27FA',\n\tDoubleLongRightArrow: '\\u27F9',\n\tDoubleRightArrow: '\\u21D2',\n\tDoubleRightTee: '\\u22A8',\n\tDoubleUpArrow: '\\u21D1',\n\tDoubleUpDownArrow: '\\u21D5',\n\tDoubleVerticalBar: '\\u2225',\n\tDownArrow: '\\u2193',\n\tDownarrow: '\\u21D3',\n\tdownarrow: '\\u2193',\n\tDownArrowBar: '\\u2913',\n\tDownArrowUpArrow: '\\u21F5',\n\tDownBreve: '\\u0311',\n\tdowndownarrows: '\\u21CA',\n\tdownharpoonleft: '\\u21C3',\n\tdownharpoonright: '\\u21C2',\n\tDownLeftRightVector: '\\u2950',\n\tDownLeftTeeVector: '\\u295E',\n\tDownLeftVector: '\\u21BD',\n\tDownLeftVectorBar: '\\u2956',\n\tDownRightTeeVector: '\\u295F',\n\tDownRightVector: '\\u21C1',\n\tDownRightVectorBar: '\\u2957',\n\tDownTee: '\\u22A4',\n\tDownTeeArrow: '\\u21A7',\n\tdrbkarow: '\\u2910',\n\tdrcorn: '\\u231F',\n\tdrcrop: '\\u230C',\n\tDscr: '\\uD835\\uDC9F',\n\tdscr: '\\uD835\\uDCB9',\n\tDScy: '\\u0405',\n\tdscy: '\\u0455',\n\tdsol: '\\u29F6',\n\tDstrok: '\\u0110',\n\tdstrok: '\\u0111',\n\tdtdot: '\\u22F1',\n\tdtri: '\\u25BF',\n\tdtrif: '\\u25BE',\n\tduarr: '\\u21F5',\n\tduhar: '\\u296F',\n\tdwangle: '\\u29A6',\n\tDZcy: '\\u040F',\n\tdzcy: '\\u045F',\n\tdzigrarr: '\\u27FF',\n\tEacute: '\\u00C9',\n\teacute: '\\u00E9',\n\teaster: '\\u2A6E',\n\tEcaron: '\\u011A',\n\tecaron: '\\u011B',\n\tecir: '\\u2256',\n\tEcirc: '\\u00CA',\n\tecirc: '\\u00EA',\n\tecolon: '\\u2255',\n\tEcy: '\\u042D',\n\tecy: '\\u044D',\n\teDDot: '\\u2A77',\n\tEdot: '\\u0116',\n\teDot: '\\u2251',\n\tedot: '\\u0117',\n\tee: '\\u2147',\n\tefDot: '\\u2252',\n\tEfr: '\\uD835\\uDD08',\n\tefr: '\\uD835\\uDD22',\n\teg: '\\u2A9A',\n\tEgrave: '\\u00C8',\n\tegrave: '\\u00E8',\n\tegs: '\\u2A96',\n\tegsdot: '\\u2A98',\n\tel: '\\u2A99',\n\tElement: '\\u2208',\n\telinters: '\\u23E7',\n\tell: '\\u2113',\n\tels: '\\u2A95',\n\telsdot: '\\u2A97',\n\tEmacr: '\\u0112',\n\temacr: '\\u0113',\n\tempty: '\\u2205',\n\temptyset: '\\u2205',\n\tEmptySmallSquare: '\\u25FB',\n\temptyv: '\\u2205',\n\tEmptyVerySmallSquare: '\\u25AB',\n\temsp: '\\u2003',\n\temsp13: '\\u2004',\n\temsp14: '\\u2005',\n\tENG: '\\u014A',\n\teng: '\\u014B',\n\tensp: '\\u2002',\n\tEogon: '\\u0118',\n\teogon: '\\u0119',\n\tEopf: '\\uD835\\uDD3C',\n\teopf: '\\uD835\\uDD56',\n\tepar: '\\u22D5',\n\teparsl: '\\u29E3',\n\teplus: '\\u2A71',\n\tepsi: '\\u03B5',\n\tEpsilon: '\\u0395',\n\tepsilon: '\\u03B5',\n\tepsiv: '\\u03F5',\n\teqcirc: '\\u2256',\n\teqcolon: '\\u2255',\n\teqsim: '\\u2242',\n\teqslantgtr: '\\u2A96',\n\teqslantless: '\\u2A95',\n\tEqual: '\\u2A75',\n\tequals: '\\u003D',\n\tEqualTilde: '\\u2242',\n\tequest: '\\u225F',\n\tEquilibrium: '\\u21CC',\n\tequiv: '\\u2261',\n\tequivDD: '\\u2A78',\n\teqvparsl: '\\u29E5',\n\terarr: '\\u2971',\n\terDot: '\\u2253',\n\tEscr: '\\u2130',\n\tescr: '\\u212F',\n\tesdot: '\\u2250',\n\tEsim: '\\u2A73',\n\tesim: '\\u2242',\n\tEta: '\\u0397',\n\teta: '\\u03B7',\n\tETH: '\\u00D0',\n\teth: '\\u00F0',\n\tEuml: '\\u00CB',\n\teuml: '\\u00EB',\n\teuro: '\\u20AC',\n\texcl: '\\u0021',\n\texist: '\\u2203',\n\tExists: '\\u2203',\n\texpectation: '\\u2130',\n\tExponentialE: '\\u2147',\n\texponentiale: '\\u2147',\n\tfallingdotseq: '\\u2252',\n\tFcy: '\\u0424',\n\tfcy: '\\u0444',\n\tfemale: '\\u2640',\n\tffilig: '\\uFB03',\n\tfflig: '\\uFB00',\n\tffllig: '\\uFB04',\n\tFfr: '\\uD835\\uDD09',\n\tffr: '\\uD835\\uDD23',\n\tfilig: '\\uFB01',\n\tFilledSmallSquare: '\\u25FC',\n\tFilledVerySmallSquare: '\\u25AA',\n\tfjlig: '\\u0066\\u006A',\n\tflat: '\\u266D',\n\tfllig: '\\uFB02',\n\tfltns: '\\u25B1',\n\tfnof: '\\u0192',\n\tFopf: '\\uD835\\uDD3D',\n\tfopf: '\\uD835\\uDD57',\n\tForAll: '\\u2200',\n\tforall: '\\u2200',\n\tfork: '\\u22D4',\n\tforkv: '\\u2AD9',\n\tFouriertrf: '\\u2131',\n\tfpartint: '\\u2A0D',\n\tfrac12: '\\u00BD',\n\tfrac13: '\\u2153',\n\tfrac14: '\\u00BC',\n\tfrac15: '\\u2155',\n\tfrac16: '\\u2159',\n\tfrac18: '\\u215B',\n\tfrac23: '\\u2154',\n\tfrac25: '\\u2156',\n\tfrac34: '\\u00BE',\n\tfrac35: '\\u2157',\n\tfrac38: '\\u215C',\n\tfrac45: '\\u2158',\n\tfrac56: '\\u215A',\n\tfrac58: '\\u215D',\n\tfrac78: '\\u215E',\n\tfrasl: '\\u2044',\n\tfrown: '\\u2322',\n\tFscr: '\\u2131',\n\tfscr: '\\uD835\\uDCBB',\n\tgacute: '\\u01F5',\n\tGamma: '\\u0393',\n\tgamma: '\\u03B3',\n\tGammad: '\\u03DC',\n\tgammad: '\\u03DD',\n\tgap: '\\u2A86',\n\tGbreve: '\\u011E',\n\tgbreve: '\\u011F',\n\tGcedil: '\\u0122',\n\tGcirc: '\\u011C',\n\tgcirc: '\\u011D',\n\tGcy: '\\u0413',\n\tgcy: '\\u0433',\n\tGdot: '\\u0120',\n\tgdot: '\\u0121',\n\tgE: '\\u2267',\n\tge: '\\u2265',\n\tgEl: '\\u2A8C',\n\tgel: '\\u22DB',\n\tgeq: '\\u2265',\n\tgeqq: '\\u2267',\n\tgeqslant: '\\u2A7E',\n\tges: '\\u2A7E',\n\tgescc: '\\u2AA9',\n\tgesdot: '\\u2A80',\n\tgesdoto: '\\u2A82',\n\tgesdotol: '\\u2A84',\n\tgesl: '\\u22DB\\uFE00',\n\tgesles: '\\u2A94',\n\tGfr: '\\uD835\\uDD0A',\n\tgfr: '\\uD835\\uDD24',\n\tGg: '\\u22D9',\n\tgg: '\\u226B',\n\tggg: '\\u22D9',\n\tgimel: '\\u2137',\n\tGJcy: '\\u0403',\n\tgjcy: '\\u0453',\n\tgl: '\\u2277',\n\tgla: '\\u2AA5',\n\tglE: '\\u2A92',\n\tglj: '\\u2AA4',\n\tgnap: '\\u2A8A',\n\tgnapprox: '\\u2A8A',\n\tgnE: '\\u2269',\n\tgne: '\\u2A88',\n\tgneq: '\\u2A88',\n\tgneqq: '\\u2269',\n\tgnsim: '\\u22E7',\n\tGopf: '\\uD835\\uDD3E',\n\tgopf: '\\uD835\\uDD58',\n\tgrave: '\\u0060',\n\tGreaterEqual: '\\u2265',\n\tGreaterEqualLess: '\\u22DB',\n\tGreaterFullEqual: '\\u2267',\n\tGreaterGreater: '\\u2AA2',\n\tGreaterLess: '\\u2277',\n\tGreaterSlantEqual: '\\u2A7E',\n\tGreaterTilde: '\\u2273',\n\tGscr: '\\uD835\\uDCA2',\n\tgscr: '\\u210A',\n\tgsim: '\\u2273',\n\tgsime: '\\u2A8E',\n\tgsiml: '\\u2A90',\n\tGt: '\\u226B',\n\tGT: '\\u003E',\n\tgt: '\\u003E',\n\tgtcc: '\\u2AA7',\n\tgtcir: '\\u2A7A',\n\tgtdot: '\\u22D7',\n\tgtlPar: '\\u2995',\n\tgtquest: '\\u2A7C',\n\tgtrapprox: '\\u2A86',\n\tgtrarr: '\\u2978',\n\tgtrdot: '\\u22D7',\n\tgtreqless: '\\u22DB',\n\tgtreqqless: '\\u2A8C',\n\tgtrless: '\\u2277',\n\tgtrsim: '\\u2273',\n\tgvertneqq: '\\u2269\\uFE00',\n\tgvnE: '\\u2269\\uFE00',\n\tHacek: '\\u02C7',\n\thairsp: '\\u200A',\n\thalf: '\\u00BD',\n\thamilt: '\\u210B',\n\tHARDcy: '\\u042A',\n\thardcy: '\\u044A',\n\thArr: '\\u21D4',\n\tharr: '\\u2194',\n\tharrcir: '\\u2948',\n\tharrw: '\\u21AD',\n\tHat: '\\u005E',\n\thbar: '\\u210F',\n\tHcirc: '\\u0124',\n\thcirc: '\\u0125',\n\thearts: '\\u2665',\n\theartsuit: '\\u2665',\n\thellip: '\\u2026',\n\thercon: '\\u22B9',\n\tHfr: '\\u210C',\n\thfr: '\\uD835\\uDD25',\n\tHilbertSpace: '\\u210B',\n\thksearow: '\\u2925',\n\thkswarow: '\\u2926',\n\thoarr: '\\u21FF',\n\thomtht: '\\u223B',\n\thookleftarrow: '\\u21A9',\n\thookrightarrow: '\\u21AA',\n\tHopf: '\\u210D',\n\thopf: '\\uD835\\uDD59',\n\thorbar: '\\u2015',\n\tHorizontalLine: '\\u2500',\n\tHscr: '\\u210B',\n\thscr: '\\uD835\\uDCBD',\n\thslash: '\\u210F',\n\tHstrok: '\\u0126',\n\thstrok: '\\u0127',\n\tHumpDownHump: '\\u224E',\n\tHumpEqual: '\\u224F',\n\thybull: '\\u2043',\n\thyphen: '\\u2010',\n\tIacute: '\\u00CD',\n\tiacute: '\\u00ED',\n\tic: '\\u2063',\n\tIcirc: '\\u00CE',\n\ticirc: '\\u00EE',\n\tIcy: '\\u0418',\n\ticy: '\\u0438',\n\tIdot: '\\u0130',\n\tIEcy: '\\u0415',\n\tiecy: '\\u0435',\n\tiexcl: '\\u00A1',\n\tiff: '\\u21D4',\n\tIfr: '\\u2111',\n\tifr: '\\uD835\\uDD26',\n\tIgrave: '\\u00CC',\n\tigrave: '\\u00EC',\n\tii: '\\u2148',\n\tiiiint: '\\u2A0C',\n\tiiint: '\\u222D',\n\tiinfin: '\\u29DC',\n\tiiota: '\\u2129',\n\tIJlig: '\\u0132',\n\tijlig: '\\u0133',\n\tIm: '\\u2111',\n\tImacr: '\\u012A',\n\timacr: '\\u012B',\n\timage: '\\u2111',\n\tImaginaryI: '\\u2148',\n\timagline: '\\u2110',\n\timagpart: '\\u2111',\n\timath: '\\u0131',\n\timof: '\\u22B7',\n\timped: '\\u01B5',\n\tImplies: '\\u21D2',\n\tin: '\\u2208',\n\tincare: '\\u2105',\n\tinfin: '\\u221E',\n\tinfintie: '\\u29DD',\n\tinodot: '\\u0131',\n\tInt: '\\u222C',\n\tint: '\\u222B',\n\tintcal: '\\u22BA',\n\tintegers: '\\u2124',\n\tIntegral: '\\u222B',\n\tintercal: '\\u22BA',\n\tIntersection: '\\u22C2',\n\tintlarhk: '\\u2A17',\n\tintprod: '\\u2A3C',\n\tInvisibleComma: '\\u2063',\n\tInvisibleTimes: '\\u2062',\n\tIOcy: '\\u0401',\n\tiocy: '\\u0451',\n\tIogon: '\\u012E',\n\tiogon: '\\u012F',\n\tIopf: '\\uD835\\uDD40',\n\tiopf: '\\uD835\\uDD5A',\n\tIota: '\\u0399',\n\tiota: '\\u03B9',\n\tiprod: '\\u2A3C',\n\tiquest: '\\u00BF',\n\tIscr: '\\u2110',\n\tiscr: '\\uD835\\uDCBE',\n\tisin: '\\u2208',\n\tisindot: '\\u22F5',\n\tisinE: '\\u22F9',\n\tisins: '\\u22F4',\n\tisinsv: '\\u22F3',\n\tisinv: '\\u2208',\n\tit: '\\u2062',\n\tItilde: '\\u0128',\n\titilde: '\\u0129',\n\tIukcy: '\\u0406',\n\tiukcy: '\\u0456',\n\tIuml: '\\u00CF',\n\tiuml: '\\u00EF',\n\tJcirc: '\\u0134',\n\tjcirc: '\\u0135',\n\tJcy: '\\u0419',\n\tjcy: '\\u0439',\n\tJfr: '\\uD835\\uDD0D',\n\tjfr: '\\uD835\\uDD27',\n\tjmath: '\\u0237',\n\tJopf: '\\uD835\\uDD41',\n\tjopf: '\\uD835\\uDD5B',\n\tJscr: '\\uD835\\uDCA5',\n\tjscr: '\\uD835\\uDCBF',\n\tJsercy: '\\u0408',\n\tjsercy: '\\u0458',\n\tJukcy: '\\u0404',\n\tjukcy: '\\u0454',\n\tKappa: '\\u039A',\n\tkappa: '\\u03BA',\n\tkappav: '\\u03F0',\n\tKcedil: '\\u0136',\n\tkcedil: '\\u0137',\n\tKcy: '\\u041A',\n\tkcy: '\\u043A',\n\tKfr: '\\uD835\\uDD0E',\n\tkfr: '\\uD835\\uDD28',\n\tkgreen: '\\u0138',\n\tKHcy: '\\u0425',\n\tkhcy: '\\u0445',\n\tKJcy: '\\u040C',\n\tkjcy: '\\u045C',\n\tKopf: '\\uD835\\uDD42',\n\tkopf: '\\uD835\\uDD5C',\n\tKscr: '\\uD835\\uDCA6',\n\tkscr: '\\uD835\\uDCC0',\n\tlAarr: '\\u21DA',\n\tLacute: '\\u0139',\n\tlacute: '\\u013A',\n\tlaemptyv: '\\u29B4',\n\tlagran: '\\u2112',\n\tLambda: '\\u039B',\n\tlambda: '\\u03BB',\n\tLang: '\\u27EA',\n\tlang: '\\u27E8',\n\tlangd: '\\u2991',\n\tlangle: '\\u27E8',\n\tlap: '\\u2A85',\n\tLaplacetrf: '\\u2112',\n\tlaquo: '\\u00AB',\n\tLarr: '\\u219E',\n\tlArr: '\\u21D0',\n\tlarr: '\\u2190',\n\tlarrb: '\\u21E4',\n\tlarrbfs: '\\u291F',\n\tlarrfs: '\\u291D',\n\tlarrhk: '\\u21A9',\n\tlarrlp: '\\u21AB',\n\tlarrpl: '\\u2939',\n\tlarrsim: '\\u2973',\n\tlarrtl: '\\u21A2',\n\tlat: '\\u2AAB',\n\tlAtail: '\\u291B',\n\tlatail: '\\u2919',\n\tlate: '\\u2AAD',\n\tlates: '\\u2AAD\\uFE00',\n\tlBarr: '\\u290E',\n\tlbarr: '\\u290C',\n\tlbbrk: '\\u2772',\n\tlbrace: '\\u007B',\n\tlbrack: '\\u005B',\n\tlbrke: '\\u298B',\n\tlbrksld: '\\u298F',\n\tlbrkslu: '\\u298D',\n\tLcaron: '\\u013D',\n\tlcaron: '\\u013E',\n\tLcedil: '\\u013B',\n\tlcedil: '\\u013C',\n\tlceil: '\\u2308',\n\tlcub: '\\u007B',\n\tLcy: '\\u041B',\n\tlcy: '\\u043B',\n\tldca: '\\u2936',\n\tldquo: '\\u201C',\n\tldquor: '\\u201E',\n\tldrdhar: '\\u2967',\n\tldrushar: '\\u294B',\n\tldsh: '\\u21B2',\n\tlE: '\\u2266',\n\tle: '\\u2264',\n\tLeftAngleBracket: '\\u27E8',\n\tLeftArrow: '\\u2190',\n\tLeftarrow: '\\u21D0',\n\tleftarrow: '\\u2190',\n\tLeftArrowBar: '\\u21E4',\n\tLeftArrowRightArrow: '\\u21C6',\n\tleftarrowtail: '\\u21A2',\n\tLeftCeiling: '\\u2308',\n\tLeftDoubleBracket: '\\u27E6',\n\tLeftDownTeeVector: '\\u2961',\n\tLeftDownVector: '\\u21C3',\n\tLeftDownVectorBar: '\\u2959',\n\tLeftFloor: '\\u230A',\n\tleftharpoondown: '\\u21BD',\n\tleftharpoonup: '\\u21BC',\n\tleftleftarrows: '\\u21C7',\n\tLeftRightArrow: '\\u2194',\n\tLeftrightarrow: '\\u21D4',\n\tleftrightarrow: '\\u2194',\n\tleftrightarrows: '\\u21C6',\n\tleftrightharpoons: '\\u21CB',\n\tleftrightsquigarrow: '\\u21AD',\n\tLeftRightVector: '\\u294E',\n\tLeftTee: '\\u22A3',\n\tLeftTeeArrow: '\\u21A4',\n\tLeftTeeVector: '\\u295A',\n\tleftthreetimes: '\\u22CB',\n\tLeftTriangle: '\\u22B2',\n\tLeftTriangleBar: '\\u29CF',\n\tLeftTriangleEqual: '\\u22B4',\n\tLeftUpDownVector: '\\u2951',\n\tLeftUpTeeVector: '\\u2960',\n\tLeftUpVector: '\\u21BF',\n\tLeftUpVectorBar: '\\u2958',\n\tLeftVector: '\\u21BC',\n\tLeftVectorBar: '\\u2952',\n\tlEg: '\\u2A8B',\n\tleg: '\\u22DA',\n\tleq: '\\u2264',\n\tleqq: '\\u2266',\n\tleqslant: '\\u2A7D',\n\tles: '\\u2A7D',\n\tlescc: '\\u2AA8',\n\tlesdot: '\\u2A7F',\n\tlesdoto: '\\u2A81',\n\tlesdotor: '\\u2A83',\n\tlesg: '\\u22DA\\uFE00',\n\tlesges: '\\u2A93',\n\tlessapprox: '\\u2A85',\n\tlessdot: '\\u22D6',\n\tlesseqgtr: '\\u22DA',\n\tlesseqqgtr: '\\u2A8B',\n\tLessEqualGreater: '\\u22DA',\n\tLessFullEqual: '\\u2266',\n\tLessGreater: '\\u2276',\n\tlessgtr: '\\u2276',\n\tLessLess: '\\u2AA1',\n\tlesssim: '\\u2272',\n\tLessSlantEqual: '\\u2A7D',\n\tLessTilde: '\\u2272',\n\tlfisht: '\\u297C',\n\tlfloor: '\\u230A',\n\tLfr: '\\uD835\\uDD0F',\n\tlfr: '\\uD835\\uDD29',\n\tlg: '\\u2276',\n\tlgE: '\\u2A91',\n\tlHar: '\\u2962',\n\tlhard: '\\u21BD',\n\tlharu: '\\u21BC',\n\tlharul: '\\u296A',\n\tlhblk: '\\u2584',\n\tLJcy: '\\u0409',\n\tljcy: '\\u0459',\n\tLl: '\\u22D8',\n\tll: '\\u226A',\n\tllarr: '\\u21C7',\n\tllcorner: '\\u231E',\n\tLleftarrow: '\\u21DA',\n\tllhard: '\\u296B',\n\tlltri: '\\u25FA',\n\tLmidot: '\\u013F',\n\tlmidot: '\\u0140',\n\tlmoust: '\\u23B0',\n\tlmoustache: '\\u23B0',\n\tlnap: '\\u2A89',\n\tlnapprox: '\\u2A89',\n\tlnE: '\\u2268',\n\tlne: '\\u2A87',\n\tlneq: '\\u2A87',\n\tlneqq: '\\u2268',\n\tlnsim: '\\u22E6',\n\tloang: '\\u27EC',\n\tloarr: '\\u21FD',\n\tlobrk: '\\u27E6',\n\tLongLeftArrow: '\\u27F5',\n\tLongleftarrow: '\\u27F8',\n\tlongleftarrow: '\\u27F5',\n\tLongLeftRightArrow: '\\u27F7',\n\tLongleftrightarrow: '\\u27FA',\n\tlongleftrightarrow: '\\u27F7',\n\tlongmapsto: '\\u27FC',\n\tLongRightArrow: '\\u27F6',\n\tLongrightarrow: '\\u27F9',\n\tlongrightarrow: '\\u27F6',\n\tlooparrowleft: '\\u21AB',\n\tlooparrowright: '\\u21AC',\n\tlopar: '\\u2985',\n\tLopf: '\\uD835\\uDD43',\n\tlopf: '\\uD835\\uDD5D',\n\tloplus: '\\u2A2D',\n\tlotimes: '\\u2A34',\n\tlowast: '\\u2217',\n\tlowbar: '\\u005F',\n\tLowerLeftArrow: '\\u2199',\n\tLowerRightArrow: '\\u2198',\n\tloz: '\\u25CA',\n\tlozenge: '\\u25CA',\n\tlozf: '\\u29EB',\n\tlpar: '\\u0028',\n\tlparlt: '\\u2993',\n\tlrarr: '\\u21C6',\n\tlrcorner: '\\u231F',\n\tlrhar: '\\u21CB',\n\tlrhard: '\\u296D',\n\tlrm: '\\u200E',\n\tlrtri: '\\u22BF',\n\tlsaquo: '\\u2039',\n\tLscr: '\\u2112',\n\tlscr: '\\uD835\\uDCC1',\n\tLsh: '\\u21B0',\n\tlsh: '\\u21B0',\n\tlsim: '\\u2272',\n\tlsime: '\\u2A8D',\n\tlsimg: '\\u2A8F',\n\tlsqb: '\\u005B',\n\tlsquo: '\\u2018',\n\tlsquor: '\\u201A',\n\tLstrok: '\\u0141',\n\tlstrok: '\\u0142',\n\tLt: '\\u226A',\n\tLT: '\\u003C',\n\tlt: '\\u003C',\n\tltcc: '\\u2AA6',\n\tltcir: '\\u2A79',\n\tltdot: '\\u22D6',\n\tlthree: '\\u22CB',\n\tltimes: '\\u22C9',\n\tltlarr: '\\u2976',\n\tltquest: '\\u2A7B',\n\tltri: '\\u25C3',\n\tltrie: '\\u22B4',\n\tltrif: '\\u25C2',\n\tltrPar: '\\u2996',\n\tlurdshar: '\\u294A',\n\tluruhar: '\\u2966',\n\tlvertneqq: '\\u2268\\uFE00',\n\tlvnE: '\\u2268\\uFE00',\n\tmacr: '\\u00AF',\n\tmale: '\\u2642',\n\tmalt: '\\u2720',\n\tmaltese: '\\u2720',\n\tMap: '\\u2905',\n\tmap: '\\u21A6',\n\tmapsto: '\\u21A6',\n\tmapstodown: '\\u21A7',\n\tmapstoleft: '\\u21A4',\n\tmapstoup: '\\u21A5',\n\tmarker: '\\u25AE',\n\tmcomma: '\\u2A29',\n\tMcy: '\\u041C',\n\tmcy: '\\u043C',\n\tmdash: '\\u2014',\n\tmDDot: '\\u223A',\n\tmeasuredangle: '\\u2221',\n\tMediumSpace: '\\u205F',\n\tMellintrf: '\\u2133',\n\tMfr: '\\uD835\\uDD10',\n\tmfr: '\\uD835\\uDD2A',\n\tmho: '\\u2127',\n\tmicro: '\\u00B5',\n\tmid: '\\u2223',\n\tmidast: '\\u002A',\n\tmidcir: '\\u2AF0',\n\tmiddot: '\\u00B7',\n\tminus: '\\u2212',\n\tminusb: '\\u229F',\n\tminusd: '\\u2238',\n\tminusdu: '\\u2A2A',\n\tMinusPlus: '\\u2213',\n\tmlcp: '\\u2ADB',\n\tmldr: '\\u2026',\n\tmnplus: '\\u2213',\n\tmodels: '\\u22A7',\n\tMopf: '\\uD835\\uDD44',\n\tmopf: '\\uD835\\uDD5E',\n\tmp: '\\u2213',\n\tMscr: '\\u2133',\n\tmscr: '\\uD835\\uDCC2',\n\tmstpos: '\\u223E',\n\tMu: '\\u039C',\n\tmu: '\\u03BC',\n\tmultimap: '\\u22B8',\n\tmumap: '\\u22B8',\n\tnabla: '\\u2207',\n\tNacute: '\\u0143',\n\tnacute: '\\u0144',\n\tnang: '\\u2220\\u20D2',\n\tnap: '\\u2249',\n\tnapE: '\\u2A70\\u0338',\n\tnapid: '\\u224B\\u0338',\n\tnapos: '\\u0149',\n\tnapprox: '\\u2249',\n\tnatur: '\\u266E',\n\tnatural: '\\u266E',\n\tnaturals: '\\u2115',\n\tnbsp: '\\u00A0',\n\tnbump: '\\u224E\\u0338',\n\tnbumpe: '\\u224F\\u0338',\n\tncap: '\\u2A43',\n\tNcaron: '\\u0147',\n\tncaron: '\\u0148',\n\tNcedil: '\\u0145',\n\tncedil: '\\u0146',\n\tncong: '\\u2247',\n\tncongdot: '\\u2A6D\\u0338',\n\tncup: '\\u2A42',\n\tNcy: '\\u041D',\n\tncy: '\\u043D',\n\tndash: '\\u2013',\n\tne: '\\u2260',\n\tnearhk: '\\u2924',\n\tneArr: '\\u21D7',\n\tnearr: '\\u2197',\n\tnearrow: '\\u2197',\n\tnedot: '\\u2250\\u0338',\n\tNegativeMediumSpace: '\\u200B',\n\tNegativeThickSpace: '\\u200B',\n\tNegativeThinSpace: '\\u200B',\n\tNegativeVeryThinSpace: '\\u200B',\n\tnequiv: '\\u2262',\n\tnesear: '\\u2928',\n\tnesim: '\\u2242\\u0338',\n\tNestedGreaterGreater: '\\u226B',\n\tNestedLessLess: '\\u226A',\n\tNewLine: '\\u000A',\n\tnexist: '\\u2204',\n\tnexists: '\\u2204',\n\tNfr: '\\uD835\\uDD11',\n\tnfr: '\\uD835\\uDD2B',\n\tngE: '\\u2267\\u0338',\n\tnge: '\\u2271',\n\tngeq: '\\u2271',\n\tngeqq: '\\u2267\\u0338',\n\tngeqslant: '\\u2A7E\\u0338',\n\tnges: '\\u2A7E\\u0338',\n\tnGg: '\\u22D9\\u0338',\n\tngsim: '\\u2275',\n\tnGt: '\\u226B\\u20D2',\n\tngt: '\\u226F',\n\tngtr: '\\u226F',\n\tnGtv: '\\u226B\\u0338',\n\tnhArr: '\\u21CE',\n\tnharr: '\\u21AE',\n\tnhpar: '\\u2AF2',\n\tni: '\\u220B',\n\tnis: '\\u22FC',\n\tnisd: '\\u22FA',\n\tniv: '\\u220B',\n\tNJcy: '\\u040A',\n\tnjcy: '\\u045A',\n\tnlArr: '\\u21CD',\n\tnlarr: '\\u219A',\n\tnldr: '\\u2025',\n\tnlE: '\\u2266\\u0338',\n\tnle: '\\u2270',\n\tnLeftarrow: '\\u21CD',\n\tnleftarrow: '\\u219A',\n\tnLeftrightarrow: '\\u21CE',\n\tnleftrightarrow: '\\u21AE',\n\tnleq: '\\u2270',\n\tnleqq: '\\u2266\\u0338',\n\tnleqslant: '\\u2A7D\\u0338',\n\tnles: '\\u2A7D\\u0338',\n\tnless: '\\u226E',\n\tnLl: '\\u22D8\\u0338',\n\tnlsim: '\\u2274',\n\tnLt: '\\u226A\\u20D2',\n\tnlt: '\\u226E',\n\tnltri: '\\u22EA',\n\tnltrie: '\\u22EC',\n\tnLtv: '\\u226A\\u0338',\n\tnmid: '\\u2224',\n\tNoBreak: '\\u2060',\n\tNonBreakingSpace: '\\u00A0',\n\tNopf: '\\u2115',\n\tnopf: '\\uD835\\uDD5F',\n\tNot: '\\u2AEC',\n\tnot: '\\u00AC',\n\tNotCongruent: '\\u2262',\n\tNotCupCap: '\\u226D',\n\tNotDoubleVerticalBar: '\\u2226',\n\tNotElement: '\\u2209',\n\tNotEqual: '\\u2260',\n\tNotEqualTilde: '\\u2242\\u0338',\n\tNotExists: '\\u2204',\n\tNotGreater: '\\u226F',\n\tNotGreaterEqual: '\\u2271',\n\tNotGreaterFullEqual: '\\u2267\\u0338',\n\tNotGreaterGreater: '\\u226B\\u0338',\n\tNotGreaterLess: '\\u2279',\n\tNotGreaterSlantEqual: '\\u2A7E\\u0338',\n\tNotGreaterTilde: '\\u2275',\n\tNotHumpDownHump: '\\u224E\\u0338',\n\tNotHumpEqual: '\\u224F\\u0338',\n\tnotin: '\\u2209',\n\tnotindot: '\\u22F5\\u0338',\n\tnotinE: '\\u22F9\\u0338',\n\tnotinva: '\\u2209',\n\tnotinvb: '\\u22F7',\n\tnotinvc: '\\u22F6',\n\tNotLeftTriangle: '\\u22EA',\n\tNotLeftTriangleBar: '\\u29CF\\u0338',\n\tNotLeftTriangleEqual: '\\u22EC',\n\tNotLess: '\\u226E',\n\tNotLessEqual: '\\u2270',\n\tNotLessGreater: '\\u2278',\n\tNotLessLess: '\\u226A\\u0338',\n\tNotLessSlantEqual: '\\u2A7D\\u0338',\n\tNotLessTilde: '\\u2274',\n\tNotNestedGreaterGreater: '\\u2AA2\\u0338',\n\tNotNestedLessLess: '\\u2AA1\\u0338',\n\tnotni: '\\u220C',\n\tnotniva: '\\u220C',\n\tnotnivb: '\\u22FE',\n\tnotnivc: '\\u22FD',\n\tNotPrecedes: '\\u2280',\n\tNotPrecedesEqual: '\\u2AAF\\u0338',\n\tNotPrecedesSlantEqual: '\\u22E0',\n\tNotReverseElement: '\\u220C',\n\tNotRightTriangle: '\\u22EB',\n\tNotRightTriangleBar: '\\u29D0\\u0338',\n\tNotRightTriangleEqual: '\\u22ED',\n\tNotSquareSubset: '\\u228F\\u0338',\n\tNotSquareSubsetEqual: '\\u22E2',\n\tNotSquareSuperset: '\\u2290\\u0338',\n\tNotSquareSupersetEqual: '\\u22E3',\n\tNotSubset: '\\u2282\\u20D2',\n\tNotSubsetEqual: '\\u2288',\n\tNotSucceeds: '\\u2281',\n\tNotSucceedsEqual: '\\u2AB0\\u0338',\n\tNotSucceedsSlantEqual: '\\u22E1',\n\tNotSucceedsTilde: '\\u227F\\u0338',\n\tNotSuperset: '\\u2283\\u20D2',\n\tNotSupersetEqual: '\\u2289',\n\tNotTilde: '\\u2241',\n\tNotTildeEqual: '\\u2244',\n\tNotTildeFullEqual: '\\u2247',\n\tNotTildeTilde: '\\u2249',\n\tNotVerticalBar: '\\u2224',\n\tnpar: '\\u2226',\n\tnparallel: '\\u2226',\n\tnparsl: '\\u2AFD\\u20E5',\n\tnpart: '\\u2202\\u0338',\n\tnpolint: '\\u2A14',\n\tnpr: '\\u2280',\n\tnprcue: '\\u22E0',\n\tnpre: '\\u2AAF\\u0338',\n\tnprec: '\\u2280',\n\tnpreceq: '\\u2AAF\\u0338',\n\tnrArr: '\\u21CF',\n\tnrarr: '\\u219B',\n\tnrarrc: '\\u2933\\u0338',\n\tnrarrw: '\\u219D\\u0338',\n\tnRightarrow: '\\u21CF',\n\tnrightarrow: '\\u219B',\n\tnrtri: '\\u22EB',\n\tnrtrie: '\\u22ED',\n\tnsc: '\\u2281',\n\tnsccue: '\\u22E1',\n\tnsce: '\\u2AB0\\u0338',\n\tNscr: '\\uD835\\uDCA9',\n\tnscr: '\\uD835\\uDCC3',\n\tnshortmid: '\\u2224',\n\tnshortparallel: '\\u2226',\n\tnsim: '\\u2241',\n\tnsime: '\\u2244',\n\tnsimeq: '\\u2244',\n\tnsmid: '\\u2224',\n\tnspar: '\\u2226',\n\tnsqsube: '\\u22E2',\n\tnsqsupe: '\\u22E3',\n\tnsub: '\\u2284',\n\tnsubE: '\\u2AC5\\u0338',\n\tnsube: '\\u2288',\n\tnsubset: '\\u2282\\u20D2',\n\tnsubseteq: '\\u2288',\n\tnsubseteqq: '\\u2AC5\\u0338',\n\tnsucc: '\\u2281',\n\tnsucceq: '\\u2AB0\\u0338',\n\tnsup: '\\u2285',\n\tnsupE: '\\u2AC6\\u0338',\n\tnsupe: '\\u2289',\n\tnsupset: '\\u2283\\u20D2',\n\tnsupseteq: '\\u2289',\n\tnsupseteqq: '\\u2AC6\\u0338',\n\tntgl: '\\u2279',\n\tNtilde: '\\u00D1',\n\tntilde: '\\u00F1',\n\tntlg: '\\u2278',\n\tntriangleleft: '\\u22EA',\n\tntrianglelefteq: '\\u22EC',\n\tntriangleright: '\\u22EB',\n\tntrianglerighteq: '\\u22ED',\n\tNu: '\\u039D',\n\tnu: '\\u03BD',\n\tnum: '\\u0023',\n\tnumero: '\\u2116',\n\tnumsp: '\\u2007',\n\tnvap: '\\u224D\\u20D2',\n\tnVDash: '\\u22AF',\n\tnVdash: '\\u22AE',\n\tnvDash: '\\u22AD',\n\tnvdash: '\\u22AC',\n\tnvge: '\\u2265\\u20D2',\n\tnvgt: '\\u003E\\u20D2',\n\tnvHarr: '\\u2904',\n\tnvinfin: '\\u29DE',\n\tnvlArr: '\\u2902',\n\tnvle: '\\u2264\\u20D2',\n\tnvlt: '\\u003C\\u20D2',\n\tnvltrie: '\\u22B4\\u20D2',\n\tnvrArr: '\\u2903',\n\tnvrtrie: '\\u22B5\\u20D2',\n\tnvsim: '\\u223C\\u20D2',\n\tnwarhk: '\\u2923',\n\tnwArr: '\\u21D6',\n\tnwarr: '\\u2196',\n\tnwarrow: '\\u2196',\n\tnwnear: '\\u2927',\n\tOacute: '\\u00D3',\n\toacute: '\\u00F3',\n\toast: '\\u229B',\n\tocir: '\\u229A',\n\tOcirc: '\\u00D4',\n\tocirc: '\\u00F4',\n\tOcy: '\\u041E',\n\tocy: '\\u043E',\n\todash: '\\u229D',\n\tOdblac: '\\u0150',\n\todblac: '\\u0151',\n\todiv: '\\u2A38',\n\todot: '\\u2299',\n\todsold: '\\u29BC',\n\tOElig: '\\u0152',\n\toelig: '\\u0153',\n\tofcir: '\\u29BF',\n\tOfr: '\\uD835\\uDD12',\n\tofr: '\\uD835\\uDD2C',\n\togon: '\\u02DB',\n\tOgrave: '\\u00D2',\n\tograve: '\\u00F2',\n\togt: '\\u29C1',\n\tohbar: '\\u29B5',\n\tohm: '\\u03A9',\n\toint: '\\u222E',\n\tolarr: '\\u21BA',\n\tolcir: '\\u29BE',\n\tolcross: '\\u29BB',\n\toline: '\\u203E',\n\tolt: '\\u29C0',\n\tOmacr: '\\u014C',\n\tomacr: '\\u014D',\n\tOmega: '\\u03A9',\n\tomega: '\\u03C9',\n\tOmicron: '\\u039F',\n\tomicron: '\\u03BF',\n\tomid: '\\u29B6',\n\tominus: '\\u2296',\n\tOopf: '\\uD835\\uDD46',\n\toopf: '\\uD835\\uDD60',\n\topar: '\\u29B7',\n\tOpenCurlyDoubleQuote: '\\u201C',\n\tOpenCurlyQuote: '\\u2018',\n\toperp: '\\u29B9',\n\toplus: '\\u2295',\n\tOr: '\\u2A54',\n\tor: '\\u2228',\n\torarr: '\\u21BB',\n\tord: '\\u2A5D',\n\torder: '\\u2134',\n\torderof: '\\u2134',\n\tordf: '\\u00AA',\n\tordm: '\\u00BA',\n\torigof: '\\u22B6',\n\toror: '\\u2A56',\n\torslope: '\\u2A57',\n\torv: '\\u2A5B',\n\toS: '\\u24C8',\n\tOscr: '\\uD835\\uDCAA',\n\toscr: '\\u2134',\n\tOslash: '\\u00D8',\n\toslash: '\\u00F8',\n\tosol: '\\u2298',\n\tOtilde: '\\u00D5',\n\totilde: '\\u00F5',\n\tOtimes: '\\u2A37',\n\totimes: '\\u2297',\n\totimesas: '\\u2A36',\n\tOuml: '\\u00D6',\n\touml: '\\u00F6',\n\tovbar: '\\u233D',\n\tOverBar: '\\u203E',\n\tOverBrace: '\\u23DE',\n\tOverBracket: '\\u23B4',\n\tOverParenthesis: '\\u23DC',\n\tpar: '\\u2225',\n\tpara: '\\u00B6',\n\tparallel: '\\u2225',\n\tparsim: '\\u2AF3',\n\tparsl: '\\u2AFD',\n\tpart: '\\u2202',\n\tPartialD: '\\u2202',\n\tPcy: '\\u041F',\n\tpcy: '\\u043F',\n\tpercnt: '\\u0025',\n\tperiod: '\\u002E',\n\tpermil: '\\u2030',\n\tperp: '\\u22A5',\n\tpertenk: '\\u2031',\n\tPfr: '\\uD835\\uDD13',\n\tpfr: '\\uD835\\uDD2D',\n\tPhi: '\\u03A6',\n\tphi: '\\u03C6',\n\tphiv: '\\u03D5',\n\tphmmat: '\\u2133',\n\tphone: '\\u260E',\n\tPi: '\\u03A0',\n\tpi: '\\u03C0',\n\tpitchfork: '\\u22D4',\n\tpiv: '\\u03D6',\n\tplanck: '\\u210F',\n\tplanckh: '\\u210E',\n\tplankv: '\\u210F',\n\tplus: '\\u002B',\n\tplusacir: '\\u2A23',\n\tplusb: '\\u229E',\n\tpluscir: '\\u2A22',\n\tplusdo: '\\u2214',\n\tplusdu: '\\u2A25',\n\tpluse: '\\u2A72',\n\tPlusMinus: '\\u00B1',\n\tplusmn: '\\u00B1',\n\tplussim: '\\u2A26',\n\tplustwo: '\\u2A27',\n\tpm: '\\u00B1',\n\tPoincareplane: '\\u210C',\n\tpointint: '\\u2A15',\n\tPopf: '\\u2119',\n\tpopf: '\\uD835\\uDD61',\n\tpound: '\\u00A3',\n\tPr: '\\u2ABB',\n\tpr: '\\u227A',\n\tprap: '\\u2AB7',\n\tprcue: '\\u227C',\n\tprE: '\\u2AB3',\n\tpre: '\\u2AAF',\n\tprec: '\\u227A',\n\tprecapprox: '\\u2AB7',\n\tpreccurlyeq: '\\u227C',\n\tPrecedes: '\\u227A',\n\tPrecedesEqual: '\\u2AAF',\n\tPrecedesSlantEqual: '\\u227C',\n\tPrecedesTilde: '\\u227E',\n\tpreceq: '\\u2AAF',\n\tprecnapprox: '\\u2AB9',\n\tprecneqq: '\\u2AB5',\n\tprecnsim: '\\u22E8',\n\tprecsim: '\\u227E',\n\tPrime: '\\u2033',\n\tprime: '\\u2032',\n\tprimes: '\\u2119',\n\tprnap: '\\u2AB9',\n\tprnE: '\\u2AB5',\n\tprnsim: '\\u22E8',\n\tprod: '\\u220F',\n\tProduct: '\\u220F',\n\tprofalar: '\\u232E',\n\tprofline: '\\u2312',\n\tprofsurf: '\\u2313',\n\tprop: '\\u221D',\n\tProportion: '\\u2237',\n\tProportional: '\\u221D',\n\tpropto: '\\u221D',\n\tprsim: '\\u227E',\n\tprurel: '\\u22B0',\n\tPscr: '\\uD835\\uDCAB',\n\tpscr: '\\uD835\\uDCC5',\n\tPsi: '\\u03A8',\n\tpsi: '\\u03C8',\n\tpuncsp: '\\u2008',\n\tQfr: '\\uD835\\uDD14',\n\tqfr: '\\uD835\\uDD2E',\n\tqint: '\\u2A0C',\n\tQopf: '\\u211A',\n\tqopf: '\\uD835\\uDD62',\n\tqprime: '\\u2057',\n\tQscr: '\\uD835\\uDCAC',\n\tqscr: '\\uD835\\uDCC6',\n\tquaternions: '\\u210D',\n\tquatint: '\\u2A16',\n\tquest: '\\u003F',\n\tquesteq: '\\u225F',\n\tQUOT: '\\u0022',\n\tquot: '\\u0022',\n\trAarr: '\\u21DB',\n\trace: '\\u223D\\u0331',\n\tRacute: '\\u0154',\n\tracute: '\\u0155',\n\tradic: '\\u221A',\n\traemptyv: '\\u29B3',\n\tRang: '\\u27EB',\n\trang: '\\u27E9',\n\trangd: '\\u2992',\n\trange: '\\u29A5',\n\trangle: '\\u27E9',\n\traquo: '\\u00BB',\n\tRarr: '\\u21A0',\n\trArr: '\\u21D2',\n\trarr: '\\u2192',\n\trarrap: '\\u2975',\n\trarrb: '\\u21E5',\n\trarrbfs: '\\u2920',\n\trarrc: '\\u2933',\n\trarrfs: '\\u291E',\n\trarrhk: '\\u21AA',\n\trarrlp: '\\u21AC',\n\trarrpl: '\\u2945',\n\trarrsim: '\\u2974',\n\tRarrtl: '\\u2916',\n\trarrtl: '\\u21A3',\n\trarrw: '\\u219D',\n\trAtail: '\\u291C',\n\tratail: '\\u291A',\n\tratio: '\\u2236',\n\trationals: '\\u211A',\n\tRBarr: '\\u2910',\n\trBarr: '\\u290F',\n\trbarr: '\\u290D',\n\trbbrk: '\\u2773',\n\trbrace: '\\u007D',\n\trbrack: '\\u005D',\n\trbrke: '\\u298C',\n\trbrksld: '\\u298E',\n\trbrkslu: '\\u2990',\n\tRcaron: '\\u0158',\n\trcaron: '\\u0159',\n\tRcedil: '\\u0156',\n\trcedil: '\\u0157',\n\trceil: '\\u2309',\n\trcub: '\\u007D',\n\tRcy: '\\u0420',\n\trcy: '\\u0440',\n\trdca: '\\u2937',\n\trdldhar: '\\u2969',\n\trdquo: '\\u201D',\n\trdquor: '\\u201D',\n\trdsh: '\\u21B3',\n\tRe: '\\u211C',\n\treal: '\\u211C',\n\trealine: '\\u211B',\n\trealpart: '\\u211C',\n\treals: '\\u211D',\n\trect: '\\u25AD',\n\tREG: '\\u00AE',\n\treg: '\\u00AE',\n\tReverseElement: '\\u220B',\n\tReverseEquilibrium: '\\u21CB',\n\tReverseUpEquilibrium: '\\u296F',\n\trfisht: '\\u297D',\n\trfloor: '\\u230B',\n\tRfr: '\\u211C',\n\trfr: '\\uD835\\uDD2F',\n\trHar: '\\u2964',\n\trhard: '\\u21C1',\n\trharu: '\\u21C0',\n\trharul: '\\u296C',\n\tRho: '\\u03A1',\n\trho: '\\u03C1',\n\trhov: '\\u03F1',\n\tRightAngleBracket: '\\u27E9',\n\tRightArrow: '\\u2192',\n\tRightarrow: '\\u21D2',\n\trightarrow: '\\u2192',\n\tRightArrowBar: '\\u21E5',\n\tRightArrowLeftArrow: '\\u21C4',\n\trightarrowtail: '\\u21A3',\n\tRightCeiling: '\\u2309',\n\tRightDoubleBracket: '\\u27E7',\n\tRightDownTeeVector: '\\u295D',\n\tRightDownVector: '\\u21C2',\n\tRightDownVectorBar: '\\u2955',\n\tRightFloor: '\\u230B',\n\trightharpoondown: '\\u21C1',\n\trightharpoonup: '\\u21C0',\n\trightleftarrows: '\\u21C4',\n\trightleftharpoons: '\\u21CC',\n\trightrightarrows: '\\u21C9',\n\trightsquigarrow: '\\u219D',\n\tRightTee: '\\u22A2',\n\tRightTeeArrow: '\\u21A6',\n\tRightTeeVector: '\\u295B',\n\trightthreetimes: '\\u22CC',\n\tRightTriangle: '\\u22B3',\n\tRightTriangleBar: '\\u29D0',\n\tRightTriangleEqual: '\\u22B5',\n\tRightUpDownVector: '\\u294F',\n\tRightUpTeeVector: '\\u295C',\n\tRightUpVector: '\\u21BE',\n\tRightUpVectorBar: '\\u2954',\n\tRightVector: '\\u21C0',\n\tRightVectorBar: '\\u2953',\n\tring: '\\u02DA',\n\trisingdotseq: '\\u2253',\n\trlarr: '\\u21C4',\n\trlhar: '\\u21CC',\n\trlm: '\\u200F',\n\trmoust: '\\u23B1',\n\trmoustache: '\\u23B1',\n\trnmid: '\\u2AEE',\n\troang: '\\u27ED',\n\troarr: '\\u21FE',\n\trobrk: '\\u27E7',\n\tropar: '\\u2986',\n\tRopf: '\\u211D',\n\tropf: '\\uD835\\uDD63',\n\troplus: '\\u2A2E',\n\trotimes: '\\u2A35',\n\tRoundImplies: '\\u2970',\n\trpar: '\\u0029',\n\trpargt: '\\u2994',\n\trppolint: '\\u2A12',\n\trrarr: '\\u21C9',\n\tRrightarrow: '\\u21DB',\n\trsaquo: '\\u203A',\n\tRscr: '\\u211B',\n\trscr: '\\uD835\\uDCC7',\n\tRsh: '\\u21B1',\n\trsh: '\\u21B1',\n\trsqb: '\\u005D',\n\trsquo: '\\u2019',\n\trsquor: '\\u2019',\n\trthree: '\\u22CC',\n\trtimes: '\\u22CA',\n\trtri: '\\u25B9',\n\trtrie: '\\u22B5',\n\trtrif: '\\u25B8',\n\trtriltri: '\\u29CE',\n\tRuleDelayed: '\\u29F4',\n\truluhar: '\\u2968',\n\trx: '\\u211E',\n\tSacute: '\\u015A',\n\tsacute: '\\u015B',\n\tsbquo: '\\u201A',\n\tSc: '\\u2ABC',\n\tsc: '\\u227B',\n\tscap: '\\u2AB8',\n\tScaron: '\\u0160',\n\tscaron: '\\u0161',\n\tsccue: '\\u227D',\n\tscE: '\\u2AB4',\n\tsce: '\\u2AB0',\n\tScedil: '\\u015E',\n\tscedil: '\\u015F',\n\tScirc: '\\u015C',\n\tscirc: '\\u015D',\n\tscnap: '\\u2ABA',\n\tscnE: '\\u2AB6',\n\tscnsim: '\\u22E9',\n\tscpolint: '\\u2A13',\n\tscsim: '\\u227F',\n\tScy: '\\u0421',\n\tscy: '\\u0441',\n\tsdot: '\\u22C5',\n\tsdotb: '\\u22A1',\n\tsdote: '\\u2A66',\n\tsearhk: '\\u2925',\n\tseArr: '\\u21D8',\n\tsearr: '\\u2198',\n\tsearrow: '\\u2198',\n\tsect: '\\u00A7',\n\tsemi: '\\u003B',\n\tseswar: '\\u2929',\n\tsetminus: '\\u2216',\n\tsetmn: '\\u2216',\n\tsext: '\\u2736',\n\tSfr: '\\uD835\\uDD16',\n\tsfr: '\\uD835\\uDD30',\n\tsfrown: '\\u2322',\n\tsharp: '\\u266F',\n\tSHCHcy: '\\u0429',\n\tshchcy: '\\u0449',\n\tSHcy: '\\u0428',\n\tshcy: '\\u0448',\n\tShortDownArrow: '\\u2193',\n\tShortLeftArrow: '\\u2190',\n\tshortmid: '\\u2223',\n\tshortparallel: '\\u2225',\n\tShortRightArrow: '\\u2192',\n\tShortUpArrow: '\\u2191',\n\tshy: '\\u00AD',\n\tSigma: '\\u03A3',\n\tsigma: '\\u03C3',\n\tsigmaf: '\\u03C2',\n\tsigmav: '\\u03C2',\n\tsim: '\\u223C',\n\tsimdot: '\\u2A6A',\n\tsime: '\\u2243',\n\tsimeq: '\\u2243',\n\tsimg: '\\u2A9E',\n\tsimgE: '\\u2AA0',\n\tsiml: '\\u2A9D',\n\tsimlE: '\\u2A9F',\n\tsimne: '\\u2246',\n\tsimplus: '\\u2A24',\n\tsimrarr: '\\u2972',\n\tslarr: '\\u2190',\n\tSmallCircle: '\\u2218',\n\tsmallsetminus: '\\u2216',\n\tsmashp: '\\u2A33',\n\tsmeparsl: '\\u29E4',\n\tsmid: '\\u2223',\n\tsmile: '\\u2323',\n\tsmt: '\\u2AAA',\n\tsmte: '\\u2AAC',\n\tsmtes: '\\u2AAC\\uFE00',\n\tSOFTcy: '\\u042C',\n\tsoftcy: '\\u044C',\n\tsol: '\\u002F',\n\tsolb: '\\u29C4',\n\tsolbar: '\\u233F',\n\tSopf: '\\uD835\\uDD4A',\n\tsopf: '\\uD835\\uDD64',\n\tspades: '\\u2660',\n\tspadesuit: '\\u2660',\n\tspar: '\\u2225',\n\tsqcap: '\\u2293',\n\tsqcaps: '\\u2293\\uFE00',\n\tsqcup: '\\u2294',\n\tsqcups: '\\u2294\\uFE00',\n\tSqrt: '\\u221A',\n\tsqsub: '\\u228F',\n\tsqsube: '\\u2291',\n\tsqsubset: '\\u228F',\n\tsqsubseteq: '\\u2291',\n\tsqsup: '\\u2290',\n\tsqsupe: '\\u2292',\n\tsqsupset: '\\u2290',\n\tsqsupseteq: '\\u2292',\n\tsqu: '\\u25A1',\n\tSquare: '\\u25A1',\n\tsquare: '\\u25A1',\n\tSquareIntersection: '\\u2293',\n\tSquareSubset: '\\u228F',\n\tSquareSubsetEqual: '\\u2291',\n\tSquareSuperset: '\\u2290',\n\tSquareSupersetEqual: '\\u2292',\n\tSquareUnion: '\\u2294',\n\tsquarf: '\\u25AA',\n\tsquf: '\\u25AA',\n\tsrarr: '\\u2192',\n\tSscr: '\\uD835\\uDCAE',\n\tsscr: '\\uD835\\uDCC8',\n\tssetmn: '\\u2216',\n\tssmile: '\\u2323',\n\tsstarf: '\\u22C6',\n\tStar: '\\u22C6',\n\tstar: '\\u2606',\n\tstarf: '\\u2605',\n\tstraightepsilon: '\\u03F5',\n\tstraightphi: '\\u03D5',\n\tstrns: '\\u00AF',\n\tSub: '\\u22D0',\n\tsub: '\\u2282',\n\tsubdot: '\\u2ABD',\n\tsubE: '\\u2AC5',\n\tsube: '\\u2286',\n\tsubedot: '\\u2AC3',\n\tsubmult: '\\u2AC1',\n\tsubnE: '\\u2ACB',\n\tsubne: '\\u228A',\n\tsubplus: '\\u2ABF',\n\tsubrarr: '\\u2979',\n\tSubset: '\\u22D0',\n\tsubset: '\\u2282',\n\tsubseteq: '\\u2286',\n\tsubseteqq: '\\u2AC5',\n\tSubsetEqual: '\\u2286',\n\tsubsetneq: '\\u228A',\n\tsubsetneqq: '\\u2ACB',\n\tsubsim: '\\u2AC7',\n\tsubsub: '\\u2AD5',\n\tsubsup: '\\u2AD3',\n\tsucc: '\\u227B',\n\tsuccapprox: '\\u2AB8',\n\tsucccurlyeq: '\\u227D',\n\tSucceeds: '\\u227B',\n\tSucceedsEqual: '\\u2AB0',\n\tSucceedsSlantEqual: '\\u227D',\n\tSucceedsTilde: '\\u227F',\n\tsucceq: '\\u2AB0',\n\tsuccnapprox: '\\u2ABA',\n\tsuccneqq: '\\u2AB6',\n\tsuccnsim: '\\u22E9',\n\tsuccsim: '\\u227F',\n\tSuchThat: '\\u220B',\n\tSum: '\\u2211',\n\tsum: '\\u2211',\n\tsung: '\\u266A',\n\tSup: '\\u22D1',\n\tsup: '\\u2283',\n\tsup1: '\\u00B9',\n\tsup2: '\\u00B2',\n\tsup3: '\\u00B3',\n\tsupdot: '\\u2ABE',\n\tsupdsub: '\\u2AD8',\n\tsupE: '\\u2AC6',\n\tsupe: '\\u2287',\n\tsupedot: '\\u2AC4',\n\tSuperset: '\\u2283',\n\tSupersetEqual: '\\u2287',\n\tsuphsol: '\\u27C9',\n\tsuphsub: '\\u2AD7',\n\tsuplarr: '\\u297B',\n\tsupmult: '\\u2AC2',\n\tsupnE: '\\u2ACC',\n\tsupne: '\\u228B',\n\tsupplus: '\\u2AC0',\n\tSupset: '\\u22D1',\n\tsupset: '\\u2283',\n\tsupseteq: '\\u2287',\n\tsupseteqq: '\\u2AC6',\n\tsupsetneq: '\\u228B',\n\tsupsetneqq: '\\u2ACC',\n\tsupsim: '\\u2AC8',\n\tsupsub: '\\u2AD4',\n\tsupsup: '\\u2AD6',\n\tswarhk: '\\u2926',\n\tswArr: '\\u21D9',\n\tswarr: '\\u2199',\n\tswarrow: '\\u2199',\n\tswnwar: '\\u292A',\n\tszlig: '\\u00DF',\n\tTab: '\\u0009',\n\ttarget: '\\u2316',\n\tTau: '\\u03A4',\n\ttau: '\\u03C4',\n\ttbrk: '\\u23B4',\n\tTcaron: '\\u0164',\n\ttcaron: '\\u0165',\n\tTcedil: '\\u0162',\n\ttcedil: '\\u0163',\n\tTcy: '\\u0422',\n\ttcy: '\\u0442',\n\ttdot: '\\u20DB',\n\ttelrec: '\\u2315',\n\tTfr: '\\uD835\\uDD17',\n\ttfr: '\\uD835\\uDD31',\n\tthere4: '\\u2234',\n\tTherefore: '\\u2234',\n\ttherefore: '\\u2234',\n\tTheta: '\\u0398',\n\ttheta: '\\u03B8',\n\tthetasym: '\\u03D1',\n\tthetav: '\\u03D1',\n\tthickapprox: '\\u2248',\n\tthicksim: '\\u223C',\n\tThickSpace: '\\u205F\\u200A',\n\tthinsp: '\\u2009',\n\tThinSpace: '\\u2009',\n\tthkap: '\\u2248',\n\tthksim: '\\u223C',\n\tTHORN: '\\u00DE',\n\tthorn: '\\u00FE',\n\tTilde: '\\u223C',\n\ttilde: '\\u02DC',\n\tTildeEqual: '\\u2243',\n\tTildeFullEqual: '\\u2245',\n\tTildeTilde: '\\u2248',\n\ttimes: '\\u00D7',\n\ttimesb: '\\u22A0',\n\ttimesbar: '\\u2A31',\n\ttimesd: '\\u2A30',\n\ttint: '\\u222D',\n\ttoea: '\\u2928',\n\ttop: '\\u22A4',\n\ttopbot: '\\u2336',\n\ttopcir: '\\u2AF1',\n\tTopf: '\\uD835\\uDD4B',\n\ttopf: '\\uD835\\uDD65',\n\ttopfork: '\\u2ADA',\n\ttosa: '\\u2929',\n\ttprime: '\\u2034',\n\tTRADE: '\\u2122',\n\ttrade: '\\u2122',\n\ttriangle: '\\u25B5',\n\ttriangledown: '\\u25BF',\n\ttriangleleft: '\\u25C3',\n\ttrianglelefteq: '\\u22B4',\n\ttriangleq: '\\u225C',\n\ttriangleright: '\\u25B9',\n\ttrianglerighteq: '\\u22B5',\n\ttridot: '\\u25EC',\n\ttrie: '\\u225C',\n\ttriminus: '\\u2A3A',\n\tTripleDot: '\\u20DB',\n\ttriplus: '\\u2A39',\n\ttrisb: '\\u29CD',\n\ttritime: '\\u2A3B',\n\ttrpezium: '\\u23E2',\n\tTscr: '\\uD835\\uDCAF',\n\ttscr: '\\uD835\\uDCC9',\n\tTScy: '\\u0426',\n\ttscy: '\\u0446',\n\tTSHcy: '\\u040B',\n\ttshcy: '\\u045B',\n\tTstrok: '\\u0166',\n\ttstrok: '\\u0167',\n\ttwixt: '\\u226C',\n\ttwoheadleftarrow: '\\u219E',\n\ttwoheadrightarrow: '\\u21A0',\n\tUacute: '\\u00DA',\n\tuacute: '\\u00FA',\n\tUarr: '\\u219F',\n\tuArr: '\\u21D1',\n\tuarr: '\\u2191',\n\tUarrocir: '\\u2949',\n\tUbrcy: '\\u040E',\n\tubrcy: '\\u045E',\n\tUbreve: '\\u016C',\n\tubreve: '\\u016D',\n\tUcirc: '\\u00DB',\n\tucirc: '\\u00FB',\n\tUcy: '\\u0423',\n\tucy: '\\u0443',\n\tudarr: '\\u21C5',\n\tUdblac: '\\u0170',\n\tudblac: '\\u0171',\n\tudhar: '\\u296E',\n\tufisht: '\\u297E',\n\tUfr: '\\uD835\\uDD18',\n\tufr: '\\uD835\\uDD32',\n\tUgrave: '\\u00D9',\n\tugrave: '\\u00F9',\n\tuHar: '\\u2963',\n\tuharl: '\\u21BF',\n\tuharr: '\\u21BE',\n\tuhblk: '\\u2580',\n\tulcorn: '\\u231C',\n\tulcorner: '\\u231C',\n\tulcrop: '\\u230F',\n\tultri: '\\u25F8',\n\tUmacr: '\\u016A',\n\tumacr: '\\u016B',\n\tuml: '\\u00A8',\n\tUnderBar: '\\u005F',\n\tUnderBrace: '\\u23DF',\n\tUnderBracket: '\\u23B5',\n\tUnderParenthesis: '\\u23DD',\n\tUnion: '\\u22C3',\n\tUnionPlus: '\\u228E',\n\tUogon: '\\u0172',\n\tuogon: '\\u0173',\n\tUopf: '\\uD835\\uDD4C',\n\tuopf: '\\uD835\\uDD66',\n\tUpArrow: '\\u2191',\n\tUparrow: '\\u21D1',\n\tuparrow: '\\u2191',\n\tUpArrowBar: '\\u2912',\n\tUpArrowDownArrow: '\\u21C5',\n\tUpDownArrow: '\\u2195',\n\tUpdownarrow: '\\u21D5',\n\tupdownarrow: '\\u2195',\n\tUpEquilibrium: '\\u296E',\n\tupharpoonleft: '\\u21BF',\n\tupharpoonright: '\\u21BE',\n\tuplus: '\\u228E',\n\tUpperLeftArrow: '\\u2196',\n\tUpperRightArrow: '\\u2197',\n\tUpsi: '\\u03D2',\n\tupsi: '\\u03C5',\n\tupsih: '\\u03D2',\n\tUpsilon: '\\u03A5',\n\tupsilon: '\\u03C5',\n\tUpTee: '\\u22A5',\n\tUpTeeArrow: '\\u21A5',\n\tupuparrows: '\\u21C8',\n\turcorn: '\\u231D',\n\turcorner: '\\u231D',\n\turcrop: '\\u230E',\n\tUring: '\\u016E',\n\turing: '\\u016F',\n\turtri: '\\u25F9',\n\tUscr: '\\uD835\\uDCB0',\n\tuscr: '\\uD835\\uDCCA',\n\tutdot: '\\u22F0',\n\tUtilde: '\\u0168',\n\tutilde: '\\u0169',\n\tutri: '\\u25B5',\n\tutrif: '\\u25B4',\n\tuuarr: '\\u21C8',\n\tUuml: '\\u00DC',\n\tuuml: '\\u00FC',\n\tuwangle: '\\u29A7',\n\tvangrt: '\\u299C',\n\tvarepsilon: '\\u03F5',\n\tvarkappa: '\\u03F0',\n\tvarnothing: '\\u2205',\n\tvarphi: '\\u03D5',\n\tvarpi: '\\u03D6',\n\tvarpropto: '\\u221D',\n\tvArr: '\\u21D5',\n\tvarr: '\\u2195',\n\tvarrho: '\\u03F1',\n\tvarsigma: '\\u03C2',\n\tvarsubsetneq: '\\u228A\\uFE00',\n\tvarsubsetneqq: '\\u2ACB\\uFE00',\n\tvarsupsetneq: '\\u228B\\uFE00',\n\tvarsupsetneqq: '\\u2ACC\\uFE00',\n\tvartheta: '\\u03D1',\n\tvartriangleleft: '\\u22B2',\n\tvartriangleright: '\\u22B3',\n\tVbar: '\\u2AEB',\n\tvBar: '\\u2AE8',\n\tvBarv: '\\u2AE9',\n\tVcy: '\\u0412',\n\tvcy: '\\u0432',\n\tVDash: '\\u22AB',\n\tVdash: '\\u22A9',\n\tvDash: '\\u22A8',\n\tvdash: '\\u22A2',\n\tVdashl: '\\u2AE6',\n\tVee: '\\u22C1',\n\tvee: '\\u2228',\n\tveebar: '\\u22BB',\n\tveeeq: '\\u225A',\n\tvellip: '\\u22EE',\n\tVerbar: '\\u2016',\n\tverbar: '\\u007C',\n\tVert: '\\u2016',\n\tvert: '\\u007C',\n\tVerticalBar: '\\u2223',\n\tVerticalLine: '\\u007C',\n\tVerticalSeparator: '\\u2758',\n\tVerticalTilde: '\\u2240',\n\tVeryThinSpace: '\\u200A',\n\tVfr: '\\uD835\\uDD19',\n\tvfr: '\\uD835\\uDD33',\n\tvltri: '\\u22B2',\n\tvnsub: '\\u2282\\u20D2',\n\tvnsup: '\\u2283\\u20D2',\n\tVopf: '\\uD835\\uDD4D',\n\tvopf: '\\uD835\\uDD67',\n\tvprop: '\\u221D',\n\tvrtri: '\\u22B3',\n\tVscr: '\\uD835\\uDCB1',\n\tvscr: '\\uD835\\uDCCB',\n\tvsubnE: '\\u2ACB\\uFE00',\n\tvsubne: '\\u228A\\uFE00',\n\tvsupnE: '\\u2ACC\\uFE00',\n\tvsupne: '\\u228B\\uFE00',\n\tVvdash: '\\u22AA',\n\tvzigzag: '\\u299A',\n\tWcirc: '\\u0174',\n\twcirc: '\\u0175',\n\twedbar: '\\u2A5F',\n\tWedge: '\\u22C0',\n\twedge: '\\u2227',\n\twedgeq: '\\u2259',\n\tweierp: '\\u2118',\n\tWfr: '\\uD835\\uDD1A',\n\twfr: '\\uD835\\uDD34',\n\tWopf: '\\uD835\\uDD4E',\n\twopf: '\\uD835\\uDD68',\n\twp: '\\u2118',\n\twr: '\\u2240',\n\twreath: '\\u2240',\n\tWscr: '\\uD835\\uDCB2',\n\twscr: '\\uD835\\uDCCC',\n\txcap: '\\u22C2',\n\txcirc: '\\u25EF',\n\txcup: '\\u22C3',\n\txdtri: '\\u25BD',\n\tXfr: '\\uD835\\uDD1B',\n\txfr: '\\uD835\\uDD35',\n\txhArr: '\\u27FA',\n\txharr: '\\u27F7',\n\tXi: '\\u039E',\n\txi: '\\u03BE',\n\txlArr: '\\u27F8',\n\txlarr: '\\u27F5',\n\txmap: '\\u27FC',\n\txnis: '\\u22FB',\n\txodot: '\\u2A00',\n\tXopf: '\\uD835\\uDD4F',\n\txopf: '\\uD835\\uDD69',\n\txoplus: '\\u2A01',\n\txotime: '\\u2A02',\n\txrArr: '\\u27F9',\n\txrarr: '\\u27F6',\n\tXscr: '\\uD835\\uDCB3',\n\txscr: '\\uD835\\uDCCD',\n\txsqcup: '\\u2A06',\n\txuplus: '\\u2A04',\n\txutri: '\\u25B3',\n\txvee: '\\u22C1',\n\txwedge: '\\u22C0',\n\tYacute: '\\u00DD',\n\tyacute: '\\u00FD',\n\tYAcy: '\\u042F',\n\tyacy: '\\u044F',\n\tYcirc: '\\u0176',\n\tycirc: '\\u0177',\n\tYcy: '\\u042B',\n\tycy: '\\u044B',\n\tyen: '\\u00A5',\n\tYfr: '\\uD835\\uDD1C',\n\tyfr: '\\uD835\\uDD36',\n\tYIcy: '\\u0407',\n\tyicy: '\\u0457',\n\tYopf: '\\uD835\\uDD50',\n\tyopf: '\\uD835\\uDD6A',\n\tYscr: '\\uD835\\uDCB4',\n\tyscr: '\\uD835\\uDCCE',\n\tYUcy: '\\u042E',\n\tyucy: '\\u044E',\n\tYuml: '\\u0178',\n\tyuml: '\\u00FF',\n\tZacute: '\\u0179',\n\tzacute: '\\u017A',\n\tZcaron: '\\u017D',\n\tzcaron: '\\u017E',\n\tZcy: '\\u0417',\n\tzcy: '\\u0437',\n\tZdot: '\\u017B',\n\tzdot: '\\u017C',\n\tzeetrf: '\\u2128',\n\tZeroWidthSpace: '\\u200B',\n\tZeta: '\\u0396',\n\tzeta: '\\u03B6',\n\tZfr: '\\u2128',\n\tzfr: '\\uD835\\uDD37',\n\tZHcy: '\\u0416',\n\tzhcy: '\\u0436',\n\tzigrarr: '\\u21DD',\n\tZopf: '\\u2124',\n\tzopf: '\\uD835\\uDD6B',\n\tZscr: '\\uD835\\uDCB5',\n\tzscr: '\\uD835\\uDCCF',\n\tzwj: '\\u200D',\n\tzwnj: '\\u200C',\n});\n\n/**\n * @deprecated use `HTML_ENTITIES` instead\n * @see HTML_ENTITIES\n */\nexports.entityMap = exports.HTML_ENTITIES;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@xmldom/xmldom/lib/entities.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@xmldom/xmldom/lib/index.js":
/*!**************************************************!*\
  !*** ./node_modules/@xmldom/xmldom/lib/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var dom = __webpack_require__(/*! ./dom */ \"(ssr)/./node_modules/@xmldom/xmldom/lib/dom.js\")\nexports.DOMImplementation = dom.DOMImplementation\nexports.XMLSerializer = dom.XMLSerializer\nexports.DOMParser = __webpack_require__(/*! ./dom-parser */ \"(ssr)/./node_modules/@xmldom/xmldom/lib/dom-parser.js\").DOMParser\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHhtbGRvbS94bWxkb20vbGliL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFBLFVBQVUsbUJBQU8sQ0FBQyw2REFBTztBQUN6Qix5QkFBeUI7QUFDekIscUJBQXFCO0FBQ3JCLDhIQUFxRCIsInNvdXJjZXMiOlsiL1VzZXJzL3l1c3VmL0Rlc2t0b3AvcG9ydGZvbGlvL25vZGVfbW9kdWxlcy9AeG1sZG9tL3htbGRvbS9saWIvaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIGRvbSA9IHJlcXVpcmUoJy4vZG9tJylcbmV4cG9ydHMuRE9NSW1wbGVtZW50YXRpb24gPSBkb20uRE9NSW1wbGVtZW50YXRpb25cbmV4cG9ydHMuWE1MU2VyaWFsaXplciA9IGRvbS5YTUxTZXJpYWxpemVyXG5leHBvcnRzLkRPTVBhcnNlciA9IHJlcXVpcmUoJy4vZG9tLXBhcnNlcicpLkRPTVBhcnNlclxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@xmldom/xmldom/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@xmldom/xmldom/lib/sax.js":
/*!************************************************!*\
  !*** ./node_modules/@xmldom/xmldom/lib/sax.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var NAMESPACE = (__webpack_require__(/*! ./conventions */ \"(ssr)/./node_modules/@xmldom/xmldom/lib/conventions.js\").NAMESPACE);\n\n//[4]   \tNameStartChar\t   ::=   \t\":\" | [A-Z] | \"_\" | [a-z] | [#xC0-#xD6] | [#xD8-#xF6] | [#xF8-#x2FF] | [#x370-#x37D] | [#x37F-#x1FFF] | [#x200C-#x200D] | [#x2070-#x218F] | [#x2C00-#x2FEF] | [#x3001-#xD7FF] | [#xF900-#xFDCF] | [#xFDF0-#xFFFD] | [#x10000-#xEFFFF]\n//[4a]   \tNameChar\t   ::=   \tNameStartChar | \"-\" | \".\" | [0-9] | #xB7 | [#x0300-#x036F] | [#x203F-#x2040]\n//[5]   \tName\t   ::=   \tNameStartChar (NameChar)*\nvar nameStartChar = /[A-Z_a-z\\xC0-\\xD6\\xD8-\\xF6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD]///\\u10000-\\uEFFFF\nvar nameChar = new RegExp(\"[\\\\-\\\\.0-9\"+nameStartChar.source.slice(1,-1)+\"\\\\u00B7\\\\u0300-\\\\u036F\\\\u203F-\\\\u2040]\");\nvar tagNamePattern = new RegExp('^'+nameStartChar.source+nameChar.source+'*(?:\\:'+nameStartChar.source+nameChar.source+'*)?$');\n//var tagNamePattern = /^[a-zA-Z_][\\w\\-\\.]*(?:\\:[a-zA-Z_][\\w\\-\\.]*)?$/\n//var handlers = 'resolveEntity,getExternalSubset,characters,endDocument,endElement,endPrefixMapping,ignorableWhitespace,processingInstruction,setDocumentLocator,skippedEntity,startDocument,startElement,startPrefixMapping,notationDecl,unparsedEntityDecl,error,fatalError,warning,attributeDecl,elementDecl,externalEntityDecl,internalEntityDecl,comment,endCDATA,endDTD,endEntity,startCDATA,startDTD,startEntity'.split(',')\n\n//S_TAG,\tS_ATTR,\tS_EQ,\tS_ATTR_NOQUOT_VALUE\n//S_ATTR_SPACE,\tS_ATTR_END,\tS_TAG_SPACE, S_TAG_CLOSE\nvar S_TAG = 0;//tag name offerring\nvar S_ATTR = 1;//attr name offerring\nvar S_ATTR_SPACE=2;//attr name end and space offer\nvar S_EQ = 3;//=space?\nvar S_ATTR_NOQUOT_VALUE = 4;//attr value(no quot value only)\nvar S_ATTR_END = 5;//attr value end and no space(quot end)\nvar S_TAG_SPACE = 6;//(attr value end || tag end ) && (space offer)\nvar S_TAG_CLOSE = 7;//closed el<el />\n\n/**\n * Creates an error that will not be caught by XMLReader aka the SAX parser.\n *\n * @param {string} message\n * @param {any?} locator Optional, can provide details about the location in the source\n * @constructor\n */\nfunction ParseError(message, locator) {\n\tthis.message = message\n\tthis.locator = locator\n\tif(Error.captureStackTrace) Error.captureStackTrace(this, ParseError);\n}\nParseError.prototype = new Error();\nParseError.prototype.name = ParseError.name\n\nfunction XMLReader(){\n\n}\n\nXMLReader.prototype = {\n\tparse:function(source,defaultNSMap,entityMap){\n\t\tvar domBuilder = this.domBuilder;\n\t\tdomBuilder.startDocument();\n\t\t_copy(defaultNSMap ,defaultNSMap = {})\n\t\tparse(source,defaultNSMap,entityMap,\n\t\t\t\tdomBuilder,this.errorHandler);\n\t\tdomBuilder.endDocument();\n\t}\n}\nfunction parse(source,defaultNSMapCopy,entityMap,domBuilder,errorHandler){\n\tfunction fixedFromCharCode(code) {\n\t\t// String.prototype.fromCharCode does not supports\n\t\t// > 2 bytes unicode chars directly\n\t\tif (code > 0xffff) {\n\t\t\tcode -= 0x10000;\n\t\t\tvar surrogate1 = 0xd800 + (code >> 10)\n\t\t\t\t, surrogate2 = 0xdc00 + (code & 0x3ff);\n\n\t\t\treturn String.fromCharCode(surrogate1, surrogate2);\n\t\t} else {\n\t\t\treturn String.fromCharCode(code);\n\t\t}\n\t}\n\tfunction entityReplacer(a){\n\t\tvar k = a.slice(1,-1);\n\t\tif (Object.hasOwnProperty.call(entityMap, k)) {\n\t\t\treturn entityMap[k];\n\t\t}else if(k.charAt(0) === '#'){\n\t\t\treturn fixedFromCharCode(parseInt(k.substr(1).replace('x','0x')))\n\t\t}else{\n\t\t\terrorHandler.error('entity not found:'+a);\n\t\t\treturn a;\n\t\t}\n\t}\n\tfunction appendText(end){//has some bugs\n\t\tif(end>start){\n\t\t\tvar xt = source.substring(start,end).replace(/&#?\\w+;/g,entityReplacer);\n\t\t\tlocator&&position(start);\n\t\t\tdomBuilder.characters(xt,0,end-start);\n\t\t\tstart = end\n\t\t}\n\t}\n\tfunction position(p,m){\n\t\twhile(p>=lineEnd && (m = linePattern.exec(source))){\n\t\t\tlineStart = m.index;\n\t\t\tlineEnd = lineStart + m[0].length;\n\t\t\tlocator.lineNumber++;\n\t\t\t//console.log('line++:',locator,startPos,endPos)\n\t\t}\n\t\tlocator.columnNumber = p-lineStart+1;\n\t}\n\tvar lineStart = 0;\n\tvar lineEnd = 0;\n\tvar linePattern = /.*(?:\\r\\n?|\\n)|.*$/g\n\tvar locator = domBuilder.locator;\n\n\tvar parseStack = [{currentNSMap:defaultNSMapCopy}]\n\tvar closeMap = {};\n\tvar start = 0;\n\twhile(true){\n\t\ttry{\n\t\t\tvar tagStart = source.indexOf('<',start);\n\t\t\tif(tagStart<0){\n\t\t\t\tif(!source.substr(start).match(/^\\s*$/)){\n\t\t\t\t\tvar doc = domBuilder.doc;\n\t    \t\t\tvar text = doc.createTextNode(source.substr(start));\n\t    \t\t\tdoc.appendChild(text);\n\t    \t\t\tdomBuilder.currentElement = text;\n\t\t\t\t}\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif(tagStart>start){\n\t\t\t\tappendText(tagStart);\n\t\t\t}\n\t\t\tswitch(source.charAt(tagStart+1)){\n\t\t\tcase '/':\n\t\t\t\tvar end = source.indexOf('>',tagStart+3);\n\t\t\t\tvar tagName = source.substring(tagStart + 2, end).replace(/[ \\t\\n\\r]+$/g, '');\n\t\t\t\tvar config = parseStack.pop();\n\t\t\t\tif(end<0){\n\n\t        \t\ttagName = source.substring(tagStart+2).replace(/[\\s<].*/,'');\n\t        \t\terrorHandler.error(\"end tag name: \"+tagName+' is not complete:'+config.tagName);\n\t        \t\tend = tagStart+1+tagName.length;\n\t        \t}else if(tagName.match(/\\s</)){\n\t        \t\ttagName = tagName.replace(/[\\s<].*/,'');\n\t        \t\terrorHandler.error(\"end tag name: \"+tagName+' maybe not complete');\n\t        \t\tend = tagStart+1+tagName.length;\n\t\t\t\t}\n\t\t\t\tvar localNSMap = config.localNSMap;\n\t\t\t\tvar endMatch = config.tagName == tagName;\n\t\t\t\tvar endIgnoreCaseMach = endMatch || config.tagName&&config.tagName.toLowerCase() == tagName.toLowerCase()\n\t\t        if(endIgnoreCaseMach){\n\t\t        \tdomBuilder.endElement(config.uri,config.localName,tagName);\n\t\t\t\t\tif(localNSMap){\n\t\t\t\t\t\tfor (var prefix in localNSMap) {\n\t\t\t\t\t\t\tif (Object.prototype.hasOwnProperty.call(localNSMap, prefix)) {\n\t\t\t\t\t\t\t\tdomBuilder.endPrefixMapping(prefix);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif(!endMatch){\n\t\t            \terrorHandler.fatalError(\"end tag name: \"+tagName+' is not match the current start tagName:'+config.tagName ); // No known test case\n\t\t\t\t\t}\n\t\t        }else{\n\t\t        \tparseStack.push(config)\n\t\t        }\n\n\t\t\t\tend++;\n\t\t\t\tbreak;\n\t\t\t\t// end elment\n\t\t\tcase '?':// <?...?>\n\t\t\t\tlocator&&position(tagStart);\n\t\t\t\tend = parseInstruction(source,tagStart,domBuilder);\n\t\t\t\tbreak;\n\t\t\tcase '!':// <!doctype,<![CDATA,<!--\n\t\t\t\tlocator&&position(tagStart);\n\t\t\t\tend = parseDCC(source,tagStart,domBuilder,errorHandler);\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tlocator&&position(tagStart);\n\t\t\t\tvar el = new ElementAttributes();\n\t\t\t\tvar currentNSMap = parseStack[parseStack.length-1].currentNSMap;\n\t\t\t\t//elStartEnd\n\t\t\t\tvar end = parseElementStartPart(source,tagStart,el,currentNSMap,entityReplacer,errorHandler);\n\t\t\t\tvar len = el.length;\n\n\n\t\t\t\tif(!el.closed && fixSelfClosed(source,end,el.tagName,closeMap)){\n\t\t\t\t\tel.closed = true;\n\t\t\t\t\tif(!entityMap.nbsp){\n\t\t\t\t\t\terrorHandler.warning('unclosed xml attribute');\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif(locator && len){\n\t\t\t\t\tvar locator2 = copyLocator(locator,{});\n\t\t\t\t\t//try{//attribute position fixed\n\t\t\t\t\tfor(var i = 0;i<len;i++){\n\t\t\t\t\t\tvar a = el[i];\n\t\t\t\t\t\tposition(a.offset);\n\t\t\t\t\t\ta.locator = copyLocator(locator,{});\n\t\t\t\t\t}\n\t\t\t\t\tdomBuilder.locator = locator2\n\t\t\t\t\tif(appendElement(el,domBuilder,currentNSMap)){\n\t\t\t\t\t\tparseStack.push(el)\n\t\t\t\t\t}\n\t\t\t\t\tdomBuilder.locator = locator;\n\t\t\t\t}else{\n\t\t\t\t\tif(appendElement(el,domBuilder,currentNSMap)){\n\t\t\t\t\t\tparseStack.push(el)\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (NAMESPACE.isHTML(el.uri) && !el.closed) {\n\t\t\t\t\tend = parseHtmlSpecialContent(source,end,el.tagName,entityReplacer,domBuilder)\n\t\t\t\t} else {\n\t\t\t\t\tend++;\n\t\t\t\t}\n\t\t\t}\n\t\t}catch(e){\n\t\t\tif (e instanceof ParseError) {\n\t\t\t\tthrow e;\n\t\t\t}\n\t\t\terrorHandler.error('element parse error: '+e)\n\t\t\tend = -1;\n\t\t}\n\t\tif(end>start){\n\t\t\tstart = end;\n\t\t}else{\n\t\t\t//TODO: 这里有可能sax回退，有位置错误风险\n\t\t\tappendText(Math.max(tagStart,start)+1);\n\t\t}\n\t}\n}\nfunction copyLocator(f,t){\n\tt.lineNumber = f.lineNumber;\n\tt.columnNumber = f.columnNumber;\n\treturn t;\n}\n\n/**\n * @see #appendElement(source,elStartEnd,el,selfClosed,entityReplacer,domBuilder,parseStack);\n * @return end of the elementStartPart(end of elementEndPart for selfClosed el)\n */\nfunction parseElementStartPart(source,start,el,currentNSMap,entityReplacer,errorHandler){\n\n\t/**\n\t * @param {string} qname\n\t * @param {string} value\n\t * @param {number} startIndex\n\t */\n\tfunction addAttribute(qname, value, startIndex) {\n\t\tif (el.attributeNames.hasOwnProperty(qname)) {\n\t\t\terrorHandler.fatalError('Attribute ' + qname + ' redefined')\n\t\t}\n\t\tel.addValue(\n\t\t\tqname,\n\t\t\t// @see https://www.w3.org/TR/xml/#AVNormalize\n\t\t\t// since the xmldom sax parser does not \"interpret\" DTD the following is not implemented:\n\t\t\t// - recursive replacement of (DTD) entity references\n\t\t\t// - trimming and collapsing multiple spaces into a single one for attributes that are not of type CDATA\n\t\t\tvalue.replace(/[\\t\\n\\r]/g, ' ').replace(/&#?\\w+;/g, entityReplacer),\n\t\t\tstartIndex\n\t\t)\n\t}\n\tvar attrName;\n\tvar value;\n\tvar p = ++start;\n\tvar s = S_TAG;//status\n\twhile(true){\n\t\tvar c = source.charAt(p);\n\t\tswitch(c){\n\t\tcase '=':\n\t\t\tif(s === S_ATTR){//attrName\n\t\t\t\tattrName = source.slice(start,p);\n\t\t\t\ts = S_EQ;\n\t\t\t}else if(s === S_ATTR_SPACE){\n\t\t\t\ts = S_EQ;\n\t\t\t}else{\n\t\t\t\t//fatalError: equal must after attrName or space after attrName\n\t\t\t\tthrow new Error('attribute equal must after attrName'); // No known test case\n\t\t\t}\n\t\t\tbreak;\n\t\tcase '\\'':\n\t\tcase '\"':\n\t\t\tif(s === S_EQ || s === S_ATTR //|| s == S_ATTR_SPACE\n\t\t\t\t){//equal\n\t\t\t\tif(s === S_ATTR){\n\t\t\t\t\terrorHandler.warning('attribute value must after \"=\"')\n\t\t\t\t\tattrName = source.slice(start,p)\n\t\t\t\t}\n\t\t\t\tstart = p+1;\n\t\t\t\tp = source.indexOf(c,start)\n\t\t\t\tif(p>0){\n\t\t\t\t\tvalue = source.slice(start, p);\n\t\t\t\t\taddAttribute(attrName, value, start-1);\n\t\t\t\t\ts = S_ATTR_END;\n\t\t\t\t}else{\n\t\t\t\t\t//fatalError: no end quot match\n\t\t\t\t\tthrow new Error('attribute value no end \\''+c+'\\' match');\n\t\t\t\t}\n\t\t\t}else if(s == S_ATTR_NOQUOT_VALUE){\n\t\t\t\tvalue = source.slice(start, p);\n\t\t\t\taddAttribute(attrName, value, start);\n\t\t\t\terrorHandler.warning('attribute \"'+attrName+'\" missed start quot('+c+')!!');\n\t\t\t\tstart = p+1;\n\t\t\t\ts = S_ATTR_END\n\t\t\t}else{\n\t\t\t\t//fatalError: no equal before\n\t\t\t\tthrow new Error('attribute value must after \"=\"'); // No known test case\n\t\t\t}\n\t\t\tbreak;\n\t\tcase '/':\n\t\t\tswitch(s){\n\t\t\tcase S_TAG:\n\t\t\t\tel.setTagName(source.slice(start,p));\n\t\t\tcase S_ATTR_END:\n\t\t\tcase S_TAG_SPACE:\n\t\t\tcase S_TAG_CLOSE:\n\t\t\t\ts =S_TAG_CLOSE;\n\t\t\t\tel.closed = true;\n\t\t\tcase S_ATTR_NOQUOT_VALUE:\n\t\t\tcase S_ATTR:\n\t\t\t\tbreak;\n\t\t\t\tcase S_ATTR_SPACE:\n\t\t\t\t\tel.closed = true;\n\t\t\t\tbreak;\n\t\t\t//case S_EQ:\n\t\t\tdefault:\n\t\t\t\tthrow new Error(\"attribute invalid close char('/')\") // No known test case\n\t\t\t}\n\t\t\tbreak;\n\t\tcase ''://end document\n\t\t\terrorHandler.error('unexpected end of input');\n\t\t\tif(s == S_TAG){\n\t\t\t\tel.setTagName(source.slice(start,p));\n\t\t\t}\n\t\t\treturn p;\n\t\tcase '>':\n\t\t\tswitch(s){\n\t\t\tcase S_TAG:\n\t\t\t\tel.setTagName(source.slice(start,p));\n\t\t\tcase S_ATTR_END:\n\t\t\tcase S_TAG_SPACE:\n\t\t\tcase S_TAG_CLOSE:\n\t\t\t\tbreak;//normal\n\t\t\tcase S_ATTR_NOQUOT_VALUE://Compatible state\n\t\t\tcase S_ATTR:\n\t\t\t\tvalue = source.slice(start,p);\n\t\t\t\tif(value.slice(-1) === '/'){\n\t\t\t\t\tel.closed  = true;\n\t\t\t\t\tvalue = value.slice(0,-1)\n\t\t\t\t}\n\t\t\tcase S_ATTR_SPACE:\n\t\t\t\tif(s === S_ATTR_SPACE){\n\t\t\t\t\tvalue = attrName;\n\t\t\t\t}\n\t\t\t\tif(s == S_ATTR_NOQUOT_VALUE){\n\t\t\t\t\terrorHandler.warning('attribute \"'+value+'\" missed quot(\")!');\n\t\t\t\t\taddAttribute(attrName, value, start)\n\t\t\t\t}else{\n\t\t\t\t\tif(!NAMESPACE.isHTML(currentNSMap['']) || !value.match(/^(?:disabled|checked|selected)$/i)){\n\t\t\t\t\t\terrorHandler.warning('attribute \"'+value+'\" missed value!! \"'+value+'\" instead!!')\n\t\t\t\t\t}\n\t\t\t\t\taddAttribute(value, value, start)\n\t\t\t\t}\n\t\t\t\tbreak;\n\t\t\tcase S_EQ:\n\t\t\t\tthrow new Error('attribute value missed!!');\n\t\t\t}\n//\t\t\tconsole.log(tagName,tagNamePattern,tagNamePattern.test(tagName))\n\t\t\treturn p;\n\t\t/*xml space '\\x20' | #x9 | #xD | #xA; */\n\t\tcase '\\u0080':\n\t\t\tc = ' ';\n\t\tdefault:\n\t\t\tif(c<= ' '){//space\n\t\t\t\tswitch(s){\n\t\t\t\tcase S_TAG:\n\t\t\t\t\tel.setTagName(source.slice(start,p));//tagName\n\t\t\t\t\ts = S_TAG_SPACE;\n\t\t\t\t\tbreak;\n\t\t\t\tcase S_ATTR:\n\t\t\t\t\tattrName = source.slice(start,p)\n\t\t\t\t\ts = S_ATTR_SPACE;\n\t\t\t\t\tbreak;\n\t\t\t\tcase S_ATTR_NOQUOT_VALUE:\n\t\t\t\t\tvar value = source.slice(start, p);\n\t\t\t\t\terrorHandler.warning('attribute \"'+value+'\" missed quot(\")!!');\n\t\t\t\t\taddAttribute(attrName, value, start)\n\t\t\t\tcase S_ATTR_END:\n\t\t\t\t\ts = S_TAG_SPACE;\n\t\t\t\t\tbreak;\n\t\t\t\t//case S_TAG_SPACE:\n\t\t\t\t//case S_EQ:\n\t\t\t\t//case S_ATTR_SPACE:\n\t\t\t\t//\tvoid();break;\n\t\t\t\t//case S_TAG_CLOSE:\n\t\t\t\t\t//ignore warning\n\t\t\t\t}\n\t\t\t}else{//not space\n//S_TAG,\tS_ATTR,\tS_EQ,\tS_ATTR_NOQUOT_VALUE\n//S_ATTR_SPACE,\tS_ATTR_END,\tS_TAG_SPACE, S_TAG_CLOSE\n\t\t\t\tswitch(s){\n\t\t\t\t//case S_TAG:void();break;\n\t\t\t\t//case S_ATTR:void();break;\n\t\t\t\t//case S_ATTR_NOQUOT_VALUE:void();break;\n\t\t\t\tcase S_ATTR_SPACE:\n\t\t\t\t\tvar tagName =  el.tagName;\n\t\t\t\t\tif (!NAMESPACE.isHTML(currentNSMap['']) || !attrName.match(/^(?:disabled|checked|selected)$/i)) {\n\t\t\t\t\t\terrorHandler.warning('attribute \"'+attrName+'\" missed value!! \"'+attrName+'\" instead2!!')\n\t\t\t\t\t}\n\t\t\t\t\taddAttribute(attrName, attrName, start);\n\t\t\t\t\tstart = p;\n\t\t\t\t\ts = S_ATTR;\n\t\t\t\t\tbreak;\n\t\t\t\tcase S_ATTR_END:\n\t\t\t\t\terrorHandler.warning('attribute space is required\"'+attrName+'\"!!')\n\t\t\t\tcase S_TAG_SPACE:\n\t\t\t\t\ts = S_ATTR;\n\t\t\t\t\tstart = p;\n\t\t\t\t\tbreak;\n\t\t\t\tcase S_EQ:\n\t\t\t\t\ts = S_ATTR_NOQUOT_VALUE;\n\t\t\t\t\tstart = p;\n\t\t\t\t\tbreak;\n\t\t\t\tcase S_TAG_CLOSE:\n\t\t\t\t\tthrow new Error(\"elements closed character '/' and '>' must be connected to\");\n\t\t\t\t}\n\t\t\t}\n\t\t}//end outer switch\n\t\t//console.log('p++',p)\n\t\tp++;\n\t}\n}\n/**\n * @return true if has new namespace define\n */\nfunction appendElement(el,domBuilder,currentNSMap){\n\tvar tagName = el.tagName;\n\tvar localNSMap = null;\n\t//var currentNSMap = parseStack[parseStack.length-1].currentNSMap;\n\tvar i = el.length;\n\twhile(i--){\n\t\tvar a = el[i];\n\t\tvar qName = a.qName;\n\t\tvar value = a.value;\n\t\tvar nsp = qName.indexOf(':');\n\t\tif(nsp>0){\n\t\t\tvar prefix = a.prefix = qName.slice(0,nsp);\n\t\t\tvar localName = qName.slice(nsp+1);\n\t\t\tvar nsPrefix = prefix === 'xmlns' && localName\n\t\t}else{\n\t\t\tlocalName = qName;\n\t\t\tprefix = null\n\t\t\tnsPrefix = qName === 'xmlns' && ''\n\t\t}\n\t\t//can not set prefix,because prefix !== ''\n\t\ta.localName = localName ;\n\t\t//prefix == null for no ns prefix attribute\n\t\tif(nsPrefix !== false){//hack!!\n\t\t\tif(localNSMap == null){\n\t\t\t\tlocalNSMap = {}\n\t\t\t\t//console.log(currentNSMap,0)\n\t\t\t\t_copy(currentNSMap,currentNSMap={})\n\t\t\t\t//console.log(currentNSMap,1)\n\t\t\t}\n\t\t\tcurrentNSMap[nsPrefix] = localNSMap[nsPrefix] = value;\n\t\t\ta.uri = NAMESPACE.XMLNS\n\t\t\tdomBuilder.startPrefixMapping(nsPrefix, value)\n\t\t}\n\t}\n\tvar i = el.length;\n\twhile(i--){\n\t\ta = el[i];\n\t\tvar prefix = a.prefix;\n\t\tif(prefix){//no prefix attribute has no namespace\n\t\t\tif(prefix === 'xml'){\n\t\t\t\ta.uri = NAMESPACE.XML;\n\t\t\t}if(prefix !== 'xmlns'){\n\t\t\t\ta.uri = currentNSMap[prefix || '']\n\n\t\t\t\t//{console.log('###'+a.qName,domBuilder.locator.systemId+'',currentNSMap,a.uri)}\n\t\t\t}\n\t\t}\n\t}\n\tvar nsp = tagName.indexOf(':');\n\tif(nsp>0){\n\t\tprefix = el.prefix = tagName.slice(0,nsp);\n\t\tlocalName = el.localName = tagName.slice(nsp+1);\n\t}else{\n\t\tprefix = null;//important!!\n\t\tlocalName = el.localName = tagName;\n\t}\n\t//no prefix element has default namespace\n\tvar ns = el.uri = currentNSMap[prefix || ''];\n\tdomBuilder.startElement(ns,localName,tagName,el);\n\t//endPrefixMapping and startPrefixMapping have not any help for dom builder\n\t//localNSMap = null\n\tif(el.closed){\n\t\tdomBuilder.endElement(ns,localName,tagName);\n\t\tif(localNSMap){\n\t\t\tfor (prefix in localNSMap) {\n\t\t\t\tif (Object.prototype.hasOwnProperty.call(localNSMap, prefix)) {\n\t\t\t\t\tdomBuilder.endPrefixMapping(prefix);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}else{\n\t\tel.currentNSMap = currentNSMap;\n\t\tel.localNSMap = localNSMap;\n\t\t//parseStack.push(el);\n\t\treturn true;\n\t}\n}\nfunction parseHtmlSpecialContent(source,elStartEnd,tagName,entityReplacer,domBuilder){\n\tif(/^(?:script|textarea)$/i.test(tagName)){\n\t\tvar elEndStart =  source.indexOf('</'+tagName+'>',elStartEnd);\n\t\tvar text = source.substring(elStartEnd+1,elEndStart);\n\t\tif(/[&<]/.test(text)){\n\t\t\tif(/^script$/i.test(tagName)){\n\t\t\t\t//if(!/\\]\\]>/.test(text)){\n\t\t\t\t\t//lexHandler.startCDATA();\n\t\t\t\t\tdomBuilder.characters(text,0,text.length);\n\t\t\t\t\t//lexHandler.endCDATA();\n\t\t\t\t\treturn elEndStart;\n\t\t\t\t//}\n\t\t\t}//}else{//text area\n\t\t\t\ttext = text.replace(/&#?\\w+;/g,entityReplacer);\n\t\t\t\tdomBuilder.characters(text,0,text.length);\n\t\t\t\treturn elEndStart;\n\t\t\t//}\n\n\t\t}\n\t}\n\treturn elStartEnd+1;\n}\nfunction fixSelfClosed(source,elStartEnd,tagName,closeMap){\n\t//if(tagName in closeMap){\n\tvar pos = closeMap[tagName];\n\tif(pos == null){\n\t\t//console.log(tagName)\n\t\tpos =  source.lastIndexOf('</'+tagName+'>')\n\t\tif(pos<elStartEnd){//忘记闭合\n\t\t\tpos = source.lastIndexOf('</'+tagName)\n\t\t}\n\t\tcloseMap[tagName] =pos\n\t}\n\treturn pos<elStartEnd;\n\t//}\n}\n\nfunction _copy (source, target) {\n\tfor (var n in source) {\n\t\tif (Object.prototype.hasOwnProperty.call(source, n)) {\n\t\t\ttarget[n] = source[n];\n\t\t}\n\t}\n}\n\nfunction parseDCC(source,start,domBuilder,errorHandler){//sure start with '<!'\n\tvar next= source.charAt(start+2)\n\tswitch(next){\n\tcase '-':\n\t\tif(source.charAt(start + 3) === '-'){\n\t\t\tvar end = source.indexOf('-->',start+4);\n\t\t\t//append comment source.substring(4,end)//<!--\n\t\t\tif(end>start){\n\t\t\t\tdomBuilder.comment(source,start+4,end-start-4);\n\t\t\t\treturn end+3;\n\t\t\t}else{\n\t\t\t\terrorHandler.error(\"Unclosed comment\");\n\t\t\t\treturn -1;\n\t\t\t}\n\t\t}else{\n\t\t\t//error\n\t\t\treturn -1;\n\t\t}\n\tdefault:\n\t\tif(source.substr(start+3,6) == 'CDATA['){\n\t\t\tvar end = source.indexOf(']]>',start+9);\n\t\t\tdomBuilder.startCDATA();\n\t\t\tdomBuilder.characters(source,start+9,end-start-9);\n\t\t\tdomBuilder.endCDATA()\n\t\t\treturn end+3;\n\t\t}\n\t\t//<!DOCTYPE\n\t\t//startDTD(java.lang.String name, java.lang.String publicId, java.lang.String systemId)\n\t\tvar matchs = split(source,start);\n\t\tvar len = matchs.length;\n\t\tif(len>1 && /!doctype/i.test(matchs[0][0])){\n\t\t\tvar name = matchs[1][0];\n\t\t\tvar pubid = false;\n\t\t\tvar sysid = false;\n\t\t\tif(len>3){\n\t\t\t\tif(/^public$/i.test(matchs[2][0])){\n\t\t\t\t\tpubid = matchs[3][0];\n\t\t\t\t\tsysid = len>4 && matchs[4][0];\n\t\t\t\t}else if(/^system$/i.test(matchs[2][0])){\n\t\t\t\t\tsysid = matchs[3][0];\n\t\t\t\t}\n\t\t\t}\n\t\t\tvar lastMatch = matchs[len-1]\n\t\t\tdomBuilder.startDTD(name, pubid, sysid);\n\t\t\tdomBuilder.endDTD();\n\n\t\t\treturn lastMatch.index+lastMatch[0].length\n\t\t}\n\t}\n\treturn -1;\n}\n\n\n\nfunction parseInstruction(source,start,domBuilder){\n\tvar end = source.indexOf('?>',start);\n\tif(end){\n\t\tvar match = source.substring(start,end).match(/^<\\?(\\S*)\\s*([\\s\\S]*?)\\s*$/);\n\t\tif(match){\n\t\t\tvar len = match[0].length;\n\t\t\tdomBuilder.processingInstruction(match[1], match[2]) ;\n\t\t\treturn end+2;\n\t\t}else{//error\n\t\t\treturn -1;\n\t\t}\n\t}\n\treturn -1;\n}\n\nfunction ElementAttributes(){\n\tthis.attributeNames = {}\n}\nElementAttributes.prototype = {\n\tsetTagName:function(tagName){\n\t\tif(!tagNamePattern.test(tagName)){\n\t\t\tthrow new Error('invalid tagName:'+tagName)\n\t\t}\n\t\tthis.tagName = tagName\n\t},\n\taddValue:function(qName, value, offset) {\n\t\tif(!tagNamePattern.test(qName)){\n\t\t\tthrow new Error('invalid attribute:'+qName)\n\t\t}\n\t\tthis.attributeNames[qName] = this.length;\n\t\tthis[this.length++] = {qName:qName,value:value,offset:offset}\n\t},\n\tlength:0,\n\tgetLocalName:function(i){return this[i].localName},\n\tgetLocator:function(i){return this[i].locator},\n\tgetQName:function(i){return this[i].qName},\n\tgetURI:function(i){return this[i].uri},\n\tgetValue:function(i){return this[i].value}\n//\t,getIndex:function(uri, localName)){\n//\t\tif(localName){\n//\n//\t\t}else{\n//\t\t\tvar qName = uri\n//\t\t}\n//\t},\n//\tgetValue:function(){return this.getValue(this.getIndex.apply(this,arguments))},\n//\tgetType:function(uri,localName){}\n//\tgetType:function(i){},\n}\n\n\n\nfunction split(source,start){\n\tvar match;\n\tvar buf = [];\n\tvar reg = /'[^']+'|\"[^\"]+\"|[^\\s<>\\/=]+=?|(\\/?\\s*>|<)/g;\n\treg.lastIndex = start;\n\treg.exec(source);//skip <\n\twhile(match = reg.exec(source)){\n\t\tbuf.push(match);\n\t\tif(match[1])return buf;\n\t}\n}\n\nexports.XMLReader = XMLReader;\nexports.ParseError = ParseError;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@xmldom/xmldom/lib/sax.js\n");

/***/ })

};
;