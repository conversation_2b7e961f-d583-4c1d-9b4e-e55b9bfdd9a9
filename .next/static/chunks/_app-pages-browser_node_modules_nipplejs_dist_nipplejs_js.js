/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_nipplejs_dist_nipplejs_js"],{

/***/ "(app-pages-browser)/./node_modules/nipplejs/dist/nipplejs.js":
/*!************************************************!*\
  !*** ./node_modules/nipplejs/dist/nipplejs.js ***!
  \************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("!function(t,i){ true?module.exports=i():0}(window,function(){return function(t){var i={};function e(o){if(i[o])return i[o].exports;var n=i[o]={i:o,l:!1,exports:{}};return t[o].call(n.exports,n,n.exports,e),n.l=!0,n.exports}return e.m=t,e.c=i,e.d=function(t,i,o){e.o(t,i)||Object.defineProperty(t,i,{enumerable:!0,get:o})},e.r=function(t){\"undefined\"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:\"Module\"}),Object.defineProperty(t,\"__esModule\",{value:!0})},e.t=function(t,i){if(1&i&&(t=e(t)),8&i)return t;if(4&i&&\"object\"==typeof t&&t&&t.__esModule)return t;var o=Object.create(null);if(e.r(o),Object.defineProperty(o,\"default\",{enumerable:!0,value:t}),2&i&&\"string\"!=typeof t)for(var n in t)e.d(o,n,function(i){return t[i]}.bind(null,n));return o},e.n=function(t){var i=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(i,\"a\",i),i},e.o=function(t,i){return Object.prototype.hasOwnProperty.call(t,i)},e.p=\"\",e(e.s=0)}([function(t,i,e){\"use strict\";e.r(i);var o,n=function(t,i){var e=i.x-t.x,o=i.y-t.y;return Math.sqrt(e*e+o*o)},s=function(t){return t*(Math.PI/180)},r=function(t){return t*(180/Math.PI)},d=new Map,a=function(t){d.has(t)&&clearTimeout(d.get(t)),d.set(t,setTimeout(t,100))},p=function(t,i,e){for(var o,n=i.split(/[ ,]+/g),s=0;s<n.length;s+=1)o=n[s],t.addEventListener?t.addEventListener(o,e,!1):t.attachEvent&&t.attachEvent(o,e)},c=function(t,i,e){for(var o,n=i.split(/[ ,]+/g),s=0;s<n.length;s+=1)o=n[s],t.removeEventListener?t.removeEventListener(o,e):t.detachEvent&&t.detachEvent(o,e)},l=function(t){return t.preventDefault(),t.type.match(/^touch/)?t.changedTouches:t},h=function(){return{x:void 0!==window.pageXOffset?window.pageXOffset:(document.documentElement||document.body.parentNode||document.body).scrollLeft,y:void 0!==window.pageYOffset?window.pageYOffset:(document.documentElement||document.body.parentNode||document.body).scrollTop}},u=function(t,i){i.top||i.right||i.bottom||i.left?(t.style.top=i.top,t.style.right=i.right,t.style.bottom=i.bottom,t.style.left=i.left):(t.style.left=i.x+\"px\",t.style.top=i.y+\"px\")},f=function(t,i,e){var o=y(t);for(var n in o)if(o.hasOwnProperty(n))if(\"string\"==typeof i)o[n]=i+\" \"+e;else{for(var s=\"\",r=0,d=i.length;r<d;r+=1)s+=i[r]+\" \"+e+\", \";o[n]=s.slice(0,-2)}return o},y=function(t){var i={};i[t]=\"\";return[\"webkit\",\"Moz\",\"o\"].forEach(function(e){i[e+t.charAt(0).toUpperCase()+t.slice(1)]=\"\"}),i},m=function(t,i){for(var e in i)i.hasOwnProperty(e)&&(t[e]=i[e]);return t},v=function(t,i){if(t.length)for(var e=0,o=t.length;e<o;e+=1)i(t[e]);else i(t)},g=!!(\"ontouchstart\"in window),b=!!window.PointerEvent,x=!!window.MSPointerEvent,O={start:\"mousedown\",move:\"mousemove\",end:\"mouseup\"},w={};function _(){}b?o={start:\"pointerdown\",move:\"pointermove\",end:\"pointerup, pointercancel\"}:x?o={start:\"MSPointerDown\",move:\"MSPointerMove\",end:\"MSPointerUp\"}:g?(o={start:\"touchstart\",move:\"touchmove\",end:\"touchend, touchcancel\"},w=O):o=O,_.prototype.on=function(t,i){var e,o=t.split(/[ ,]+/g);this._handlers_=this._handlers_||{};for(var n=0;n<o.length;n+=1)e=o[n],this._handlers_[e]=this._handlers_[e]||[],this._handlers_[e].push(i);return this},_.prototype.off=function(t,i){return this._handlers_=this._handlers_||{},void 0===t?this._handlers_={}:void 0===i?this._handlers_[t]=null:this._handlers_[t]&&this._handlers_[t].indexOf(i)>=0&&this._handlers_[t].splice(this._handlers_[t].indexOf(i),1),this},_.prototype.trigger=function(t,i){var e,o=this,n=t.split(/[ ,]+/g);o._handlers_=o._handlers_||{};for(var s=0;s<n.length;s+=1)e=n[s],o._handlers_[e]&&o._handlers_[e].length&&o._handlers_[e].forEach(function(t){t.call(o,{type:e,target:o},i)})},_.prototype.config=function(t){this.options=this.defaults||{},t&&(this.options=function(t,i){var e={};for(var o in t)t.hasOwnProperty(o)&&i.hasOwnProperty(o)?e[o]=i[o]:t.hasOwnProperty(o)&&(e[o]=t[o]);return e}(this.options,t))},_.prototype.bindEvt=function(t,i){var e=this;return e._domHandlers_=e._domHandlers_||{},e._domHandlers_[i]=function(){\"function\"==typeof e[\"on\"+i]?e[\"on\"+i].apply(e,arguments):console.warn('[WARNING] : Missing \"on'+i+'\" handler.')},p(t,o[i],e._domHandlers_[i]),w[i]&&p(t,w[i],e._domHandlers_[i]),e},_.prototype.unbindEvt=function(t,i){return this._domHandlers_=this._domHandlers_||{},c(t,o[i],this._domHandlers_[i]),w[i]&&c(t,w[i],this._domHandlers_[i]),delete this._domHandlers_[i],this};var T=_;function k(t,i){return this.identifier=i.identifier,this.position=i.position,this.frontPosition=i.frontPosition,this.collection=t,this.defaults={size:100,threshold:.1,color:\"white\",fadeTime:250,dataOnly:!1,restJoystick:!0,restOpacity:.5,mode:\"dynamic\",zone:document.body,lockX:!1,lockY:!1,shape:\"circle\"},this.config(i),\"dynamic\"===this.options.mode&&(this.options.restOpacity=0),this.id=k.id,k.id+=1,this.buildEl().stylize(),this.instance={el:this.ui.el,on:this.on.bind(this),off:this.off.bind(this),show:this.show.bind(this),hide:this.hide.bind(this),add:this.addToDom.bind(this),remove:this.removeFromDom.bind(this),destroy:this.destroy.bind(this),setPosition:this.setPosition.bind(this),resetDirection:this.resetDirection.bind(this),computeDirection:this.computeDirection.bind(this),trigger:this.trigger.bind(this),position:this.position,frontPosition:this.frontPosition,ui:this.ui,identifier:this.identifier,id:this.id,options:this.options},this.instance}k.prototype=new T,k.constructor=k,k.id=0,k.prototype.buildEl=function(t){return this.ui={},this.options.dataOnly?this:(this.ui.el=document.createElement(\"div\"),this.ui.back=document.createElement(\"div\"),this.ui.front=document.createElement(\"div\"),this.ui.el.className=\"nipple collection_\"+this.collection.id,this.ui.back.className=\"back\",this.ui.front.className=\"front\",this.ui.el.setAttribute(\"id\",\"nipple_\"+this.collection.id+\"_\"+this.id),this.ui.el.appendChild(this.ui.back),this.ui.el.appendChild(this.ui.front),this)},k.prototype.stylize=function(){if(this.options.dataOnly)return this;var t=this.options.fadeTime+\"ms\",i=function(t,i){var e=y(t);for(var o in e)e.hasOwnProperty(o)&&(e[o]=i);return e}(\"borderRadius\",\"50%\"),e=f(\"transition\",\"opacity\",t),o={};return o.el={position:\"absolute\",opacity:this.options.restOpacity,display:\"block\",zIndex:999},o.back={position:\"absolute\",display:\"block\",width:this.options.size+\"px\",height:this.options.size+\"px\",left:0,marginLeft:-this.options.size/2+\"px\",marginTop:-this.options.size/2+\"px\",background:this.options.color,opacity:\".5\"},o.front={width:this.options.size/2+\"px\",height:this.options.size/2+\"px\",position:\"absolute\",display:\"block\",left:0,marginLeft:-this.options.size/4+\"px\",marginTop:-this.options.size/4+\"px\",background:this.options.color,opacity:\".5\",transform:\"translate(0px, 0px)\"},m(o.el,e),\"circle\"===this.options.shape&&m(o.back,i),m(o.front,i),this.applyStyles(o),this},k.prototype.applyStyles=function(t){for(var i in this.ui)if(this.ui.hasOwnProperty(i))for(var e in t[i])this.ui[i].style[e]=t[i][e];return this},k.prototype.addToDom=function(){return this.options.dataOnly||document.body.contains(this.ui.el)?this:(this.options.zone.appendChild(this.ui.el),this)},k.prototype.removeFromDom=function(){return this.options.dataOnly||!document.body.contains(this.ui.el)?this:(this.options.zone.removeChild(this.ui.el),this)},k.prototype.destroy=function(){clearTimeout(this.removeTimeout),clearTimeout(this.showTimeout),clearTimeout(this.restTimeout),this.trigger(\"destroyed\",this.instance),this.removeFromDom(),this.off()},k.prototype.show=function(t){var i=this;return i.options.dataOnly?i:(clearTimeout(i.removeTimeout),clearTimeout(i.showTimeout),clearTimeout(i.restTimeout),i.addToDom(),i.restCallback(),setTimeout(function(){i.ui.el.style.opacity=1},0),i.showTimeout=setTimeout(function(){i.trigger(\"shown\",i.instance),\"function\"==typeof t&&t.call(this)},i.options.fadeTime),i)},k.prototype.hide=function(t){var i=this;if(i.options.dataOnly)return i;if(i.ui.el.style.opacity=i.options.restOpacity,clearTimeout(i.removeTimeout),clearTimeout(i.showTimeout),clearTimeout(i.restTimeout),i.removeTimeout=setTimeout(function(){var e=\"dynamic\"===i.options.mode?\"none\":\"block\";i.ui.el.style.display=e,\"function\"==typeof t&&t.call(i),i.trigger(\"hidden\",i.instance)},i.options.fadeTime),i.options.restJoystick){var e=i.options.restJoystick,o={};o.x=!0===e||!1!==e.x?0:i.instance.frontPosition.x,o.y=!0===e||!1!==e.y?0:i.instance.frontPosition.y,i.setPosition(t,o)}return i},k.prototype.setPosition=function(t,i){var e=this;e.frontPosition={x:i.x,y:i.y};var o=e.options.fadeTime+\"ms\",n={};n.front=f(\"transition\",[\"transform\"],o);var s={front:{}};s.front={transform:\"translate(\"+e.frontPosition.x+\"px,\"+e.frontPosition.y+\"px)\"},e.applyStyles(n),e.applyStyles(s),e.restTimeout=setTimeout(function(){\"function\"==typeof t&&t.call(e),e.restCallback()},e.options.fadeTime)},k.prototype.restCallback=function(){var t={};t.front=f(\"transition\",\"none\",\"\"),this.applyStyles(t),this.trigger(\"rested\",this.instance)},k.prototype.resetDirection=function(){this.direction={x:!1,y:!1,angle:!1}},k.prototype.computeDirection=function(t){var i,e,o,n=t.angle.radian,s=Math.PI/4,r=Math.PI/2;if(n>s&&n<3*s&&!t.lockX?i=\"up\":n>-s&&n<=s&&!t.lockY?i=\"left\":n>3*-s&&n<=-s&&!t.lockX?i=\"down\":t.lockY||(i=\"right\"),t.lockY||(e=n>-r&&n<r?\"left\":\"right\"),t.lockX||(o=n>0?\"up\":\"down\"),t.force>this.options.threshold){var d,a={};for(d in this.direction)this.direction.hasOwnProperty(d)&&(a[d]=this.direction[d]);var p={};for(d in this.direction={x:e,y:o,angle:i},t.direction=this.direction,a)a[d]===this.direction[d]&&(p[d]=!0);if(p.x&&p.y&&p.angle)return t;p.x&&p.y||this.trigger(\"plain\",t),p.x||this.trigger(\"plain:\"+e,t),p.y||this.trigger(\"plain:\"+o,t),p.angle||this.trigger(\"dir dir:\"+i,t)}else this.resetDirection();return t};var P=k;function E(t,i){this.nipples=[],this.idles=[],this.actives=[],this.ids=[],this.pressureIntervals={},this.manager=t,this.id=E.id,E.id+=1,this.defaults={zone:document.body,multitouch:!1,maxNumberOfNipples:10,mode:\"dynamic\",position:{top:0,left:0},catchDistance:200,size:100,threshold:.1,color:\"white\",fadeTime:250,dataOnly:!1,restJoystick:!0,restOpacity:.5,lockX:!1,lockY:!1,shape:\"circle\",dynamicPage:!1,follow:!1},this.config(i),\"static\"!==this.options.mode&&\"semi\"!==this.options.mode||(this.options.multitouch=!1),this.options.multitouch||(this.options.maxNumberOfNipples=1);var e=getComputedStyle(this.options.zone.parentElement);return e&&\"flex\"===e.display&&(this.parentIsFlex=!0),this.updateBox(),this.prepareNipples(),this.bindings(),this.begin(),this.nipples}E.prototype=new T,E.constructor=E,E.id=0,E.prototype.prepareNipples=function(){var t=this.nipples;t.on=this.on.bind(this),t.off=this.off.bind(this),t.options=this.options,t.destroy=this.destroy.bind(this),t.ids=this.ids,t.id=this.id,t.processOnMove=this.processOnMove.bind(this),t.processOnEnd=this.processOnEnd.bind(this),t.get=function(i){if(void 0===i)return t[0];for(var e=0,o=t.length;e<o;e+=1)if(t[e].identifier===i)return t[e];return!1}},E.prototype.bindings=function(){this.bindEvt(this.options.zone,\"start\"),this.options.zone.style.touchAction=\"none\",this.options.zone.style.msTouchAction=\"none\"},E.prototype.begin=function(){var t=this.options;if(\"static\"===t.mode){var i=this.createNipple(t.position,this.manager.getIdentifier());i.add(),this.idles.push(i)}},E.prototype.createNipple=function(t,i){var e=this.manager.scroll,o={},n=this.options,s=this.parentIsFlex?e.x:e.x+this.box.left,r=this.parentIsFlex?e.y:e.y+this.box.top;if(t.x&&t.y)o={x:t.x-s,y:t.y-r};else if(t.top||t.right||t.bottom||t.left){var d=document.createElement(\"DIV\");d.style.display=\"hidden\",d.style.top=t.top,d.style.right=t.right,d.style.bottom=t.bottom,d.style.left=t.left,d.style.position=\"absolute\",n.zone.appendChild(d);var a=d.getBoundingClientRect();n.zone.removeChild(d),o=t,t={x:a.left+e.x,y:a.top+e.y}}var p=new P(this,{color:n.color,size:n.size,threshold:n.threshold,fadeTime:n.fadeTime,dataOnly:n.dataOnly,restJoystick:n.restJoystick,restOpacity:n.restOpacity,mode:n.mode,identifier:i,position:t,zone:n.zone,frontPosition:{x:0,y:0},shape:n.shape});return n.dataOnly||(u(p.ui.el,o),u(p.ui.front,p.frontPosition)),this.nipples.push(p),this.trigger(\"added \"+p.identifier+\":added\",p),this.manager.trigger(\"added \"+p.identifier+\":added\",p),this.bindNipple(p),p},E.prototype.updateBox=function(){this.box=this.options.zone.getBoundingClientRect()},E.prototype.bindNipple=function(t){var i,e=this,o=function(t,o){i=t.type+\" \"+o.id+\":\"+t.type,e.trigger(i,o)};t.on(\"destroyed\",e.onDestroyed.bind(e)),t.on(\"shown hidden rested dir plain\",o),t.on(\"dir:up dir:right dir:down dir:left\",o),t.on(\"plain:up plain:right plain:down plain:left\",o)},E.prototype.pressureFn=function(t,i,e){var o=this,n=0;clearInterval(o.pressureIntervals[e]),o.pressureIntervals[e]=setInterval(function(){var e=t.force||t.pressure||t.webkitForce||0;e!==n&&(i.trigger(\"pressure\",e),o.trigger(\"pressure \"+i.identifier+\":pressure\",e),n=e)}.bind(o),100)},E.prototype.onstart=function(t){var i=this,e=i.options,o=t;t=l(t),i.updateBox();return v(t,function(n){i.actives.length<e.maxNumberOfNipples?i.processOnStart(n):o.type.match(/^touch/)&&(Object.keys(i.manager.ids).forEach(function(e){if(Object.values(o.touches).findIndex(function(t){return t.identifier===e})<0){var n=[t[0]];n.identifier=e,i.processOnEnd(n)}}),i.actives.length<e.maxNumberOfNipples&&i.processOnStart(n))}),i.manager.bindDocument(),!1},E.prototype.processOnStart=function(t){var i,e=this,o=e.options,s=e.manager.getIdentifier(t),r=t.force||t.pressure||t.webkitForce||0,d={x:t.pageX,y:t.pageY},a=e.getOrCreate(s,d);a.identifier!==s&&e.manager.removeIdentifier(a.identifier),a.identifier=s;var p=function(i){i.trigger(\"start\",i),e.trigger(\"start \"+i.id+\":start\",i),i.show(),r>0&&e.pressureFn(t,i,i.identifier),e.processOnMove(t)};if((i=e.idles.indexOf(a))>=0&&e.idles.splice(i,1),e.actives.push(a),e.ids.push(a.identifier),\"semi\"!==o.mode)p(a);else{if(!(n(d,a.position)<=o.catchDistance))return a.destroy(),void e.processOnStart(t);p(a)}return a},E.prototype.getOrCreate=function(t,i){var e,o=this.options;return/(semi|static)/.test(o.mode)?(e=this.idles[0])?(this.idles.splice(0,1),e):\"semi\"===o.mode?this.createNipple(i,t):(console.warn(\"Coudln't find the needed nipple.\"),!1):e=this.createNipple(i,t)},E.prototype.processOnMove=function(t){var i=this.options,e=this.manager.getIdentifier(t),o=this.nipples.get(e),d=this.manager.scroll;if(function(t){return isNaN(t.buttons)?0!==t.pressure:0!==t.buttons}(t)){if(!o)return console.error(\"Found zombie joystick with ID \"+e),void this.manager.removeIdentifier(e);if(i.dynamicPage){var a=o.el.getBoundingClientRect();o.position={x:d.x+a.left,y:d.y+a.top}}o.identifier=e;var p=o.options.size/2,c={x:t.pageX,y:t.pageY};i.lockX&&(c.y=o.position.y),i.lockY&&(c.x=o.position.x);var l,h,u,f,y,m,v,g,b,x,O=n(c,o.position),w=(l=c,h=o.position,u=h.x-l.x,f=h.y-l.y,r(Math.atan2(f,u))),_=s(w),T=O/p,k={distance:O,position:c};if(\"circle\"===o.options.shape?(y=Math.min(O,p),v=o.position,g=y,x={x:0,y:0},b=s(b=w),x.x=v.x-g*Math.cos(b),x.y=v.y-g*Math.sin(b),m=x):(m=function(t,i,e){return{x:Math.min(Math.max(t.x,i.x-e),i.x+e),y:Math.min(Math.max(t.y,i.y-e),i.y+e)}}(c,o.position,p),y=n(m,o.position)),i.follow){if(O>p){var P=c.x-m.x,E=c.y-m.y;o.position.x+=P,o.position.y+=E,o.el.style.top=o.position.y-(this.box.top+d.y)+\"px\",o.el.style.left=o.position.x-(this.box.left+d.x)+\"px\",O=n(c,o.position)}}else c=m,O=y;var I=c.x-o.position.x,z=c.y-o.position.y;o.frontPosition={x:I,y:z},i.dataOnly||(o.ui.front.style.transform=\"translate(\"+I+\"px,\"+z+\"px)\");var D={identifier:o.identifier,position:c,force:T,pressure:t.force||t.pressure||t.webkitForce||0,distance:O,angle:{radian:_,degree:w},vector:{x:I/p,y:-z/p},raw:k,instance:o,lockX:i.lockX,lockY:i.lockY};(D=o.computeDirection(D)).angle={radian:s(180-w),degree:180-w},o.trigger(\"move\",D),this.trigger(\"move \"+o.id+\":move\",D)}else this.processOnEnd(t)},E.prototype.processOnEnd=function(t){var i=this,e=i.options,o=i.manager.getIdentifier(t),n=i.nipples.get(o),s=i.manager.removeIdentifier(n.identifier);n&&(e.dataOnly||n.hide(function(){\"dynamic\"===e.mode&&(n.trigger(\"removed\",n),i.trigger(\"removed \"+n.id+\":removed\",n),i.manager.trigger(\"removed \"+n.id+\":removed\",n),n.destroy())}),clearInterval(i.pressureIntervals[n.identifier]),n.resetDirection(),n.trigger(\"end\",n),i.trigger(\"end \"+n.id+\":end\",n),i.ids.indexOf(n.identifier)>=0&&i.ids.splice(i.ids.indexOf(n.identifier),1),i.actives.indexOf(n)>=0&&i.actives.splice(i.actives.indexOf(n),1),/(semi|static)/.test(e.mode)?i.idles.push(n):i.nipples.indexOf(n)>=0&&i.nipples.splice(i.nipples.indexOf(n),1),i.manager.unbindDocument(),/(semi|static)/.test(e.mode)&&(i.manager.ids[s.id]=s.identifier))},E.prototype.onDestroyed=function(t,i){this.nipples.indexOf(i)>=0&&this.nipples.splice(this.nipples.indexOf(i),1),this.actives.indexOf(i)>=0&&this.actives.splice(this.actives.indexOf(i),1),this.idles.indexOf(i)>=0&&this.idles.splice(this.idles.indexOf(i),1),this.ids.indexOf(i.identifier)>=0&&this.ids.splice(this.ids.indexOf(i.identifier),1),this.manager.removeIdentifier(i.identifier),this.manager.unbindDocument()},E.prototype.destroy=function(){for(var t in this.unbindEvt(this.options.zone,\"start\"),this.nipples.forEach(function(t){t.destroy()}),this.pressureIntervals)this.pressureIntervals.hasOwnProperty(t)&&clearInterval(this.pressureIntervals[t]);this.trigger(\"destroyed\",this.nipples),this.manager.unbindDocument(),this.off()};var I=E;function z(t){var i=this;i.ids={},i.index=0,i.collections=[],i.scroll=h(),i.config(t),i.prepareCollections();var e=function(){var t;i.collections.forEach(function(e){e.forEach(function(e){t=e.el.getBoundingClientRect(),e.position={x:i.scroll.x+t.left,y:i.scroll.y+t.top}})})};p(window,\"resize\",function(){a(e)});var o=function(){i.scroll=h()};return p(window,\"scroll\",function(){a(o)}),i.collections}z.prototype=new T,z.constructor=z,z.prototype.prepareCollections=function(){var t=this;t.collections.create=t.create.bind(t),t.collections.on=t.on.bind(t),t.collections.off=t.off.bind(t),t.collections.destroy=t.destroy.bind(t),t.collections.get=function(i){var e;return t.collections.every(function(t){return!(e=t.get(i))}),e}},z.prototype.create=function(t){return this.createCollection(t)},z.prototype.createCollection=function(t){var i=new I(this,t);return this.bindCollection(i),this.collections.push(i),i},z.prototype.bindCollection=function(t){var i,e=this,o=function(t,o){i=t.type+\" \"+o.id+\":\"+t.type,e.trigger(i,o)};t.on(\"destroyed\",e.onDestroyed.bind(e)),t.on(\"shown hidden rested dir plain\",o),t.on(\"dir:up dir:right dir:down dir:left\",o),t.on(\"plain:up plain:right plain:down plain:left\",o)},z.prototype.bindDocument=function(){this.binded||(this.bindEvt(document,\"move\").bindEvt(document,\"end\"),this.binded=!0)},z.prototype.unbindDocument=function(t){Object.keys(this.ids).length&&!0!==t||(this.unbindEvt(document,\"move\").unbindEvt(document,\"end\"),this.binded=!1)},z.prototype.getIdentifier=function(t){var i;return t?void 0===(i=void 0===t.identifier?t.pointerId:t.identifier)&&(i=this.latest||0):i=this.index,void 0===this.ids[i]&&(this.ids[i]=this.index,this.index+=1),this.latest=i,this.ids[i]},z.prototype.removeIdentifier=function(t){var i={};for(var e in this.ids)if(this.ids[e]===t){i.id=e,i.identifier=this.ids[e],delete this.ids[e];break}return i},z.prototype.onmove=function(t){return this.onAny(\"move\",t),!1},z.prototype.onend=function(t){return this.onAny(\"end\",t),!1},z.prototype.oncancel=function(t){return this.onAny(\"end\",t),!1},z.prototype.onAny=function(t,i){var e,o=this,n=\"processOn\"+t.charAt(0).toUpperCase()+t.slice(1);i=l(i);return v(i,function(t){e=o.getIdentifier(t),v(o.collections,function(t,i,e){e.ids.indexOf(i)>=0&&(e[n](t),t._found_=!0)}.bind(null,t,e)),t._found_||o.removeIdentifier(e)}),!1},z.prototype.destroy=function(){this.unbindDocument(!0),this.ids={},this.index=0,this.collections.forEach(function(t){t.destroy()}),this.off()},z.prototype.onDestroyed=function(t,i){if(this.collections.indexOf(i)<0)return!1;this.collections.splice(this.collections.indexOf(i),1)};var D=new z;i.default={create:function(t){return D.create(t)},factory:D}}]).default});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uaXBwbGVqcy9kaXN0L25pcHBsZWpzLmpzIiwibWFwcGluZ3MiOiJBQUFBLGVBQWUsS0FBaUQsb0JBQW9CLENBQTBILENBQUMsbUJBQW1CLG1CQUFtQixTQUFTLGNBQWMsNEJBQTRCLFlBQVkscUJBQXFCLDJEQUEyRCx1Q0FBdUMscUNBQXFDLG9CQUFvQixFQUFFLGlCQUFpQiw0RkFBNEYsZUFBZSx3Q0FBd0MsU0FBUyxFQUFFLG1CQUFtQiw4QkFBOEIscURBQXFELDBCQUEwQiw2Q0FBNkMsc0JBQXNCLDZEQUE2RCxZQUFZLGVBQWUsU0FBUyxpQkFBaUIsaUNBQWlDLGlCQUFpQixZQUFZLFVBQVUsc0JBQXNCLG1CQUFtQixpREFBaUQsaUJBQWlCLGtCQUFrQixhQUFhLE9BQU8sc0JBQXNCLHdCQUF3QiwwQkFBMEIsZUFBZSx1QkFBdUIsZUFBZSx1QkFBdUIseUJBQXlCLDREQUE0RCxtQkFBbUIsa0NBQWtDLFdBQVcsNEZBQTRGLG1CQUFtQixrQ0FBa0MsV0FBVywrRkFBK0YsZUFBZSxvRUFBb0UsY0FBYyxPQUFPLGdRQUFnUSxpQkFBaUIsb0tBQW9LLG1CQUFtQixXQUFXLHlFQUF5RSxLQUFLLDRCQUE0QixJQUFJLHdCQUF3QixtQkFBbUIsU0FBUyxlQUFlLFNBQVMsUUFBUSwrQ0FBK0MsNkNBQTZDLElBQUksaUJBQWlCLGdEQUFnRCxTQUFTLGlCQUFpQixtQ0FBbUMsSUFBSSxhQUFhLFVBQVUsb0ZBQW9GLGlEQUFpRCxNQUFNLGNBQWMsS0FBSyxzRUFBc0UsTUFBTSw2REFBNkQsT0FBTyxnRUFBZ0UsdUNBQXVDLDBCQUEwQixvQ0FBb0MsWUFBWSxXQUFXLGlGQUFpRixZQUFZLCtCQUErQiwwQ0FBMEMsOEJBQThCLDBKQUEwSixtQ0FBbUMsaUNBQWlDLDhCQUE4QixZQUFZLFdBQVcseUZBQXlGLFVBQVUsZ0JBQWdCLElBQUksRUFBRSxnQ0FBZ0MsOEJBQThCLGdDQUFnQyxTQUFTLG1HQUFtRyxTQUFTLGtCQUFrQixtQ0FBbUMsV0FBVywwQ0FBMEMsK0JBQStCLGlIQUFpSCxtRUFBbUUscUNBQXFDLGdEQUFnRCwwR0FBMEcsUUFBUSxnQkFBZ0IsaUlBQWlJLCtKQUErSix5SUFBeUksd2ZBQXdmLGVBQWUseUVBQXlFLGlCQUFpQixnYkFBZ2IsZ0NBQWdDLHFDQUFxQyxpREFBaUQsV0FBVyw2Q0FBNkMsU0FBUywwREFBMEQsYUFBYSxnRkFBZ0YsU0FBUywwTkFBME4sVUFBVSw4UEFBOFAsNEZBQTRGLHFDQUFxQyxnR0FBZ0csWUFBWSxpQ0FBaUMsdUhBQXVILHNDQUFzQyx3SEFBd0gsZ0NBQWdDLHVLQUF1Syw4QkFBOEIsV0FBVyx1S0FBdUssd0JBQXdCLHdDQUF3QyxpRUFBaUUsd0JBQXdCLDhCQUE4QixXQUFXLCtCQUErQiwyS0FBMkssZ0RBQWdELHVGQUF1Riw2Q0FBNkMsa0NBQWtDLHVIQUF1SCxTQUFTLHVDQUF1QyxXQUFXLGlCQUFpQixhQUFhLG1DQUFtQyx3Q0FBd0MsT0FBTyxVQUFVLFNBQVMsdUVBQXVFLHVFQUF1RSxpREFBaUQscUJBQXFCLHFDQUFxQyxTQUFTLDJGQUEyRix1Q0FBdUMsZ0JBQWdCLG9CQUFvQiwwQ0FBMEMsbURBQW1ELHNOQUFzTixXQUFXLG1GQUFtRixTQUFTLHlCQUF5QixnQkFBZ0Isa0VBQWtFLDhCQUE4Qix3SUFBd0ksMkJBQTJCLFVBQVUsUUFBUSxnQkFBZ0IsbUZBQW1GLG9EQUFvRCxnRkFBZ0YsYUFBYSx5S0FBeUssb0tBQW9LLHdEQUF3RCxzSUFBc0ksK0VBQStFLG1CQUFtQixtUEFBbVAsMEJBQTBCLHVCQUF1QixJQUFJLHdDQUF3QyxVQUFVLGlDQUFpQyxnSUFBZ0ksOEJBQThCLG1CQUFtQixzQkFBc0IsaUVBQWlFLDRCQUE0Qix3Q0FBd0MsOEJBQThCLG1HQUFtRyxlQUFlLGlCQUFpQiwwQ0FBMEMsb0NBQW9DLCtKQUErSixnQ0FBZ0MsNkJBQTZCLDBCQUEwQixrQkFBa0IsNk1BQTZNLFFBQVEsZUFBZSxFQUFFLGdOQUFnTixrQ0FBa0MsbURBQW1ELG9DQUFvQyw2QkFBNkIsNkNBQTZDLGtMQUFrTCx3Q0FBd0MsZUFBZSxvRkFBb0YsNENBQTRDLHVGQUF1RixjQUFjLGlDQUFpQywyQkFBMkIscUJBQXFCLHVCQUF1QixrSUFBa0ksa0RBQWtELHdCQUF3QixLQUFLLGFBQWEsa0NBQWtDLDhEQUE4RCw4QkFBOEIsd0NBQXdDLGlHQUFpRyxvQkFBb0Isc0JBQXNCLDBFQUEwRSxrQkFBa0IsMEhBQTBILGtIQUFrSCxLQUFLLG1GQUFtRixLQUFLLFNBQVMsdUNBQXVDLHFCQUFxQixzTUFBc00sdUNBQXVDLCtGQUErRixlQUFlLHFEQUFxRCxLQUFLLHFHQUFxRyxrQkFBa0IsbUNBQW1DLFlBQVksMEJBQTBCLGVBQWUsMEJBQTBCLHFCQUFxQix3REFBd0Qsc0hBQXNILHVCQUF1QixtRUFBbUUsUUFBUSw4RUFBOEUsT0FBTyw2RUFBNkUsOENBQThDLFFBQVEsd0JBQXdCLDZKQUE2SixhQUFhLDBDQUEwQyxpQkFBaUIsUUFBUSx1RUFBdUUsT0FBTyw0R0FBNEcsa0JBQWtCLFNBQVMsYUFBYSwrQ0FBK0MsaUNBQWlDLDZCQUE2QiwwREFBMEQsMEJBQTBCLHNDQUFzQyxrSEFBa0gsa0NBQWtDLGlKQUFpSixtZEFBbWQsdUNBQXVDLDBYQUEwWCxnQ0FBZ0Msd0ZBQXdGLFlBQVksNEdBQTRHLGlGQUFpRixRQUFRLGNBQWMsV0FBVyxRQUFRLDRFQUE0RSxpQkFBaUIsTUFBTSxrQ0FBa0Msc0JBQXNCLDJDQUEyQyx3Q0FBd0MsRUFBRSxHQUFHLDZCQUE2QixLQUFLLEVBQUUsaUJBQWlCLGNBQWMsb0NBQW9DLEtBQUssZ0JBQWdCLDRFQUE0RSxXQUFXLDBLQUEwSyxNQUFNLHVDQUF1QyxvQkFBb0IsS0FBSyxnQ0FBZ0MsZ0NBQWdDLDBDQUEwQyxvQkFBb0IseURBQXlELHdDQUF3Qyw2QkFBNkIsNkNBQTZDLGtMQUFrTCxxQ0FBcUMsb0ZBQW9GLHdDQUF3QyxpSEFBaUgsdUNBQXVDLE1BQU0sNkxBQTZMLDBDQUEwQyxTQUFTLDBDQUEwQyxtREFBbUQsTUFBTSxTQUFTLGdDQUFnQywrQkFBK0IsK0JBQStCLDhCQUE4QixrQ0FBa0MsOEJBQThCLGlDQUFpQyxnRUFBZ0UsT0FBTyx1QkFBdUIscURBQXFELDRDQUE0QyxrREFBa0QsS0FBSyxnQ0FBZ0MsbUNBQW1DLG1EQUFtRCxZQUFZLGFBQWEsdUNBQXVDLDBDQUEwQyx3REFBd0QsWUFBWSxXQUFXLG1CQUFtQixtQkFBbUIsWUFBWSxXQUFXIiwic291cmNlcyI6WyIvVXNlcnMveXVzdWYvRGVza3RvcC9wb3J0Zm9saW8vbm9kZV9tb2R1bGVzL25pcHBsZWpzL2Rpc3QvbmlwcGxlanMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiIWZ1bmN0aW9uKHQsaSl7XCJvYmplY3RcIj09dHlwZW9mIGV4cG9ydHMmJlwib2JqZWN0XCI9PXR5cGVvZiBtb2R1bGU/bW9kdWxlLmV4cG9ydHM9aSgpOlwiZnVuY3Rpb25cIj09dHlwZW9mIGRlZmluZSYmZGVmaW5lLmFtZD9kZWZpbmUoXCJuaXBwbGVqc1wiLFtdLGkpOlwib2JqZWN0XCI9PXR5cGVvZiBleHBvcnRzP2V4cG9ydHMubmlwcGxlanM9aSgpOnQubmlwcGxlanM9aSgpfSh3aW5kb3csZnVuY3Rpb24oKXtyZXR1cm4gZnVuY3Rpb24odCl7dmFyIGk9e307ZnVuY3Rpb24gZShvKXtpZihpW29dKXJldHVybiBpW29dLmV4cG9ydHM7dmFyIG49aVtvXT17aTpvLGw6ITEsZXhwb3J0czp7fX07cmV0dXJuIHRbb10uY2FsbChuLmV4cG9ydHMsbixuLmV4cG9ydHMsZSksbi5sPSEwLG4uZXhwb3J0c31yZXR1cm4gZS5tPXQsZS5jPWksZS5kPWZ1bmN0aW9uKHQsaSxvKXtlLm8odCxpKXx8T2JqZWN0LmRlZmluZVByb3BlcnR5KHQsaSx7ZW51bWVyYWJsZTohMCxnZXQ6b30pfSxlLnI9ZnVuY3Rpb24odCl7XCJ1bmRlZmluZWRcIiE9dHlwZW9mIFN5bWJvbCYmU3ltYm9sLnRvU3RyaW5nVGFnJiZPYmplY3QuZGVmaW5lUHJvcGVydHkodCxTeW1ib2wudG9TdHJpbmdUYWcse3ZhbHVlOlwiTW9kdWxlXCJ9KSxPYmplY3QuZGVmaW5lUHJvcGVydHkodCxcIl9fZXNNb2R1bGVcIix7dmFsdWU6ITB9KX0sZS50PWZ1bmN0aW9uKHQsaSl7aWYoMSZpJiYodD1lKHQpKSw4JmkpcmV0dXJuIHQ7aWYoNCZpJiZcIm9iamVjdFwiPT10eXBlb2YgdCYmdCYmdC5fX2VzTW9kdWxlKXJldHVybiB0O3ZhciBvPU9iamVjdC5jcmVhdGUobnVsbCk7aWYoZS5yKG8pLE9iamVjdC5kZWZpbmVQcm9wZXJ0eShvLFwiZGVmYXVsdFwiLHtlbnVtZXJhYmxlOiEwLHZhbHVlOnR9KSwyJmkmJlwic3RyaW5nXCIhPXR5cGVvZiB0KWZvcih2YXIgbiBpbiB0KWUuZChvLG4sZnVuY3Rpb24oaSl7cmV0dXJuIHRbaV19LmJpbmQobnVsbCxuKSk7cmV0dXJuIG99LGUubj1mdW5jdGlvbih0KXt2YXIgaT10JiZ0Ll9fZXNNb2R1bGU/ZnVuY3Rpb24oKXtyZXR1cm4gdC5kZWZhdWx0fTpmdW5jdGlvbigpe3JldHVybiB0fTtyZXR1cm4gZS5kKGksXCJhXCIsaSksaX0sZS5vPWZ1bmN0aW9uKHQsaSl7cmV0dXJuIE9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbCh0LGkpfSxlLnA9XCJcIixlKGUucz0wKX0oW2Z1bmN0aW9uKHQsaSxlKXtcInVzZSBzdHJpY3RcIjtlLnIoaSk7dmFyIG8sbj1mdW5jdGlvbih0LGkpe3ZhciBlPWkueC10Lngsbz1pLnktdC55O3JldHVybiBNYXRoLnNxcnQoZSplK28qbyl9LHM9ZnVuY3Rpb24odCl7cmV0dXJuIHQqKE1hdGguUEkvMTgwKX0scj1mdW5jdGlvbih0KXtyZXR1cm4gdCooMTgwL01hdGguUEkpfSxkPW5ldyBNYXAsYT1mdW5jdGlvbih0KXtkLmhhcyh0KSYmY2xlYXJUaW1lb3V0KGQuZ2V0KHQpKSxkLnNldCh0LHNldFRpbWVvdXQodCwxMDApKX0scD1mdW5jdGlvbih0LGksZSl7Zm9yKHZhciBvLG49aS5zcGxpdCgvWyAsXSsvZykscz0wO3M8bi5sZW5ndGg7cys9MSlvPW5bc10sdC5hZGRFdmVudExpc3RlbmVyP3QuYWRkRXZlbnRMaXN0ZW5lcihvLGUsITEpOnQuYXR0YWNoRXZlbnQmJnQuYXR0YWNoRXZlbnQobyxlKX0sYz1mdW5jdGlvbih0LGksZSl7Zm9yKHZhciBvLG49aS5zcGxpdCgvWyAsXSsvZykscz0wO3M8bi5sZW5ndGg7cys9MSlvPW5bc10sdC5yZW1vdmVFdmVudExpc3RlbmVyP3QucmVtb3ZlRXZlbnRMaXN0ZW5lcihvLGUpOnQuZGV0YWNoRXZlbnQmJnQuZGV0YWNoRXZlbnQobyxlKX0sbD1mdW5jdGlvbih0KXtyZXR1cm4gdC5wcmV2ZW50RGVmYXVsdCgpLHQudHlwZS5tYXRjaCgvXnRvdWNoLyk/dC5jaGFuZ2VkVG91Y2hlczp0fSxoPWZ1bmN0aW9uKCl7cmV0dXJue3g6dm9pZCAwIT09d2luZG93LnBhZ2VYT2Zmc2V0P3dpbmRvdy5wYWdlWE9mZnNldDooZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50fHxkb2N1bWVudC5ib2R5LnBhcmVudE5vZGV8fGRvY3VtZW50LmJvZHkpLnNjcm9sbExlZnQseTp2b2lkIDAhPT13aW5kb3cucGFnZVlPZmZzZXQ/d2luZG93LnBhZ2VZT2Zmc2V0Oihkb2N1bWVudC5kb2N1bWVudEVsZW1lbnR8fGRvY3VtZW50LmJvZHkucGFyZW50Tm9kZXx8ZG9jdW1lbnQuYm9keSkuc2Nyb2xsVG9wfX0sdT1mdW5jdGlvbih0LGkpe2kudG9wfHxpLnJpZ2h0fHxpLmJvdHRvbXx8aS5sZWZ0Pyh0LnN0eWxlLnRvcD1pLnRvcCx0LnN0eWxlLnJpZ2h0PWkucmlnaHQsdC5zdHlsZS5ib3R0b209aS5ib3R0b20sdC5zdHlsZS5sZWZ0PWkubGVmdCk6KHQuc3R5bGUubGVmdD1pLngrXCJweFwiLHQuc3R5bGUudG9wPWkueStcInB4XCIpfSxmPWZ1bmN0aW9uKHQsaSxlKXt2YXIgbz15KHQpO2Zvcih2YXIgbiBpbiBvKWlmKG8uaGFzT3duUHJvcGVydHkobikpaWYoXCJzdHJpbmdcIj09dHlwZW9mIGkpb1tuXT1pK1wiIFwiK2U7ZWxzZXtmb3IodmFyIHM9XCJcIixyPTAsZD1pLmxlbmd0aDtyPGQ7cis9MSlzKz1pW3JdK1wiIFwiK2UrXCIsIFwiO29bbl09cy5zbGljZSgwLC0yKX1yZXR1cm4gb30seT1mdW5jdGlvbih0KXt2YXIgaT17fTtpW3RdPVwiXCI7cmV0dXJuW1wid2Via2l0XCIsXCJNb3pcIixcIm9cIl0uZm9yRWFjaChmdW5jdGlvbihlKXtpW2UrdC5jaGFyQXQoMCkudG9VcHBlckNhc2UoKSt0LnNsaWNlKDEpXT1cIlwifSksaX0sbT1mdW5jdGlvbih0LGkpe2Zvcih2YXIgZSBpbiBpKWkuaGFzT3duUHJvcGVydHkoZSkmJih0W2VdPWlbZV0pO3JldHVybiB0fSx2PWZ1bmN0aW9uKHQsaSl7aWYodC5sZW5ndGgpZm9yKHZhciBlPTAsbz10Lmxlbmd0aDtlPG87ZSs9MSlpKHRbZV0pO2Vsc2UgaSh0KX0sZz0hIShcIm9udG91Y2hzdGFydFwiaW4gd2luZG93KSxiPSEhd2luZG93LlBvaW50ZXJFdmVudCx4PSEhd2luZG93Lk1TUG9pbnRlckV2ZW50LE89e3N0YXJ0OlwibW91c2Vkb3duXCIsbW92ZTpcIm1vdXNlbW92ZVwiLGVuZDpcIm1vdXNldXBcIn0sdz17fTtmdW5jdGlvbiBfKCl7fWI/bz17c3RhcnQ6XCJwb2ludGVyZG93blwiLG1vdmU6XCJwb2ludGVybW92ZVwiLGVuZDpcInBvaW50ZXJ1cCwgcG9pbnRlcmNhbmNlbFwifTp4P289e3N0YXJ0OlwiTVNQb2ludGVyRG93blwiLG1vdmU6XCJNU1BvaW50ZXJNb3ZlXCIsZW5kOlwiTVNQb2ludGVyVXBcIn06Zz8obz17c3RhcnQ6XCJ0b3VjaHN0YXJ0XCIsbW92ZTpcInRvdWNobW92ZVwiLGVuZDpcInRvdWNoZW5kLCB0b3VjaGNhbmNlbFwifSx3PU8pOm89TyxfLnByb3RvdHlwZS5vbj1mdW5jdGlvbih0LGkpe3ZhciBlLG89dC5zcGxpdCgvWyAsXSsvZyk7dGhpcy5faGFuZGxlcnNfPXRoaXMuX2hhbmRsZXJzX3x8e307Zm9yKHZhciBuPTA7bjxvLmxlbmd0aDtuKz0xKWU9b1tuXSx0aGlzLl9oYW5kbGVyc19bZV09dGhpcy5faGFuZGxlcnNfW2VdfHxbXSx0aGlzLl9oYW5kbGVyc19bZV0ucHVzaChpKTtyZXR1cm4gdGhpc30sXy5wcm90b3R5cGUub2ZmPWZ1bmN0aW9uKHQsaSl7cmV0dXJuIHRoaXMuX2hhbmRsZXJzXz10aGlzLl9oYW5kbGVyc198fHt9LHZvaWQgMD09PXQ/dGhpcy5faGFuZGxlcnNfPXt9OnZvaWQgMD09PWk/dGhpcy5faGFuZGxlcnNfW3RdPW51bGw6dGhpcy5faGFuZGxlcnNfW3RdJiZ0aGlzLl9oYW5kbGVyc19bdF0uaW5kZXhPZihpKT49MCYmdGhpcy5faGFuZGxlcnNfW3RdLnNwbGljZSh0aGlzLl9oYW5kbGVyc19bdF0uaW5kZXhPZihpKSwxKSx0aGlzfSxfLnByb3RvdHlwZS50cmlnZ2VyPWZ1bmN0aW9uKHQsaSl7dmFyIGUsbz10aGlzLG49dC5zcGxpdCgvWyAsXSsvZyk7by5faGFuZGxlcnNfPW8uX2hhbmRsZXJzX3x8e307Zm9yKHZhciBzPTA7czxuLmxlbmd0aDtzKz0xKWU9bltzXSxvLl9oYW5kbGVyc19bZV0mJm8uX2hhbmRsZXJzX1tlXS5sZW5ndGgmJm8uX2hhbmRsZXJzX1tlXS5mb3JFYWNoKGZ1bmN0aW9uKHQpe3QuY2FsbChvLHt0eXBlOmUsdGFyZ2V0Om99LGkpfSl9LF8ucHJvdG90eXBlLmNvbmZpZz1mdW5jdGlvbih0KXt0aGlzLm9wdGlvbnM9dGhpcy5kZWZhdWx0c3x8e30sdCYmKHRoaXMub3B0aW9ucz1mdW5jdGlvbih0LGkpe3ZhciBlPXt9O2Zvcih2YXIgbyBpbiB0KXQuaGFzT3duUHJvcGVydHkobykmJmkuaGFzT3duUHJvcGVydHkobyk/ZVtvXT1pW29dOnQuaGFzT3duUHJvcGVydHkobykmJihlW29dPXRbb10pO3JldHVybiBlfSh0aGlzLm9wdGlvbnMsdCkpfSxfLnByb3RvdHlwZS5iaW5kRXZ0PWZ1bmN0aW9uKHQsaSl7dmFyIGU9dGhpcztyZXR1cm4gZS5fZG9tSGFuZGxlcnNfPWUuX2RvbUhhbmRsZXJzX3x8e30sZS5fZG9tSGFuZGxlcnNfW2ldPWZ1bmN0aW9uKCl7XCJmdW5jdGlvblwiPT10eXBlb2YgZVtcIm9uXCIraV0/ZVtcIm9uXCIraV0uYXBwbHkoZSxhcmd1bWVudHMpOmNvbnNvbGUud2FybignW1dBUk5JTkddIDogTWlzc2luZyBcIm9uJytpKydcIiBoYW5kbGVyLicpfSxwKHQsb1tpXSxlLl9kb21IYW5kbGVyc19baV0pLHdbaV0mJnAodCx3W2ldLGUuX2RvbUhhbmRsZXJzX1tpXSksZX0sXy5wcm90b3R5cGUudW5iaW5kRXZ0PWZ1bmN0aW9uKHQsaSl7cmV0dXJuIHRoaXMuX2RvbUhhbmRsZXJzXz10aGlzLl9kb21IYW5kbGVyc198fHt9LGModCxvW2ldLHRoaXMuX2RvbUhhbmRsZXJzX1tpXSksd1tpXSYmYyh0LHdbaV0sdGhpcy5fZG9tSGFuZGxlcnNfW2ldKSxkZWxldGUgdGhpcy5fZG9tSGFuZGxlcnNfW2ldLHRoaXN9O3ZhciBUPV87ZnVuY3Rpb24gayh0LGkpe3JldHVybiB0aGlzLmlkZW50aWZpZXI9aS5pZGVudGlmaWVyLHRoaXMucG9zaXRpb249aS5wb3NpdGlvbix0aGlzLmZyb250UG9zaXRpb249aS5mcm9udFBvc2l0aW9uLHRoaXMuY29sbGVjdGlvbj10LHRoaXMuZGVmYXVsdHM9e3NpemU6MTAwLHRocmVzaG9sZDouMSxjb2xvcjpcIndoaXRlXCIsZmFkZVRpbWU6MjUwLGRhdGFPbmx5OiExLHJlc3RKb3lzdGljazohMCxyZXN0T3BhY2l0eTouNSxtb2RlOlwiZHluYW1pY1wiLHpvbmU6ZG9jdW1lbnQuYm9keSxsb2NrWDohMSxsb2NrWTohMSxzaGFwZTpcImNpcmNsZVwifSx0aGlzLmNvbmZpZyhpKSxcImR5bmFtaWNcIj09PXRoaXMub3B0aW9ucy5tb2RlJiYodGhpcy5vcHRpb25zLnJlc3RPcGFjaXR5PTApLHRoaXMuaWQ9ay5pZCxrLmlkKz0xLHRoaXMuYnVpbGRFbCgpLnN0eWxpemUoKSx0aGlzLmluc3RhbmNlPXtlbDp0aGlzLnVpLmVsLG9uOnRoaXMub24uYmluZCh0aGlzKSxvZmY6dGhpcy5vZmYuYmluZCh0aGlzKSxzaG93OnRoaXMuc2hvdy5iaW5kKHRoaXMpLGhpZGU6dGhpcy5oaWRlLmJpbmQodGhpcyksYWRkOnRoaXMuYWRkVG9Eb20uYmluZCh0aGlzKSxyZW1vdmU6dGhpcy5yZW1vdmVGcm9tRG9tLmJpbmQodGhpcyksZGVzdHJveTp0aGlzLmRlc3Ryb3kuYmluZCh0aGlzKSxzZXRQb3NpdGlvbjp0aGlzLnNldFBvc2l0aW9uLmJpbmQodGhpcykscmVzZXREaXJlY3Rpb246dGhpcy5yZXNldERpcmVjdGlvbi5iaW5kKHRoaXMpLGNvbXB1dGVEaXJlY3Rpb246dGhpcy5jb21wdXRlRGlyZWN0aW9uLmJpbmQodGhpcyksdHJpZ2dlcjp0aGlzLnRyaWdnZXIuYmluZCh0aGlzKSxwb3NpdGlvbjp0aGlzLnBvc2l0aW9uLGZyb250UG9zaXRpb246dGhpcy5mcm9udFBvc2l0aW9uLHVpOnRoaXMudWksaWRlbnRpZmllcjp0aGlzLmlkZW50aWZpZXIsaWQ6dGhpcy5pZCxvcHRpb25zOnRoaXMub3B0aW9uc30sdGhpcy5pbnN0YW5jZX1rLnByb3RvdHlwZT1uZXcgVCxrLmNvbnN0cnVjdG9yPWssay5pZD0wLGsucHJvdG90eXBlLmJ1aWxkRWw9ZnVuY3Rpb24odCl7cmV0dXJuIHRoaXMudWk9e30sdGhpcy5vcHRpb25zLmRhdGFPbmx5P3RoaXM6KHRoaXMudWkuZWw9ZG9jdW1lbnQuY3JlYXRlRWxlbWVudChcImRpdlwiKSx0aGlzLnVpLmJhY2s9ZG9jdW1lbnQuY3JlYXRlRWxlbWVudChcImRpdlwiKSx0aGlzLnVpLmZyb250PWRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoXCJkaXZcIiksdGhpcy51aS5lbC5jbGFzc05hbWU9XCJuaXBwbGUgY29sbGVjdGlvbl9cIit0aGlzLmNvbGxlY3Rpb24uaWQsdGhpcy51aS5iYWNrLmNsYXNzTmFtZT1cImJhY2tcIix0aGlzLnVpLmZyb250LmNsYXNzTmFtZT1cImZyb250XCIsdGhpcy51aS5lbC5zZXRBdHRyaWJ1dGUoXCJpZFwiLFwibmlwcGxlX1wiK3RoaXMuY29sbGVjdGlvbi5pZCtcIl9cIit0aGlzLmlkKSx0aGlzLnVpLmVsLmFwcGVuZENoaWxkKHRoaXMudWkuYmFjayksdGhpcy51aS5lbC5hcHBlbmRDaGlsZCh0aGlzLnVpLmZyb250KSx0aGlzKX0say5wcm90b3R5cGUuc3R5bGl6ZT1mdW5jdGlvbigpe2lmKHRoaXMub3B0aW9ucy5kYXRhT25seSlyZXR1cm4gdGhpczt2YXIgdD10aGlzLm9wdGlvbnMuZmFkZVRpbWUrXCJtc1wiLGk9ZnVuY3Rpb24odCxpKXt2YXIgZT15KHQpO2Zvcih2YXIgbyBpbiBlKWUuaGFzT3duUHJvcGVydHkobykmJihlW29dPWkpO3JldHVybiBlfShcImJvcmRlclJhZGl1c1wiLFwiNTAlXCIpLGU9ZihcInRyYW5zaXRpb25cIixcIm9wYWNpdHlcIix0KSxvPXt9O3JldHVybiBvLmVsPXtwb3NpdGlvbjpcImFic29sdXRlXCIsb3BhY2l0eTp0aGlzLm9wdGlvbnMucmVzdE9wYWNpdHksZGlzcGxheTpcImJsb2NrXCIsekluZGV4Ojk5OX0sby5iYWNrPXtwb3NpdGlvbjpcImFic29sdXRlXCIsZGlzcGxheTpcImJsb2NrXCIsd2lkdGg6dGhpcy5vcHRpb25zLnNpemUrXCJweFwiLGhlaWdodDp0aGlzLm9wdGlvbnMuc2l6ZStcInB4XCIsbGVmdDowLG1hcmdpbkxlZnQ6LXRoaXMub3B0aW9ucy5zaXplLzIrXCJweFwiLG1hcmdpblRvcDotdGhpcy5vcHRpb25zLnNpemUvMitcInB4XCIsYmFja2dyb3VuZDp0aGlzLm9wdGlvbnMuY29sb3Isb3BhY2l0eTpcIi41XCJ9LG8uZnJvbnQ9e3dpZHRoOnRoaXMub3B0aW9ucy5zaXplLzIrXCJweFwiLGhlaWdodDp0aGlzLm9wdGlvbnMuc2l6ZS8yK1wicHhcIixwb3NpdGlvbjpcImFic29sdXRlXCIsZGlzcGxheTpcImJsb2NrXCIsbGVmdDowLG1hcmdpbkxlZnQ6LXRoaXMub3B0aW9ucy5zaXplLzQrXCJweFwiLG1hcmdpblRvcDotdGhpcy5vcHRpb25zLnNpemUvNCtcInB4XCIsYmFja2dyb3VuZDp0aGlzLm9wdGlvbnMuY29sb3Isb3BhY2l0eTpcIi41XCIsdHJhbnNmb3JtOlwidHJhbnNsYXRlKDBweCwgMHB4KVwifSxtKG8uZWwsZSksXCJjaXJjbGVcIj09PXRoaXMub3B0aW9ucy5zaGFwZSYmbShvLmJhY2ssaSksbShvLmZyb250LGkpLHRoaXMuYXBwbHlTdHlsZXMobyksdGhpc30say5wcm90b3R5cGUuYXBwbHlTdHlsZXM9ZnVuY3Rpb24odCl7Zm9yKHZhciBpIGluIHRoaXMudWkpaWYodGhpcy51aS5oYXNPd25Qcm9wZXJ0eShpKSlmb3IodmFyIGUgaW4gdFtpXSl0aGlzLnVpW2ldLnN0eWxlW2VdPXRbaV1bZV07cmV0dXJuIHRoaXN9LGsucHJvdG90eXBlLmFkZFRvRG9tPWZ1bmN0aW9uKCl7cmV0dXJuIHRoaXMub3B0aW9ucy5kYXRhT25seXx8ZG9jdW1lbnQuYm9keS5jb250YWlucyh0aGlzLnVpLmVsKT90aGlzOih0aGlzLm9wdGlvbnMuem9uZS5hcHBlbmRDaGlsZCh0aGlzLnVpLmVsKSx0aGlzKX0say5wcm90b3R5cGUucmVtb3ZlRnJvbURvbT1mdW5jdGlvbigpe3JldHVybiB0aGlzLm9wdGlvbnMuZGF0YU9ubHl8fCFkb2N1bWVudC5ib2R5LmNvbnRhaW5zKHRoaXMudWkuZWwpP3RoaXM6KHRoaXMub3B0aW9ucy56b25lLnJlbW92ZUNoaWxkKHRoaXMudWkuZWwpLHRoaXMpfSxrLnByb3RvdHlwZS5kZXN0cm95PWZ1bmN0aW9uKCl7Y2xlYXJUaW1lb3V0KHRoaXMucmVtb3ZlVGltZW91dCksY2xlYXJUaW1lb3V0KHRoaXMuc2hvd1RpbWVvdXQpLGNsZWFyVGltZW91dCh0aGlzLnJlc3RUaW1lb3V0KSx0aGlzLnRyaWdnZXIoXCJkZXN0cm95ZWRcIix0aGlzLmluc3RhbmNlKSx0aGlzLnJlbW92ZUZyb21Eb20oKSx0aGlzLm9mZigpfSxrLnByb3RvdHlwZS5zaG93PWZ1bmN0aW9uKHQpe3ZhciBpPXRoaXM7cmV0dXJuIGkub3B0aW9ucy5kYXRhT25seT9pOihjbGVhclRpbWVvdXQoaS5yZW1vdmVUaW1lb3V0KSxjbGVhclRpbWVvdXQoaS5zaG93VGltZW91dCksY2xlYXJUaW1lb3V0KGkucmVzdFRpbWVvdXQpLGkuYWRkVG9Eb20oKSxpLnJlc3RDYWxsYmFjaygpLHNldFRpbWVvdXQoZnVuY3Rpb24oKXtpLnVpLmVsLnN0eWxlLm9wYWNpdHk9MX0sMCksaS5zaG93VGltZW91dD1zZXRUaW1lb3V0KGZ1bmN0aW9uKCl7aS50cmlnZ2VyKFwic2hvd25cIixpLmluc3RhbmNlKSxcImZ1bmN0aW9uXCI9PXR5cGVvZiB0JiZ0LmNhbGwodGhpcyl9LGkub3B0aW9ucy5mYWRlVGltZSksaSl9LGsucHJvdG90eXBlLmhpZGU9ZnVuY3Rpb24odCl7dmFyIGk9dGhpcztpZihpLm9wdGlvbnMuZGF0YU9ubHkpcmV0dXJuIGk7aWYoaS51aS5lbC5zdHlsZS5vcGFjaXR5PWkub3B0aW9ucy5yZXN0T3BhY2l0eSxjbGVhclRpbWVvdXQoaS5yZW1vdmVUaW1lb3V0KSxjbGVhclRpbWVvdXQoaS5zaG93VGltZW91dCksY2xlYXJUaW1lb3V0KGkucmVzdFRpbWVvdXQpLGkucmVtb3ZlVGltZW91dD1zZXRUaW1lb3V0KGZ1bmN0aW9uKCl7dmFyIGU9XCJkeW5hbWljXCI9PT1pLm9wdGlvbnMubW9kZT9cIm5vbmVcIjpcImJsb2NrXCI7aS51aS5lbC5zdHlsZS5kaXNwbGF5PWUsXCJmdW5jdGlvblwiPT10eXBlb2YgdCYmdC5jYWxsKGkpLGkudHJpZ2dlcihcImhpZGRlblwiLGkuaW5zdGFuY2UpfSxpLm9wdGlvbnMuZmFkZVRpbWUpLGkub3B0aW9ucy5yZXN0Sm95c3RpY2spe3ZhciBlPWkub3B0aW9ucy5yZXN0Sm95c3RpY2ssbz17fTtvLng9ITA9PT1lfHwhMSE9PWUueD8wOmkuaW5zdGFuY2UuZnJvbnRQb3NpdGlvbi54LG8ueT0hMD09PWV8fCExIT09ZS55PzA6aS5pbnN0YW5jZS5mcm9udFBvc2l0aW9uLnksaS5zZXRQb3NpdGlvbih0LG8pfXJldHVybiBpfSxrLnByb3RvdHlwZS5zZXRQb3NpdGlvbj1mdW5jdGlvbih0LGkpe3ZhciBlPXRoaXM7ZS5mcm9udFBvc2l0aW9uPXt4OmkueCx5OmkueX07dmFyIG89ZS5vcHRpb25zLmZhZGVUaW1lK1wibXNcIixuPXt9O24uZnJvbnQ9ZihcInRyYW5zaXRpb25cIixbXCJ0cmFuc2Zvcm1cIl0sbyk7dmFyIHM9e2Zyb250Ont9fTtzLmZyb250PXt0cmFuc2Zvcm06XCJ0cmFuc2xhdGUoXCIrZS5mcm9udFBvc2l0aW9uLngrXCJweCxcIitlLmZyb250UG9zaXRpb24ueStcInB4KVwifSxlLmFwcGx5U3R5bGVzKG4pLGUuYXBwbHlTdHlsZXMocyksZS5yZXN0VGltZW91dD1zZXRUaW1lb3V0KGZ1bmN0aW9uKCl7XCJmdW5jdGlvblwiPT10eXBlb2YgdCYmdC5jYWxsKGUpLGUucmVzdENhbGxiYWNrKCl9LGUub3B0aW9ucy5mYWRlVGltZSl9LGsucHJvdG90eXBlLnJlc3RDYWxsYmFjaz1mdW5jdGlvbigpe3ZhciB0PXt9O3QuZnJvbnQ9ZihcInRyYW5zaXRpb25cIixcIm5vbmVcIixcIlwiKSx0aGlzLmFwcGx5U3R5bGVzKHQpLHRoaXMudHJpZ2dlcihcInJlc3RlZFwiLHRoaXMuaW5zdGFuY2UpfSxrLnByb3RvdHlwZS5yZXNldERpcmVjdGlvbj1mdW5jdGlvbigpe3RoaXMuZGlyZWN0aW9uPXt4OiExLHk6ITEsYW5nbGU6ITF9fSxrLnByb3RvdHlwZS5jb21wdXRlRGlyZWN0aW9uPWZ1bmN0aW9uKHQpe3ZhciBpLGUsbyxuPXQuYW5nbGUucmFkaWFuLHM9TWF0aC5QSS80LHI9TWF0aC5QSS8yO2lmKG4+cyYmbjwzKnMmJiF0LmxvY2tYP2k9XCJ1cFwiOm4+LXMmJm48PXMmJiF0LmxvY2tZP2k9XCJsZWZ0XCI6bj4zKi1zJiZuPD0tcyYmIXQubG9ja1g/aT1cImRvd25cIjp0LmxvY2tZfHwoaT1cInJpZ2h0XCIpLHQubG9ja1l8fChlPW4+LXImJm48cj9cImxlZnRcIjpcInJpZ2h0XCIpLHQubG9ja1h8fChvPW4+MD9cInVwXCI6XCJkb3duXCIpLHQuZm9yY2U+dGhpcy5vcHRpb25zLnRocmVzaG9sZCl7dmFyIGQsYT17fTtmb3IoZCBpbiB0aGlzLmRpcmVjdGlvbil0aGlzLmRpcmVjdGlvbi5oYXNPd25Qcm9wZXJ0eShkKSYmKGFbZF09dGhpcy5kaXJlY3Rpb25bZF0pO3ZhciBwPXt9O2ZvcihkIGluIHRoaXMuZGlyZWN0aW9uPXt4OmUseTpvLGFuZ2xlOml9LHQuZGlyZWN0aW9uPXRoaXMuZGlyZWN0aW9uLGEpYVtkXT09PXRoaXMuZGlyZWN0aW9uW2RdJiYocFtkXT0hMCk7aWYocC54JiZwLnkmJnAuYW5nbGUpcmV0dXJuIHQ7cC54JiZwLnl8fHRoaXMudHJpZ2dlcihcInBsYWluXCIsdCkscC54fHx0aGlzLnRyaWdnZXIoXCJwbGFpbjpcIitlLHQpLHAueXx8dGhpcy50cmlnZ2VyKFwicGxhaW46XCIrbyx0KSxwLmFuZ2xlfHx0aGlzLnRyaWdnZXIoXCJkaXIgZGlyOlwiK2ksdCl9ZWxzZSB0aGlzLnJlc2V0RGlyZWN0aW9uKCk7cmV0dXJuIHR9O3ZhciBQPWs7ZnVuY3Rpb24gRSh0LGkpe3RoaXMubmlwcGxlcz1bXSx0aGlzLmlkbGVzPVtdLHRoaXMuYWN0aXZlcz1bXSx0aGlzLmlkcz1bXSx0aGlzLnByZXNzdXJlSW50ZXJ2YWxzPXt9LHRoaXMubWFuYWdlcj10LHRoaXMuaWQ9RS5pZCxFLmlkKz0xLHRoaXMuZGVmYXVsdHM9e3pvbmU6ZG9jdW1lbnQuYm9keSxtdWx0aXRvdWNoOiExLG1heE51bWJlck9mTmlwcGxlczoxMCxtb2RlOlwiZHluYW1pY1wiLHBvc2l0aW9uOnt0b3A6MCxsZWZ0OjB9LGNhdGNoRGlzdGFuY2U6MjAwLHNpemU6MTAwLHRocmVzaG9sZDouMSxjb2xvcjpcIndoaXRlXCIsZmFkZVRpbWU6MjUwLGRhdGFPbmx5OiExLHJlc3RKb3lzdGljazohMCxyZXN0T3BhY2l0eTouNSxsb2NrWDohMSxsb2NrWTohMSxzaGFwZTpcImNpcmNsZVwiLGR5bmFtaWNQYWdlOiExLGZvbGxvdzohMX0sdGhpcy5jb25maWcoaSksXCJzdGF0aWNcIiE9PXRoaXMub3B0aW9ucy5tb2RlJiZcInNlbWlcIiE9PXRoaXMub3B0aW9ucy5tb2RlfHwodGhpcy5vcHRpb25zLm11bHRpdG91Y2g9ITEpLHRoaXMub3B0aW9ucy5tdWx0aXRvdWNofHwodGhpcy5vcHRpb25zLm1heE51bWJlck9mTmlwcGxlcz0xKTt2YXIgZT1nZXRDb21wdXRlZFN0eWxlKHRoaXMub3B0aW9ucy56b25lLnBhcmVudEVsZW1lbnQpO3JldHVybiBlJiZcImZsZXhcIj09PWUuZGlzcGxheSYmKHRoaXMucGFyZW50SXNGbGV4PSEwKSx0aGlzLnVwZGF0ZUJveCgpLHRoaXMucHJlcGFyZU5pcHBsZXMoKSx0aGlzLmJpbmRpbmdzKCksdGhpcy5iZWdpbigpLHRoaXMubmlwcGxlc31FLnByb3RvdHlwZT1uZXcgVCxFLmNvbnN0cnVjdG9yPUUsRS5pZD0wLEUucHJvdG90eXBlLnByZXBhcmVOaXBwbGVzPWZ1bmN0aW9uKCl7dmFyIHQ9dGhpcy5uaXBwbGVzO3Qub249dGhpcy5vbi5iaW5kKHRoaXMpLHQub2ZmPXRoaXMub2ZmLmJpbmQodGhpcyksdC5vcHRpb25zPXRoaXMub3B0aW9ucyx0LmRlc3Ryb3k9dGhpcy5kZXN0cm95LmJpbmQodGhpcyksdC5pZHM9dGhpcy5pZHMsdC5pZD10aGlzLmlkLHQucHJvY2Vzc09uTW92ZT10aGlzLnByb2Nlc3NPbk1vdmUuYmluZCh0aGlzKSx0LnByb2Nlc3NPbkVuZD10aGlzLnByb2Nlc3NPbkVuZC5iaW5kKHRoaXMpLHQuZ2V0PWZ1bmN0aW9uKGkpe2lmKHZvaWQgMD09PWkpcmV0dXJuIHRbMF07Zm9yKHZhciBlPTAsbz10Lmxlbmd0aDtlPG87ZSs9MSlpZih0W2VdLmlkZW50aWZpZXI9PT1pKXJldHVybiB0W2VdO3JldHVybiExfX0sRS5wcm90b3R5cGUuYmluZGluZ3M9ZnVuY3Rpb24oKXt0aGlzLmJpbmRFdnQodGhpcy5vcHRpb25zLnpvbmUsXCJzdGFydFwiKSx0aGlzLm9wdGlvbnMuem9uZS5zdHlsZS50b3VjaEFjdGlvbj1cIm5vbmVcIix0aGlzLm9wdGlvbnMuem9uZS5zdHlsZS5tc1RvdWNoQWN0aW9uPVwibm9uZVwifSxFLnByb3RvdHlwZS5iZWdpbj1mdW5jdGlvbigpe3ZhciB0PXRoaXMub3B0aW9ucztpZihcInN0YXRpY1wiPT09dC5tb2RlKXt2YXIgaT10aGlzLmNyZWF0ZU5pcHBsZSh0LnBvc2l0aW9uLHRoaXMubWFuYWdlci5nZXRJZGVudGlmaWVyKCkpO2kuYWRkKCksdGhpcy5pZGxlcy5wdXNoKGkpfX0sRS5wcm90b3R5cGUuY3JlYXRlTmlwcGxlPWZ1bmN0aW9uKHQsaSl7dmFyIGU9dGhpcy5tYW5hZ2VyLnNjcm9sbCxvPXt9LG49dGhpcy5vcHRpb25zLHM9dGhpcy5wYXJlbnRJc0ZsZXg/ZS54OmUueCt0aGlzLmJveC5sZWZ0LHI9dGhpcy5wYXJlbnRJc0ZsZXg/ZS55OmUueSt0aGlzLmJveC50b3A7aWYodC54JiZ0Lnkpbz17eDp0Lngtcyx5OnQueS1yfTtlbHNlIGlmKHQudG9wfHx0LnJpZ2h0fHx0LmJvdHRvbXx8dC5sZWZ0KXt2YXIgZD1kb2N1bWVudC5jcmVhdGVFbGVtZW50KFwiRElWXCIpO2Quc3R5bGUuZGlzcGxheT1cImhpZGRlblwiLGQuc3R5bGUudG9wPXQudG9wLGQuc3R5bGUucmlnaHQ9dC5yaWdodCxkLnN0eWxlLmJvdHRvbT10LmJvdHRvbSxkLnN0eWxlLmxlZnQ9dC5sZWZ0LGQuc3R5bGUucG9zaXRpb249XCJhYnNvbHV0ZVwiLG4uem9uZS5hcHBlbmRDaGlsZChkKTt2YXIgYT1kLmdldEJvdW5kaW5nQ2xpZW50UmVjdCgpO24uem9uZS5yZW1vdmVDaGlsZChkKSxvPXQsdD17eDphLmxlZnQrZS54LHk6YS50b3ArZS55fX12YXIgcD1uZXcgUCh0aGlzLHtjb2xvcjpuLmNvbG9yLHNpemU6bi5zaXplLHRocmVzaG9sZDpuLnRocmVzaG9sZCxmYWRlVGltZTpuLmZhZGVUaW1lLGRhdGFPbmx5Om4uZGF0YU9ubHkscmVzdEpveXN0aWNrOm4ucmVzdEpveXN0aWNrLHJlc3RPcGFjaXR5Om4ucmVzdE9wYWNpdHksbW9kZTpuLm1vZGUsaWRlbnRpZmllcjppLHBvc2l0aW9uOnQsem9uZTpuLnpvbmUsZnJvbnRQb3NpdGlvbjp7eDowLHk6MH0sc2hhcGU6bi5zaGFwZX0pO3JldHVybiBuLmRhdGFPbmx5fHwodShwLnVpLmVsLG8pLHUocC51aS5mcm9udCxwLmZyb250UG9zaXRpb24pKSx0aGlzLm5pcHBsZXMucHVzaChwKSx0aGlzLnRyaWdnZXIoXCJhZGRlZCBcIitwLmlkZW50aWZpZXIrXCI6YWRkZWRcIixwKSx0aGlzLm1hbmFnZXIudHJpZ2dlcihcImFkZGVkIFwiK3AuaWRlbnRpZmllcitcIjphZGRlZFwiLHApLHRoaXMuYmluZE5pcHBsZShwKSxwfSxFLnByb3RvdHlwZS51cGRhdGVCb3g9ZnVuY3Rpb24oKXt0aGlzLmJveD10aGlzLm9wdGlvbnMuem9uZS5nZXRCb3VuZGluZ0NsaWVudFJlY3QoKX0sRS5wcm90b3R5cGUuYmluZE5pcHBsZT1mdW5jdGlvbih0KXt2YXIgaSxlPXRoaXMsbz1mdW5jdGlvbih0LG8pe2k9dC50eXBlK1wiIFwiK28uaWQrXCI6XCIrdC50eXBlLGUudHJpZ2dlcihpLG8pfTt0Lm9uKFwiZGVzdHJveWVkXCIsZS5vbkRlc3Ryb3llZC5iaW5kKGUpKSx0Lm9uKFwic2hvd24gaGlkZGVuIHJlc3RlZCBkaXIgcGxhaW5cIixvKSx0Lm9uKFwiZGlyOnVwIGRpcjpyaWdodCBkaXI6ZG93biBkaXI6bGVmdFwiLG8pLHQub24oXCJwbGFpbjp1cCBwbGFpbjpyaWdodCBwbGFpbjpkb3duIHBsYWluOmxlZnRcIixvKX0sRS5wcm90b3R5cGUucHJlc3N1cmVGbj1mdW5jdGlvbih0LGksZSl7dmFyIG89dGhpcyxuPTA7Y2xlYXJJbnRlcnZhbChvLnByZXNzdXJlSW50ZXJ2YWxzW2VdKSxvLnByZXNzdXJlSW50ZXJ2YWxzW2VdPXNldEludGVydmFsKGZ1bmN0aW9uKCl7dmFyIGU9dC5mb3JjZXx8dC5wcmVzc3VyZXx8dC53ZWJraXRGb3JjZXx8MDtlIT09biYmKGkudHJpZ2dlcihcInByZXNzdXJlXCIsZSksby50cmlnZ2VyKFwicHJlc3N1cmUgXCIraS5pZGVudGlmaWVyK1wiOnByZXNzdXJlXCIsZSksbj1lKX0uYmluZChvKSwxMDApfSxFLnByb3RvdHlwZS5vbnN0YXJ0PWZ1bmN0aW9uKHQpe3ZhciBpPXRoaXMsZT1pLm9wdGlvbnMsbz10O3Q9bCh0KSxpLnVwZGF0ZUJveCgpO3JldHVybiB2KHQsZnVuY3Rpb24obil7aS5hY3RpdmVzLmxlbmd0aDxlLm1heE51bWJlck9mTmlwcGxlcz9pLnByb2Nlc3NPblN0YXJ0KG4pOm8udHlwZS5tYXRjaCgvXnRvdWNoLykmJihPYmplY3Qua2V5cyhpLm1hbmFnZXIuaWRzKS5mb3JFYWNoKGZ1bmN0aW9uKGUpe2lmKE9iamVjdC52YWx1ZXMoby50b3VjaGVzKS5maW5kSW5kZXgoZnVuY3Rpb24odCl7cmV0dXJuIHQuaWRlbnRpZmllcj09PWV9KTwwKXt2YXIgbj1bdFswXV07bi5pZGVudGlmaWVyPWUsaS5wcm9jZXNzT25FbmQobil9fSksaS5hY3RpdmVzLmxlbmd0aDxlLm1heE51bWJlck9mTmlwcGxlcyYmaS5wcm9jZXNzT25TdGFydChuKSl9KSxpLm1hbmFnZXIuYmluZERvY3VtZW50KCksITF9LEUucHJvdG90eXBlLnByb2Nlc3NPblN0YXJ0PWZ1bmN0aW9uKHQpe3ZhciBpLGU9dGhpcyxvPWUub3B0aW9ucyxzPWUubWFuYWdlci5nZXRJZGVudGlmaWVyKHQpLHI9dC5mb3JjZXx8dC5wcmVzc3VyZXx8dC53ZWJraXRGb3JjZXx8MCxkPXt4OnQucGFnZVgseTp0LnBhZ2VZfSxhPWUuZ2V0T3JDcmVhdGUocyxkKTthLmlkZW50aWZpZXIhPT1zJiZlLm1hbmFnZXIucmVtb3ZlSWRlbnRpZmllcihhLmlkZW50aWZpZXIpLGEuaWRlbnRpZmllcj1zO3ZhciBwPWZ1bmN0aW9uKGkpe2kudHJpZ2dlcihcInN0YXJ0XCIsaSksZS50cmlnZ2VyKFwic3RhcnQgXCIraS5pZCtcIjpzdGFydFwiLGkpLGkuc2hvdygpLHI+MCYmZS5wcmVzc3VyZUZuKHQsaSxpLmlkZW50aWZpZXIpLGUucHJvY2Vzc09uTW92ZSh0KX07aWYoKGk9ZS5pZGxlcy5pbmRleE9mKGEpKT49MCYmZS5pZGxlcy5zcGxpY2UoaSwxKSxlLmFjdGl2ZXMucHVzaChhKSxlLmlkcy5wdXNoKGEuaWRlbnRpZmllciksXCJzZW1pXCIhPT1vLm1vZGUpcChhKTtlbHNle2lmKCEobihkLGEucG9zaXRpb24pPD1vLmNhdGNoRGlzdGFuY2UpKXJldHVybiBhLmRlc3Ryb3koKSx2b2lkIGUucHJvY2Vzc09uU3RhcnQodCk7cChhKX1yZXR1cm4gYX0sRS5wcm90b3R5cGUuZ2V0T3JDcmVhdGU9ZnVuY3Rpb24odCxpKXt2YXIgZSxvPXRoaXMub3B0aW9ucztyZXR1cm4vKHNlbWl8c3RhdGljKS8udGVzdChvLm1vZGUpPyhlPXRoaXMuaWRsZXNbMF0pPyh0aGlzLmlkbGVzLnNwbGljZSgwLDEpLGUpOlwic2VtaVwiPT09by5tb2RlP3RoaXMuY3JlYXRlTmlwcGxlKGksdCk6KGNvbnNvbGUud2FybihcIkNvdWRsbid0IGZpbmQgdGhlIG5lZWRlZCBuaXBwbGUuXCIpLCExKTplPXRoaXMuY3JlYXRlTmlwcGxlKGksdCl9LEUucHJvdG90eXBlLnByb2Nlc3NPbk1vdmU9ZnVuY3Rpb24odCl7dmFyIGk9dGhpcy5vcHRpb25zLGU9dGhpcy5tYW5hZ2VyLmdldElkZW50aWZpZXIodCksbz10aGlzLm5pcHBsZXMuZ2V0KGUpLGQ9dGhpcy5tYW5hZ2VyLnNjcm9sbDtpZihmdW5jdGlvbih0KXtyZXR1cm4gaXNOYU4odC5idXR0b25zKT8wIT09dC5wcmVzc3VyZTowIT09dC5idXR0b25zfSh0KSl7aWYoIW8pcmV0dXJuIGNvbnNvbGUuZXJyb3IoXCJGb3VuZCB6b21iaWUgam95c3RpY2sgd2l0aCBJRCBcIitlKSx2b2lkIHRoaXMubWFuYWdlci5yZW1vdmVJZGVudGlmaWVyKGUpO2lmKGkuZHluYW1pY1BhZ2Upe3ZhciBhPW8uZWwuZ2V0Qm91bmRpbmdDbGllbnRSZWN0KCk7by5wb3NpdGlvbj17eDpkLngrYS5sZWZ0LHk6ZC55K2EudG9wfX1vLmlkZW50aWZpZXI9ZTt2YXIgcD1vLm9wdGlvbnMuc2l6ZS8yLGM9e3g6dC5wYWdlWCx5OnQucGFnZVl9O2kubG9ja1gmJihjLnk9by5wb3NpdGlvbi55KSxpLmxvY2tZJiYoYy54PW8ucG9zaXRpb24ueCk7dmFyIGwsaCx1LGYseSxtLHYsZyxiLHgsTz1uKGMsby5wb3NpdGlvbiksdz0obD1jLGg9by5wb3NpdGlvbix1PWgueC1sLngsZj1oLnktbC55LHIoTWF0aC5hdGFuMihmLHUpKSksXz1zKHcpLFQ9Ty9wLGs9e2Rpc3RhbmNlOk8scG9zaXRpb246Y307aWYoXCJjaXJjbGVcIj09PW8ub3B0aW9ucy5zaGFwZT8oeT1NYXRoLm1pbihPLHApLHY9by5wb3NpdGlvbixnPXkseD17eDowLHk6MH0sYj1zKGI9dykseC54PXYueC1nKk1hdGguY29zKGIpLHgueT12LnktZypNYXRoLnNpbihiKSxtPXgpOihtPWZ1bmN0aW9uKHQsaSxlKXtyZXR1cm57eDpNYXRoLm1pbihNYXRoLm1heCh0LngsaS54LWUpLGkueCtlKSx5Ok1hdGgubWluKE1hdGgubWF4KHQueSxpLnktZSksaS55K2UpfX0oYyxvLnBvc2l0aW9uLHApLHk9bihtLG8ucG9zaXRpb24pKSxpLmZvbGxvdyl7aWYoTz5wKXt2YXIgUD1jLngtbS54LEU9Yy55LW0ueTtvLnBvc2l0aW9uLngrPVAsby5wb3NpdGlvbi55Kz1FLG8uZWwuc3R5bGUudG9wPW8ucG9zaXRpb24ueS0odGhpcy5ib3gudG9wK2QueSkrXCJweFwiLG8uZWwuc3R5bGUubGVmdD1vLnBvc2l0aW9uLngtKHRoaXMuYm94LmxlZnQrZC54KStcInB4XCIsTz1uKGMsby5wb3NpdGlvbil9fWVsc2UgYz1tLE89eTt2YXIgST1jLngtby5wb3NpdGlvbi54LHo9Yy55LW8ucG9zaXRpb24ueTtvLmZyb250UG9zaXRpb249e3g6SSx5Onp9LGkuZGF0YU9ubHl8fChvLnVpLmZyb250LnN0eWxlLnRyYW5zZm9ybT1cInRyYW5zbGF0ZShcIitJK1wicHgsXCIreitcInB4KVwiKTt2YXIgRD17aWRlbnRpZmllcjpvLmlkZW50aWZpZXIscG9zaXRpb246Yyxmb3JjZTpULHByZXNzdXJlOnQuZm9yY2V8fHQucHJlc3N1cmV8fHQud2Via2l0Rm9yY2V8fDAsZGlzdGFuY2U6TyxhbmdsZTp7cmFkaWFuOl8sZGVncmVlOnd9LHZlY3Rvcjp7eDpJL3AseTotei9wfSxyYXc6ayxpbnN0YW5jZTpvLGxvY2tYOmkubG9ja1gsbG9ja1k6aS5sb2NrWX07KEQ9by5jb21wdXRlRGlyZWN0aW9uKEQpKS5hbmdsZT17cmFkaWFuOnMoMTgwLXcpLGRlZ3JlZToxODAtd30sby50cmlnZ2VyKFwibW92ZVwiLEQpLHRoaXMudHJpZ2dlcihcIm1vdmUgXCIrby5pZCtcIjptb3ZlXCIsRCl9ZWxzZSB0aGlzLnByb2Nlc3NPbkVuZCh0KX0sRS5wcm90b3R5cGUucHJvY2Vzc09uRW5kPWZ1bmN0aW9uKHQpe3ZhciBpPXRoaXMsZT1pLm9wdGlvbnMsbz1pLm1hbmFnZXIuZ2V0SWRlbnRpZmllcih0KSxuPWkubmlwcGxlcy5nZXQobykscz1pLm1hbmFnZXIucmVtb3ZlSWRlbnRpZmllcihuLmlkZW50aWZpZXIpO24mJihlLmRhdGFPbmx5fHxuLmhpZGUoZnVuY3Rpb24oKXtcImR5bmFtaWNcIj09PWUubW9kZSYmKG4udHJpZ2dlcihcInJlbW92ZWRcIixuKSxpLnRyaWdnZXIoXCJyZW1vdmVkIFwiK24uaWQrXCI6cmVtb3ZlZFwiLG4pLGkubWFuYWdlci50cmlnZ2VyKFwicmVtb3ZlZCBcIituLmlkK1wiOnJlbW92ZWRcIixuKSxuLmRlc3Ryb3koKSl9KSxjbGVhckludGVydmFsKGkucHJlc3N1cmVJbnRlcnZhbHNbbi5pZGVudGlmaWVyXSksbi5yZXNldERpcmVjdGlvbigpLG4udHJpZ2dlcihcImVuZFwiLG4pLGkudHJpZ2dlcihcImVuZCBcIituLmlkK1wiOmVuZFwiLG4pLGkuaWRzLmluZGV4T2Yobi5pZGVudGlmaWVyKT49MCYmaS5pZHMuc3BsaWNlKGkuaWRzLmluZGV4T2Yobi5pZGVudGlmaWVyKSwxKSxpLmFjdGl2ZXMuaW5kZXhPZihuKT49MCYmaS5hY3RpdmVzLnNwbGljZShpLmFjdGl2ZXMuaW5kZXhPZihuKSwxKSwvKHNlbWl8c3RhdGljKS8udGVzdChlLm1vZGUpP2kuaWRsZXMucHVzaChuKTppLm5pcHBsZXMuaW5kZXhPZihuKT49MCYmaS5uaXBwbGVzLnNwbGljZShpLm5pcHBsZXMuaW5kZXhPZihuKSwxKSxpLm1hbmFnZXIudW5iaW5kRG9jdW1lbnQoKSwvKHNlbWl8c3RhdGljKS8udGVzdChlLm1vZGUpJiYoaS5tYW5hZ2VyLmlkc1tzLmlkXT1zLmlkZW50aWZpZXIpKX0sRS5wcm90b3R5cGUub25EZXN0cm95ZWQ9ZnVuY3Rpb24odCxpKXt0aGlzLm5pcHBsZXMuaW5kZXhPZihpKT49MCYmdGhpcy5uaXBwbGVzLnNwbGljZSh0aGlzLm5pcHBsZXMuaW5kZXhPZihpKSwxKSx0aGlzLmFjdGl2ZXMuaW5kZXhPZihpKT49MCYmdGhpcy5hY3RpdmVzLnNwbGljZSh0aGlzLmFjdGl2ZXMuaW5kZXhPZihpKSwxKSx0aGlzLmlkbGVzLmluZGV4T2YoaSk+PTAmJnRoaXMuaWRsZXMuc3BsaWNlKHRoaXMuaWRsZXMuaW5kZXhPZihpKSwxKSx0aGlzLmlkcy5pbmRleE9mKGkuaWRlbnRpZmllcik+PTAmJnRoaXMuaWRzLnNwbGljZSh0aGlzLmlkcy5pbmRleE9mKGkuaWRlbnRpZmllciksMSksdGhpcy5tYW5hZ2VyLnJlbW92ZUlkZW50aWZpZXIoaS5pZGVudGlmaWVyKSx0aGlzLm1hbmFnZXIudW5iaW5kRG9jdW1lbnQoKX0sRS5wcm90b3R5cGUuZGVzdHJveT1mdW5jdGlvbigpe2Zvcih2YXIgdCBpbiB0aGlzLnVuYmluZEV2dCh0aGlzLm9wdGlvbnMuem9uZSxcInN0YXJ0XCIpLHRoaXMubmlwcGxlcy5mb3JFYWNoKGZ1bmN0aW9uKHQpe3QuZGVzdHJveSgpfSksdGhpcy5wcmVzc3VyZUludGVydmFscyl0aGlzLnByZXNzdXJlSW50ZXJ2YWxzLmhhc093blByb3BlcnR5KHQpJiZjbGVhckludGVydmFsKHRoaXMucHJlc3N1cmVJbnRlcnZhbHNbdF0pO3RoaXMudHJpZ2dlcihcImRlc3Ryb3llZFwiLHRoaXMubmlwcGxlcyksdGhpcy5tYW5hZ2VyLnVuYmluZERvY3VtZW50KCksdGhpcy5vZmYoKX07dmFyIEk9RTtmdW5jdGlvbiB6KHQpe3ZhciBpPXRoaXM7aS5pZHM9e30saS5pbmRleD0wLGkuY29sbGVjdGlvbnM9W10saS5zY3JvbGw9aCgpLGkuY29uZmlnKHQpLGkucHJlcGFyZUNvbGxlY3Rpb25zKCk7dmFyIGU9ZnVuY3Rpb24oKXt2YXIgdDtpLmNvbGxlY3Rpb25zLmZvckVhY2goZnVuY3Rpb24oZSl7ZS5mb3JFYWNoKGZ1bmN0aW9uKGUpe3Q9ZS5lbC5nZXRCb3VuZGluZ0NsaWVudFJlY3QoKSxlLnBvc2l0aW9uPXt4Omkuc2Nyb2xsLngrdC5sZWZ0LHk6aS5zY3JvbGwueSt0LnRvcH19KX0pfTtwKHdpbmRvdyxcInJlc2l6ZVwiLGZ1bmN0aW9uKCl7YShlKX0pO3ZhciBvPWZ1bmN0aW9uKCl7aS5zY3JvbGw9aCgpfTtyZXR1cm4gcCh3aW5kb3csXCJzY3JvbGxcIixmdW5jdGlvbigpe2Eobyl9KSxpLmNvbGxlY3Rpb25zfXoucHJvdG90eXBlPW5ldyBULHouY29uc3RydWN0b3I9eix6LnByb3RvdHlwZS5wcmVwYXJlQ29sbGVjdGlvbnM9ZnVuY3Rpb24oKXt2YXIgdD10aGlzO3QuY29sbGVjdGlvbnMuY3JlYXRlPXQuY3JlYXRlLmJpbmQodCksdC5jb2xsZWN0aW9ucy5vbj10Lm9uLmJpbmQodCksdC5jb2xsZWN0aW9ucy5vZmY9dC5vZmYuYmluZCh0KSx0LmNvbGxlY3Rpb25zLmRlc3Ryb3k9dC5kZXN0cm95LmJpbmQodCksdC5jb2xsZWN0aW9ucy5nZXQ9ZnVuY3Rpb24oaSl7dmFyIGU7cmV0dXJuIHQuY29sbGVjdGlvbnMuZXZlcnkoZnVuY3Rpb24odCl7cmV0dXJuIShlPXQuZ2V0KGkpKX0pLGV9fSx6LnByb3RvdHlwZS5jcmVhdGU9ZnVuY3Rpb24odCl7cmV0dXJuIHRoaXMuY3JlYXRlQ29sbGVjdGlvbih0KX0sei5wcm90b3R5cGUuY3JlYXRlQ29sbGVjdGlvbj1mdW5jdGlvbih0KXt2YXIgaT1uZXcgSSh0aGlzLHQpO3JldHVybiB0aGlzLmJpbmRDb2xsZWN0aW9uKGkpLHRoaXMuY29sbGVjdGlvbnMucHVzaChpKSxpfSx6LnByb3RvdHlwZS5iaW5kQ29sbGVjdGlvbj1mdW5jdGlvbih0KXt2YXIgaSxlPXRoaXMsbz1mdW5jdGlvbih0LG8pe2k9dC50eXBlK1wiIFwiK28uaWQrXCI6XCIrdC50eXBlLGUudHJpZ2dlcihpLG8pfTt0Lm9uKFwiZGVzdHJveWVkXCIsZS5vbkRlc3Ryb3llZC5iaW5kKGUpKSx0Lm9uKFwic2hvd24gaGlkZGVuIHJlc3RlZCBkaXIgcGxhaW5cIixvKSx0Lm9uKFwiZGlyOnVwIGRpcjpyaWdodCBkaXI6ZG93biBkaXI6bGVmdFwiLG8pLHQub24oXCJwbGFpbjp1cCBwbGFpbjpyaWdodCBwbGFpbjpkb3duIHBsYWluOmxlZnRcIixvKX0sei5wcm90b3R5cGUuYmluZERvY3VtZW50PWZ1bmN0aW9uKCl7dGhpcy5iaW5kZWR8fCh0aGlzLmJpbmRFdnQoZG9jdW1lbnQsXCJtb3ZlXCIpLmJpbmRFdnQoZG9jdW1lbnQsXCJlbmRcIiksdGhpcy5iaW5kZWQ9ITApfSx6LnByb3RvdHlwZS51bmJpbmREb2N1bWVudD1mdW5jdGlvbih0KXtPYmplY3Qua2V5cyh0aGlzLmlkcykubGVuZ3RoJiYhMCE9PXR8fCh0aGlzLnVuYmluZEV2dChkb2N1bWVudCxcIm1vdmVcIikudW5iaW5kRXZ0KGRvY3VtZW50LFwiZW5kXCIpLHRoaXMuYmluZGVkPSExKX0sei5wcm90b3R5cGUuZ2V0SWRlbnRpZmllcj1mdW5jdGlvbih0KXt2YXIgaTtyZXR1cm4gdD92b2lkIDA9PT0oaT12b2lkIDA9PT10LmlkZW50aWZpZXI/dC5wb2ludGVySWQ6dC5pZGVudGlmaWVyKSYmKGk9dGhpcy5sYXRlc3R8fDApOmk9dGhpcy5pbmRleCx2b2lkIDA9PT10aGlzLmlkc1tpXSYmKHRoaXMuaWRzW2ldPXRoaXMuaW5kZXgsdGhpcy5pbmRleCs9MSksdGhpcy5sYXRlc3Q9aSx0aGlzLmlkc1tpXX0sei5wcm90b3R5cGUucmVtb3ZlSWRlbnRpZmllcj1mdW5jdGlvbih0KXt2YXIgaT17fTtmb3IodmFyIGUgaW4gdGhpcy5pZHMpaWYodGhpcy5pZHNbZV09PT10KXtpLmlkPWUsaS5pZGVudGlmaWVyPXRoaXMuaWRzW2VdLGRlbGV0ZSB0aGlzLmlkc1tlXTticmVha31yZXR1cm4gaX0sei5wcm90b3R5cGUub25tb3ZlPWZ1bmN0aW9uKHQpe3JldHVybiB0aGlzLm9uQW55KFwibW92ZVwiLHQpLCExfSx6LnByb3RvdHlwZS5vbmVuZD1mdW5jdGlvbih0KXtyZXR1cm4gdGhpcy5vbkFueShcImVuZFwiLHQpLCExfSx6LnByb3RvdHlwZS5vbmNhbmNlbD1mdW5jdGlvbih0KXtyZXR1cm4gdGhpcy5vbkFueShcImVuZFwiLHQpLCExfSx6LnByb3RvdHlwZS5vbkFueT1mdW5jdGlvbih0LGkpe3ZhciBlLG89dGhpcyxuPVwicHJvY2Vzc09uXCIrdC5jaGFyQXQoMCkudG9VcHBlckNhc2UoKSt0LnNsaWNlKDEpO2k9bChpKTtyZXR1cm4gdihpLGZ1bmN0aW9uKHQpe2U9by5nZXRJZGVudGlmaWVyKHQpLHYoby5jb2xsZWN0aW9ucyxmdW5jdGlvbih0LGksZSl7ZS5pZHMuaW5kZXhPZihpKT49MCYmKGVbbl0odCksdC5fZm91bmRfPSEwKX0uYmluZChudWxsLHQsZSkpLHQuX2ZvdW5kX3x8by5yZW1vdmVJZGVudGlmaWVyKGUpfSksITF9LHoucHJvdG90eXBlLmRlc3Ryb3k9ZnVuY3Rpb24oKXt0aGlzLnVuYmluZERvY3VtZW50KCEwKSx0aGlzLmlkcz17fSx0aGlzLmluZGV4PTAsdGhpcy5jb2xsZWN0aW9ucy5mb3JFYWNoKGZ1bmN0aW9uKHQpe3QuZGVzdHJveSgpfSksdGhpcy5vZmYoKX0sei5wcm90b3R5cGUub25EZXN0cm95ZWQ9ZnVuY3Rpb24odCxpKXtpZih0aGlzLmNvbGxlY3Rpb25zLmluZGV4T2YoaSk8MClyZXR1cm4hMTt0aGlzLmNvbGxlY3Rpb25zLnNwbGljZSh0aGlzLmNvbGxlY3Rpb25zLmluZGV4T2YoaSksMSl9O3ZhciBEPW5ldyB6O2kuZGVmYXVsdD17Y3JlYXRlOmZ1bmN0aW9uKHQpe3JldHVybiBELmNyZWF0ZSh0KX0sZmFjdG9yeTpEfX1dKS5kZWZhdWx0fSk7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/nipplejs/dist/nipplejs.js\n"));

/***/ })

}]);