"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/InputManager.ts":
/*!*********************************!*\
  !*** ./src/lib/InputManager.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InputManager: () => (/* binding */ InputManager)\n/* harmony export */ });\nclass InputManager {\n    init() {\n        this.setupKeyboardControls();\n        this.setupMobileControls();\n    }\n    setupKeyboardControls() {\n        // Handle keydown events\n        document.addEventListener('keydown', (event)=>{\n            this.keys[event.code] = true;\n            this.handleKeyboardInput();\n        });\n        // Handle keyup events\n        document.addEventListener('keyup', (event)=>{\n            this.keys[event.code] = false;\n            this.handleKeyboardInput();\n        });\n    }\n    handleKeyboardInput() {\n        const leftPressed = this.keys['KeyA'] || this.keys['ArrowLeft'];\n        const rightPressed = this.keys['KeyD'] || this.keys['ArrowRight'];\n        if (leftPressed && !rightPressed) {\n            var _this_onMove, _this;\n            (_this_onMove = (_this = this).onMove) === null || _this_onMove === void 0 ? void 0 : _this_onMove.call(_this, 'left');\n        } else if (rightPressed && !leftPressed) {\n            var _this_onMove1, _this1;\n            (_this_onMove1 = (_this1 = this).onMove) === null || _this_onMove1 === void 0 ? void 0 : _this_onMove1.call(_this1, 'right');\n        } else {\n            var _this_onMove2, _this2;\n            (_this_onMove2 = (_this2 = this).onMove) === null || _this_onMove2 === void 0 ? void 0 : _this_onMove2.call(_this2, 'stop');\n        }\n    }\n    async setupMobileControls() {\n        // Only run on client side\n        if (false) {}\n        // Only show mobile controls on touch devices\n        if (!('ontouchstart' in window)) {\n            return;\n        }\n        const joystickContainer = document.getElementById('mobile-joystick');\n        if (!joystickContainer) {\n            console.warn('Mobile joystick container not found');\n            return;\n        }\n        try {\n            // Dynamic import to avoid SSR issues\n            const nipplejs = (await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_nipplejs_dist_nipplejs_js\").then(__webpack_require__.t.bind(__webpack_require__, /*! nipplejs */ \"(app-pages-browser)/./node_modules/nipplejs/dist/nipplejs.js\", 23))).default;\n            // Create virtual joystick\n            this.joystick = nipplejs.create({\n                zone: joystickContainer,\n                mode: 'static',\n                position: {\n                    left: '50px',\n                    bottom: '50px'\n                },\n                color: 'white',\n                size: 100,\n                threshold: 0.1\n            });\n            // Handle joystick events\n            this.joystick.on('move', (evt, data)=>{\n                if (data.direction) {\n                    if (data.direction.x === 'left') {\n                        var _this_onMove, _this;\n                        (_this_onMove = (_this = this).onMove) === null || _this_onMove === void 0 ? void 0 : _this_onMove.call(_this, 'left');\n                    } else if (data.direction.x === 'right') {\n                        var _this_onMove1, _this1;\n                        (_this_onMove1 = (_this1 = this).onMove) === null || _this_onMove1 === void 0 ? void 0 : _this_onMove1.call(_this1, 'right');\n                    }\n                }\n            });\n            this.joystick.on('end', ()=>{\n                var _this_onMove, _this;\n                (_this_onMove = (_this = this).onMove) === null || _this_onMove === void 0 ? void 0 : _this_onMove.call(_this, 'stop');\n            });\n        } catch (error) {\n            console.error('Failed to load mobile controls:', error);\n        }\n    }\n    destroy() {\n        // Remove keyboard event listeners\n        document.removeEventListener('keydown', this.handleKeyboardInput);\n        document.removeEventListener('keyup', this.handleKeyboardInput);\n        // Destroy joystick\n        if (this.joystick) {\n            this.joystick.destroy();\n        }\n    }\n    constructor(){\n        this.keys = {};\n        this.joystick = null;\n        this.onMove = null;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/InputManager.ts\n"));

/***/ })

});