"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/InputManager.ts":
/*!*********************************!*\
  !*** ./src/lib/InputManager.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InputManager: () => (/* binding */ InputManager)\n/* harmony export */ });\nclass InputManager {\n    init() {\n        this.setupKeyboardControls();\n        this.setupMobileControls();\n    }\n    setupKeyboardControls() {\n        // Only run on client side\n        if (false) {}\n        // Handle keydown events\n        document.addEventListener('keydown', (event)=>{\n            this.keys[event.code] = true;\n            this.handleKeyboardInput();\n        });\n        // Handle keyup events\n        document.addEventListener('keyup', (event)=>{\n            this.keys[event.code] = false;\n            this.handleKeyboardInput();\n        });\n    }\n    handleKeyboardInput() {\n        const leftPressed = this.keys['KeyA'] || this.keys['ArrowLeft'];\n        const rightPressed = this.keys['KeyD'] || this.keys['ArrowRight'];\n        if (leftPressed && !rightPressed) {\n            var _this_onMove, _this;\n            (_this_onMove = (_this = this).onMove) === null || _this_onMove === void 0 ? void 0 : _this_onMove.call(_this, 'left');\n        } else if (rightPressed && !leftPressed) {\n            var _this_onMove1, _this1;\n            (_this_onMove1 = (_this1 = this).onMove) === null || _this_onMove1 === void 0 ? void 0 : _this_onMove1.call(_this1, 'right');\n        } else {\n            var _this_onMove2, _this2;\n            (_this_onMove2 = (_this2 = this).onMove) === null || _this_onMove2 === void 0 ? void 0 : _this_onMove2.call(_this2, 'stop');\n        }\n    }\n    async setupMobileControls() {\n        // Only run on client side\n        if (false) {}\n        // Only show mobile controls on touch devices\n        if (!('ontouchstart' in window)) {\n            return;\n        }\n        const joystickContainer = document.getElementById('mobile-joystick');\n        if (!joystickContainer) {\n            console.warn('Mobile joystick container not found');\n            return;\n        }\n        try {\n            // Dynamic import to avoid SSR issues\n            const nipplejs = (await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_nipplejs_dist_nipplejs_js\").then(__webpack_require__.t.bind(__webpack_require__, /*! nipplejs */ \"(app-pages-browser)/./node_modules/nipplejs/dist/nipplejs.js\", 23))).default;\n            // Create virtual joystick\n            this.joystick = nipplejs.create({\n                zone: joystickContainer,\n                mode: 'static',\n                position: {\n                    left: '50px',\n                    bottom: '50px'\n                },\n                color: 'white',\n                size: 100,\n                threshold: 0.1\n            });\n            // Handle joystick events\n            this.joystick.on('move', (evt, data)=>{\n                if (data.direction) {\n                    if (data.direction.x === 'left') {\n                        var _this_onMove, _this;\n                        (_this_onMove = (_this = this).onMove) === null || _this_onMove === void 0 ? void 0 : _this_onMove.call(_this, 'left');\n                    } else if (data.direction.x === 'right') {\n                        var _this_onMove1, _this1;\n                        (_this_onMove1 = (_this1 = this).onMove) === null || _this_onMove1 === void 0 ? void 0 : _this_onMove1.call(_this1, 'right');\n                    }\n                }\n            });\n            this.joystick.on('end', ()=>{\n                var _this_onMove, _this;\n                (_this_onMove = (_this = this).onMove) === null || _this_onMove === void 0 ? void 0 : _this_onMove.call(_this, 'stop');\n            });\n        } catch (error) {\n            console.error('Failed to load mobile controls:', error);\n        }\n    }\n    destroy() {\n        // Remove keyboard event listeners\n        document.removeEventListener('keydown', this.handleKeyboardInput);\n        document.removeEventListener('keyup', this.handleKeyboardInput);\n        // Destroy joystick\n        if (this.joystick) {\n            this.joystick.destroy();\n        }\n    }\n    constructor(){\n        this.keys = {};\n        this.joystick = null;\n        this.onMove = null;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvSW5wdXRNYW5hZ2VyLnRzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTyxNQUFNQTtJQUtYQyxPQUFPO1FBQ0wsSUFBSSxDQUFDQyxxQkFBcUI7UUFDMUIsSUFBSSxDQUFDQyxtQkFBbUI7SUFDMUI7SUFFUUQsd0JBQXdCO1FBQzlCLDBCQUEwQjtRQUMxQixJQUFJLEtBQTZCLEVBQUUsRUFFbEM7UUFFRCx3QkFBd0I7UUFDeEJFLFNBQVNDLGdCQUFnQixDQUFDLFdBQVcsQ0FBQ0M7WUFDcEMsSUFBSSxDQUFDQyxJQUFJLENBQUNELE1BQU1FLElBQUksQ0FBQyxHQUFHO1lBQ3hCLElBQUksQ0FBQ0MsbUJBQW1CO1FBQzFCO1FBRUEsc0JBQXNCO1FBQ3RCTCxTQUFTQyxnQkFBZ0IsQ0FBQyxTQUFTLENBQUNDO1lBQ2xDLElBQUksQ0FBQ0MsSUFBSSxDQUFDRCxNQUFNRSxJQUFJLENBQUMsR0FBRztZQUN4QixJQUFJLENBQUNDLG1CQUFtQjtRQUMxQjtJQUNGO0lBRVFBLHNCQUFzQjtRQUM1QixNQUFNQyxjQUFjLElBQUksQ0FBQ0gsSUFBSSxDQUFDLE9BQU8sSUFBSSxJQUFJLENBQUNBLElBQUksQ0FBQyxZQUFZO1FBQy9ELE1BQU1JLGVBQWUsSUFBSSxDQUFDSixJQUFJLENBQUMsT0FBTyxJQUFJLElBQUksQ0FBQ0EsSUFBSSxDQUFDLGFBQWE7UUFFakUsSUFBSUcsZUFBZSxDQUFDQyxjQUFjO2dCQUNoQzthQUFBLDRCQUFJLEVBQUNDLE1BQU0sY0FBWCw0REFBYztRQUNoQixPQUFPLElBQUlELGdCQUFnQixDQUFDRCxhQUFhO2dCQUN2QzthQUFBLDhCQUFJLEVBQUNFLE1BQU0sY0FBWCwrREFBYztRQUNoQixPQUFPO2dCQUNMO2FBQUEsOEJBQUksRUFBQ0EsTUFBTSxjQUFYLCtEQUFjO1FBQ2hCO0lBQ0Y7SUFFQSxNQUFjVCxzQkFBc0I7UUFDbEMsMEJBQTBCO1FBQzFCLElBQUksS0FBNkIsRUFBRSxFQUVsQztRQUVELDZDQUE2QztRQUM3QyxJQUFJLENBQUUsbUJBQWtCVSxNQUFLLEdBQUk7WUFDL0I7UUFDRjtRQUVBLE1BQU1DLG9CQUFvQlYsU0FBU1csY0FBYyxDQUFDO1FBQ2xELElBQUksQ0FBQ0QsbUJBQW1CO1lBQ3RCRSxRQUFRQyxJQUFJLENBQUM7WUFDYjtRQUNGO1FBRUEsSUFBSTtZQUNGLHFDQUFxQztZQUNyQyxNQUFNQyxXQUFXLENBQUMsTUFBTSw0T0FBaUIsRUFBR0MsT0FBTztZQUVuRCwwQkFBMEI7WUFDMUIsSUFBSSxDQUFDQyxRQUFRLEdBQUdGLFNBQVNHLE1BQU0sQ0FBQztnQkFDOUJDLE1BQU1SO2dCQUNOUyxNQUFNO2dCQUNOQyxVQUFVO29CQUFFQyxNQUFNO29CQUFRQyxRQUFRO2dCQUFPO2dCQUN6Q0MsT0FBTztnQkFDUEMsTUFBTTtnQkFDTkMsV0FBVztZQUNiO1lBRUEseUJBQXlCO1lBQ3pCLElBQUksQ0FBQ1QsUUFBUSxDQUFDVSxFQUFFLENBQUMsUUFBUSxDQUFDQyxLQUFVQztnQkFDbEMsSUFBSUEsS0FBS0MsU0FBUyxFQUFFO29CQUNsQixJQUFJRCxLQUFLQyxTQUFTLENBQUNDLENBQUMsS0FBSyxRQUFROzRCQUMvQjt5QkFBQSw0QkFBSSxFQUFDdEIsTUFBTSxjQUFYLDREQUFjO29CQUNoQixPQUFPLElBQUlvQixLQUFLQyxTQUFTLENBQUNDLENBQUMsS0FBSyxTQUFTOzRCQUN2Qzt5QkFBQSw4QkFBSSxFQUFDdEIsTUFBTSxjQUFYLCtEQUFjO29CQUNoQjtnQkFDRjtZQUNGO1lBRUEsSUFBSSxDQUFDUSxRQUFRLENBQUNVLEVBQUUsQ0FBQyxPQUFPO29CQUN0QjtpQkFBQSw0QkFBSSxFQUFDbEIsTUFBTSxjQUFYLDREQUFjO1lBQ2hCO1FBQ0YsRUFBRSxPQUFPdUIsT0FBTztZQUNkbkIsUUFBUW1CLEtBQUssQ0FBQyxtQ0FBbUNBO1FBQ25EO0lBQ0Y7SUFFQUMsVUFBVTtRQUNSLGtDQUFrQztRQUNsQ2hDLFNBQVNpQyxtQkFBbUIsQ0FBQyxXQUFXLElBQUksQ0FBQzVCLG1CQUFtQjtRQUNoRUwsU0FBU2lDLG1CQUFtQixDQUFDLFNBQVMsSUFBSSxDQUFDNUIsbUJBQW1CO1FBRTlELG1CQUFtQjtRQUNuQixJQUFJLElBQUksQ0FBQ1csUUFBUSxFQUFFO1lBQ2pCLElBQUksQ0FBQ0EsUUFBUSxDQUFDZ0IsT0FBTztRQUN2QjtJQUNGOzthQXBHUTdCLE9BQW1DLENBQUM7YUFDcENhLFdBQWdCO2FBQ2pCUixTQUFrRTs7QUFtRzNFIiwic291cmNlcyI6WyIvVXNlcnMveXVzdWYvRGVza3RvcC9wb3J0Zm9saW8vc3JjL2xpYi9JbnB1dE1hbmFnZXIudHMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNsYXNzIElucHV0TWFuYWdlciB7XG4gIHByaXZhdGUga2V5czogeyBba2V5OiBzdHJpbmddOiBib29sZWFuIH0gPSB7fVxuICBwcml2YXRlIGpveXN0aWNrOiBhbnkgPSBudWxsXG4gIHB1YmxpYyBvbk1vdmU6ICgoZGlyZWN0aW9uOiAnbGVmdCcgfCAncmlnaHQnIHwgJ3N0b3AnKSA9PiB2b2lkKSB8IG51bGwgPSBudWxsXG5cbiAgaW5pdCgpIHtcbiAgICB0aGlzLnNldHVwS2V5Ym9hcmRDb250cm9scygpXG4gICAgdGhpcy5zZXR1cE1vYmlsZUNvbnRyb2xzKClcbiAgfVxuXG4gIHByaXZhdGUgc2V0dXBLZXlib2FyZENvbnRyb2xzKCkge1xuICAgIC8vIE9ubHkgcnVuIG9uIGNsaWVudCBzaWRlXG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICByZXR1cm5cbiAgICB9XG5cbiAgICAvLyBIYW5kbGUga2V5ZG93biBldmVudHNcbiAgICBkb2N1bWVudC5hZGRFdmVudExpc3RlbmVyKCdrZXlkb3duJywgKGV2ZW50KSA9PiB7XG4gICAgICB0aGlzLmtleXNbZXZlbnQuY29kZV0gPSB0cnVlXG4gICAgICB0aGlzLmhhbmRsZUtleWJvYXJkSW5wdXQoKVxuICAgIH0pXG5cbiAgICAvLyBIYW5kbGUga2V5dXAgZXZlbnRzXG4gICAgZG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcigna2V5dXAnLCAoZXZlbnQpID0+IHtcbiAgICAgIHRoaXMua2V5c1tldmVudC5jb2RlXSA9IGZhbHNlXG4gICAgICB0aGlzLmhhbmRsZUtleWJvYXJkSW5wdXQoKVxuICAgIH0pXG4gIH1cblxuICBwcml2YXRlIGhhbmRsZUtleWJvYXJkSW5wdXQoKSB7XG4gICAgY29uc3QgbGVmdFByZXNzZWQgPSB0aGlzLmtleXNbJ0tleUEnXSB8fCB0aGlzLmtleXNbJ0Fycm93TGVmdCddXG4gICAgY29uc3QgcmlnaHRQcmVzc2VkID0gdGhpcy5rZXlzWydLZXlEJ10gfHwgdGhpcy5rZXlzWydBcnJvd1JpZ2h0J11cblxuICAgIGlmIChsZWZ0UHJlc3NlZCAmJiAhcmlnaHRQcmVzc2VkKSB7XG4gICAgICB0aGlzLm9uTW92ZT8uKCdsZWZ0JylcbiAgICB9IGVsc2UgaWYgKHJpZ2h0UHJlc3NlZCAmJiAhbGVmdFByZXNzZWQpIHtcbiAgICAgIHRoaXMub25Nb3ZlPy4oJ3JpZ2h0JylcbiAgICB9IGVsc2Uge1xuICAgICAgdGhpcy5vbk1vdmU/Lignc3RvcCcpXG4gICAgfVxuICB9XG5cbiAgcHJpdmF0ZSBhc3luYyBzZXR1cE1vYmlsZUNvbnRyb2xzKCkge1xuICAgIC8vIE9ubHkgcnVuIG9uIGNsaWVudCBzaWRlXG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICByZXR1cm5cbiAgICB9XG5cbiAgICAvLyBPbmx5IHNob3cgbW9iaWxlIGNvbnRyb2xzIG9uIHRvdWNoIGRldmljZXNcbiAgICBpZiAoISgnb250b3VjaHN0YXJ0JyBpbiB3aW5kb3cpKSB7XG4gICAgICByZXR1cm5cbiAgICB9XG5cbiAgICBjb25zdCBqb3lzdGlja0NvbnRhaW5lciA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdtb2JpbGUtam95c3RpY2snKVxuICAgIGlmICgham95c3RpY2tDb250YWluZXIpIHtcbiAgICAgIGNvbnNvbGUud2FybignTW9iaWxlIGpveXN0aWNrIGNvbnRhaW5lciBub3QgZm91bmQnKVxuICAgICAgcmV0dXJuXG4gICAgfVxuXG4gICAgdHJ5IHtcbiAgICAgIC8vIER5bmFtaWMgaW1wb3J0IHRvIGF2b2lkIFNTUiBpc3N1ZXNcbiAgICAgIGNvbnN0IG5pcHBsZWpzID0gKGF3YWl0IGltcG9ydCgnbmlwcGxlanMnKSkuZGVmYXVsdFxuXG4gICAgICAvLyBDcmVhdGUgdmlydHVhbCBqb3lzdGlja1xuICAgICAgdGhpcy5qb3lzdGljayA9IG5pcHBsZWpzLmNyZWF0ZSh7XG4gICAgICAgIHpvbmU6IGpveXN0aWNrQ29udGFpbmVyLFxuICAgICAgICBtb2RlOiAnc3RhdGljJyxcbiAgICAgICAgcG9zaXRpb246IHsgbGVmdDogJzUwcHgnLCBib3R0b206ICc1MHB4JyB9LFxuICAgICAgICBjb2xvcjogJ3doaXRlJyxcbiAgICAgICAgc2l6ZTogMTAwLFxuICAgICAgICB0aHJlc2hvbGQ6IDAuMSxcbiAgICAgIH0pXG5cbiAgICAgIC8vIEhhbmRsZSBqb3lzdGljayBldmVudHNcbiAgICAgIHRoaXMuam95c3RpY2sub24oJ21vdmUnLCAoZXZ0OiBhbnksIGRhdGE6IGFueSkgPT4ge1xuICAgICAgICBpZiAoZGF0YS5kaXJlY3Rpb24pIHtcbiAgICAgICAgICBpZiAoZGF0YS5kaXJlY3Rpb24ueCA9PT0gJ2xlZnQnKSB7XG4gICAgICAgICAgICB0aGlzLm9uTW92ZT8uKCdsZWZ0JylcbiAgICAgICAgICB9IGVsc2UgaWYgKGRhdGEuZGlyZWN0aW9uLnggPT09ICdyaWdodCcpIHtcbiAgICAgICAgICAgIHRoaXMub25Nb3ZlPy4oJ3JpZ2h0JylcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH0pXG5cbiAgICAgIHRoaXMuam95c3RpY2sub24oJ2VuZCcsICgpID0+IHtcbiAgICAgICAgdGhpcy5vbk1vdmU/Lignc3RvcCcpXG4gICAgICB9KVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gbG9hZCBtb2JpbGUgY29udHJvbHM6JywgZXJyb3IpXG4gICAgfVxuICB9XG5cbiAgZGVzdHJveSgpIHtcbiAgICAvLyBSZW1vdmUga2V5Ym9hcmQgZXZlbnQgbGlzdGVuZXJzXG4gICAgZG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcigna2V5ZG93bicsIHRoaXMuaGFuZGxlS2V5Ym9hcmRJbnB1dClcbiAgICBkb2N1bWVudC5yZW1vdmVFdmVudExpc3RlbmVyKCdrZXl1cCcsIHRoaXMuaGFuZGxlS2V5Ym9hcmRJbnB1dClcblxuICAgIC8vIERlc3Ryb3kgam95c3RpY2tcbiAgICBpZiAodGhpcy5qb3lzdGljaykge1xuICAgICAgdGhpcy5qb3lzdGljay5kZXN0cm95KClcbiAgICB9XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJJbnB1dE1hbmFnZXIiLCJpbml0Iiwic2V0dXBLZXlib2FyZENvbnRyb2xzIiwic2V0dXBNb2JpbGVDb250cm9scyIsImRvY3VtZW50IiwiYWRkRXZlbnRMaXN0ZW5lciIsImV2ZW50Iiwia2V5cyIsImNvZGUiLCJoYW5kbGVLZXlib2FyZElucHV0IiwibGVmdFByZXNzZWQiLCJyaWdodFByZXNzZWQiLCJvbk1vdmUiLCJ3aW5kb3ciLCJqb3lzdGlja0NvbnRhaW5lciIsImdldEVsZW1lbnRCeUlkIiwiY29uc29sZSIsIndhcm4iLCJuaXBwbGVqcyIsImRlZmF1bHQiLCJqb3lzdGljayIsImNyZWF0ZSIsInpvbmUiLCJtb2RlIiwicG9zaXRpb24iLCJsZWZ0IiwiYm90dG9tIiwiY29sb3IiLCJzaXplIiwidGhyZXNob2xkIiwib24iLCJldnQiLCJkYXRhIiwiZGlyZWN0aW9uIiwieCIsImVycm9yIiwiZGVzdHJveSIsInJlbW92ZUV2ZW50TGlzdGVuZXIiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/InputManager.ts\n"));

/***/ })

});