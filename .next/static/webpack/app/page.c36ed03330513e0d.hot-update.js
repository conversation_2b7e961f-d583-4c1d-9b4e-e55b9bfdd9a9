"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/Background.ts":
/*!*******************************!*\
  !*** ./src/lib/Background.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Background: () => (/* binding */ Background)\n/* harmony export */ });\n/* harmony import */ var pixi_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pixi.js */ \"(app-pages-browser)/./node_modules/pixi.js/lib/index.mjs\");\n\nclass Background {\n    async init() {\n        // Load all background textures\n        const textures = {};\n        for (const layer of this.backgroundLayers){\n            try {\n                textures[layer.name] = await pixi_js__WEBPACK_IMPORTED_MODULE_0__.Assets.load(layer.path);\n            } catch (error) {\n                console.warn(\"Failed to load background layer: \".concat(layer.path), error);\n                // Create a fallback colored rectangle\n                const graphics = new pixi_js__WEBPACK_IMPORTED_MODULE_0__.Graphics();\n                graphics.rect(0, 0, 800, 600);\n                graphics.fill(0x333333);\n                textures[layer.name] = pixi_js__WEBPACK_IMPORTED_MODULE_0__.Texture.from(graphics);\n            }\n        }\n        // Create sprites for each layer\n        for(let i = 0; i < this.backgroundLayers.length; i++){\n            const layerConfig = this.backgroundLayers[i];\n            const texture = textures[layerConfig.name];\n            if (texture) {\n                const sprite = new pixi_js__WEBPACK_IMPORTED_MODULE_0__.Sprite(texture);\n                sprite.name = layerConfig.name;\n                // Scale to fit screen while maintaining aspect ratio\n                this.scaleToFit(sprite);\n                // Position at bottom for ground layers\n                if (layerConfig.name === 'grass_road') {\n                    const screenHeight =  true ? window.innerHeight : 0;\n                    sprite.y = screenHeight - sprite.height;\n                    this.groundLevel = sprite.y;\n                }\n                this.layers.push(sprite);\n                this.container.addChild(sprite);\n            }\n        }\n    }\n    scaleToFit(sprite) {\n        const screenWidth =  true ? window.innerWidth : 0;\n        const screenHeight =  true ? window.innerHeight : 0;\n        // Scale to cover the screen width\n        const scaleX = screenWidth / sprite.texture.width;\n        const scaleY = screenHeight / sprite.texture.height;\n        // Use the larger scale to ensure full coverage\n        const scale = Math.max(scaleX, scaleY);\n        sprite.scale.set(scale);\n        // Center horizontally\n        sprite.x = (screenWidth - sprite.width) / 2;\n    }\n    resize(width, height) {\n        // Rescale all layers to new screen size\n        this.layers.forEach((sprite, index)=>{\n            this.scaleToFit(sprite);\n            // Reposition ground layer\n            if (this.backgroundLayers[index].name === 'grass_road') {\n                sprite.y = height - sprite.height;\n                this.groundLevel = sprite.y;\n            }\n        });\n    }\n    getGroundLevel() {\n        return this.groundLevel;\n    }\n    // Method to implement parallax scrolling if needed later\n    updateParallax(cameraX) {\n        this.layers.forEach((sprite, index)=>{\n            const parallaxFactor = this.backgroundLayers[index].parallax;\n            sprite.x = -cameraX * parallaxFactor;\n        });\n    }\n    constructor(){\n        this.layers = [];\n        this.groundLevel = 0;\n        // Background layer order (back to front)\n        this.backgroundLayers = [\n            {\n                name: 'sky',\n                path: '/background/sky.png',\n                parallax: 0.1\n            },\n            {\n                name: 'jungle_bg',\n                path: '/background/jungle_bg.png',\n                parallax: 0.3\n            },\n            {\n                name: 'trees_bushes',\n                path: '/background/trees&bushes.png',\n                parallax: 0.5\n            },\n            {\n                name: 'lianas',\n                path: '/background/lianas.png',\n                parallax: 0.7\n            },\n            {\n                name: 'grasses',\n                path: '/background/grasses.png',\n                parallax: 0.8\n            },\n            {\n                name: 'grass_road',\n                path: '/background/grass&road.png',\n                parallax: 1.0\n            }\n        ];\n        this.container = new pixi_js__WEBPACK_IMPORTED_MODULE_0__.Container();\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/Background.ts\n"));

/***/ })

});