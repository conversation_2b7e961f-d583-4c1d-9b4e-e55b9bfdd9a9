"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/InputManager.ts":
/*!*********************************!*\
  !*** ./src/lib/InputManager.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InputManager: () => (/* binding */ InputManager)\n/* harmony export */ });\nclass InputManager {\n    init() {\n        this.setupKeyboardControls();\n        this.setupMobileControls();\n    }\n    setupKeyboardControls() {\n        // Handle keydown events\n        document.addEventListener('keydown', (event)=>{\n            this.keys[event.code] = true;\n            this.handleKeyboardInput();\n        });\n        // Handle keyup events\n        document.addEventListener('keyup', (event)=>{\n            this.keys[event.code] = false;\n            this.handleKeyboardInput();\n        });\n    }\n    handleKeyboardInput() {\n        const leftPressed = this.keys['KeyA'] || this.keys['ArrowLeft'];\n        const rightPressed = this.keys['KeyD'] || this.keys['ArrowRight'];\n        if (leftPressed && !rightPressed) {\n            var _this_onMove, _this;\n            (_this_onMove = (_this = this).onMove) === null || _this_onMove === void 0 ? void 0 : _this_onMove.call(_this, 'left');\n        } else if (rightPressed && !leftPressed) {\n            var _this_onMove1, _this1;\n            (_this_onMove1 = (_this1 = this).onMove) === null || _this_onMove1 === void 0 ? void 0 : _this_onMove1.call(_this1, 'right');\n        } else {\n            var _this_onMove2, _this2;\n            (_this_onMove2 = (_this2 = this).onMove) === null || _this_onMove2 === void 0 ? void 0 : _this_onMove2.call(_this2, 'stop');\n        }\n    }\n    setupMobileControls() {\n        // Only show mobile controls on touch devices\n        if (!('ontouchstart' in window)) {\n            return;\n        }\n        const joystickContainer = document.getElementById('mobile-joystick');\n        if (!joystickContainer) {\n            console.warn('Mobile joystick container not found');\n            return;\n        }\n        // Create virtual joystick\n        this.joystick = nipplejs.create({\n            zone: joystickContainer,\n            mode: 'static',\n            position: {\n                left: '50px',\n                bottom: '50px'\n            },\n            color: 'white',\n            size: 100,\n            threshold: 0.1\n        });\n        // Handle joystick events\n        this.joystick.on('move', (evt, data)=>{\n            if (data.direction) {\n                if (data.direction.x === 'left') {\n                    var _this_onMove, _this;\n                    (_this_onMove = (_this = this).onMove) === null || _this_onMove === void 0 ? void 0 : _this_onMove.call(_this, 'left');\n                } else if (data.direction.x === 'right') {\n                    var _this_onMove1, _this1;\n                    (_this_onMove1 = (_this1 = this).onMove) === null || _this_onMove1 === void 0 ? void 0 : _this_onMove1.call(_this1, 'right');\n                }\n            }\n        });\n        this.joystick.on('end', ()=>{\n            var _this_onMove, _this;\n            (_this_onMove = (_this = this).onMove) === null || _this_onMove === void 0 ? void 0 : _this_onMove.call(_this, 'stop');\n        });\n    }\n    destroy() {\n        // Remove keyboard event listeners\n        document.removeEventListener('keydown', this.handleKeyboardInput);\n        document.removeEventListener('keyup', this.handleKeyboardInput);\n        // Destroy joystick\n        if (this.joystick) {\n            this.joystick.destroy();\n        }\n    }\n    constructor(){\n        this.keys = {};\n        this.joystick = null;\n        this.onMove = null;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/InputManager.ts\n"));

/***/ })

});