"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/Game.tsx":
/*!*********************************!*\
  !*** ./src/components/Game.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Game)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var pixi_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! pixi.js */ \"(app-pages-browser)/./node_modules/pixi.js/lib/index.mjs\");\n/* harmony import */ var _lib_GameEngine__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/GameEngine */ \"(app-pages-browser)/./src/lib/GameEngine.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction Game() {\n    _s();\n    const gameRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const gameEngineRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Game.useEffect\": ()=>{\n            if (!gameRef.current || \"object\" === 'undefined') return;\n            // Initialize PIXI Application\n            const app = new pixi_js__WEBPACK_IMPORTED_MODULE_2__.Application();\n            const initGame = {\n                \"Game.useEffect.initGame\": async ()=>{\n                    await app.init({\n                        width: window.innerWidth,\n                        height: window.innerHeight,\n                        backgroundColor: 0x000000,\n                        resizeTo: window\n                    });\n                    // Add canvas to DOM\n                    if (gameRef.current) {\n                        gameRef.current.appendChild(app.canvas);\n                    }\n                    // Initialize game engine\n                    gameEngineRef.current = new _lib_GameEngine__WEBPACK_IMPORTED_MODULE_3__.GameEngine(app);\n                    await gameEngineRef.current.init();\n                }\n            }[\"Game.useEffect.initGame\"];\n            initGame().catch(console.error);\n            // Handle window resize\n            const handleResize = {\n                \"Game.useEffect.handleResize\": ()=>{\n                    if (gameEngineRef.current) {\n                        gameEngineRef.current.resize(window.innerWidth, window.innerHeight);\n                    }\n                }\n            }[\"Game.useEffect.handleResize\"];\n            window.addEventListener('resize', handleResize);\n            // Cleanup\n            return ({\n                \"Game.useEffect\": ()=>{\n                    window.removeEventListener('resize', handleResize);\n                    if (gameEngineRef.current) {\n                        gameEngineRef.current.destroy();\n                    }\n                    app.destroy(true);\n                }\n            })[\"Game.useEffect\"];\n        }\n    }[\"Game.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: gameRef,\n                id: \"game-canvas\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/portfolio/src/components/Game.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mobile-controls\",\n                id: \"mobile-joystick\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/portfolio/src/components/Game.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/portfolio/src/components/Game.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n}\n_s(Game, \"iqKllH5O6ReAfZFEowEz8V+8KUE=\");\n_c = Game;\nvar _c;\n$RefreshReg$(_c, \"Game\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Game.tsx\n"));

/***/ })

});