"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/Background.ts":
/*!*******************************!*\
  !*** ./src/lib/Background.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Background: () => (/* binding */ Background)\n/* harmony export */ });\n/* harmony import */ var pixi_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pixi.js */ \"(app-pages-browser)/./node_modules/pixi.js/lib/index.mjs\");\n\nclass Background {\n    async init() {\n        // Load all background textures\n        const textures = {};\n        for (const layer of this.backgroundLayers){\n            try {\n                textures[layer.name] = await pixi_js__WEBPACK_IMPORTED_MODULE_0__.Assets.load(layer.path);\n            } catch (error) {\n                console.warn(\"Failed to load background layer: \".concat(layer.path), error);\n                // Create a fallback colored rectangle\n                const graphics = new pixi_js__WEBPACK_IMPORTED_MODULE_0__.Graphics();\n                graphics.rect(0, 0, 800, 600);\n                graphics.fill(0x333333);\n                textures[layer.name] = pixi_js__WEBPACK_IMPORTED_MODULE_0__.Texture.from(graphics);\n            }\n        }\n        // Create sprites for each layer\n        for(let i = 0; i < this.backgroundLayers.length; i++){\n            const layerConfig = this.backgroundLayers[i];\n            const texture = textures[layerConfig.name];\n            if (texture) {\n                const sprite = new pixi_js__WEBPACK_IMPORTED_MODULE_0__.Sprite(texture);\n                sprite.name = layerConfig.name;\n                // Scale to fit screen while maintaining aspect ratio\n                this.scaleToFit(sprite);\n                // Position at bottom for ground layers\n                if (layerConfig.name === 'grass_road') {\n                    sprite.y = window.innerHeight - sprite.height;\n                    this.groundLevel = sprite.y;\n                }\n                this.layers.push(sprite);\n                this.container.addChild(sprite);\n            }\n        }\n    }\n    scaleToFit(sprite) {\n        const screenWidth =  true ? window.innerWidth : 0;\n        const screenHeight =  true ? window.innerHeight : 0;\n        // Scale to cover the screen width\n        const scaleX = screenWidth / sprite.texture.width;\n        const scaleY = screenHeight / sprite.texture.height;\n        // Use the larger scale to ensure full coverage\n        const scale = Math.max(scaleX, scaleY);\n        sprite.scale.set(scale);\n        // Center horizontally\n        sprite.x = (screenWidth - sprite.width) / 2;\n    }\n    resize(width, height) {\n        // Rescale all layers to new screen size\n        this.layers.forEach((sprite, index)=>{\n            this.scaleToFit(sprite);\n            // Reposition ground layer\n            if (this.backgroundLayers[index].name === 'grass_road') {\n                sprite.y = height - sprite.height;\n                this.groundLevel = sprite.y;\n            }\n        });\n    }\n    getGroundLevel() {\n        return this.groundLevel;\n    }\n    // Method to implement parallax scrolling if needed later\n    updateParallax(cameraX) {\n        this.layers.forEach((sprite, index)=>{\n            const parallaxFactor = this.backgroundLayers[index].parallax;\n            sprite.x = -cameraX * parallaxFactor;\n        });\n    }\n    constructor(){\n        this.layers = [];\n        this.groundLevel = 0;\n        // Background layer order (back to front)\n        this.backgroundLayers = [\n            {\n                name: 'sky',\n                path: '/background/sky.png',\n                parallax: 0.1\n            },\n            {\n                name: 'jungle_bg',\n                path: '/background/jungle_bg.png',\n                parallax: 0.3\n            },\n            {\n                name: 'trees_bushes',\n                path: '/background/trees&bushes.png',\n                parallax: 0.5\n            },\n            {\n                name: 'lianas',\n                path: '/background/lianas.png',\n                parallax: 0.7\n            },\n            {\n                name: 'grasses',\n                path: '/background/grasses.png',\n                parallax: 0.8\n            },\n            {\n                name: 'grass_road',\n                path: '/background/grass&road.png',\n                parallax: 1.0\n            }\n        ];\n        this.container = new pixi_js__WEBPACK_IMPORTED_MODULE_0__.Container();\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/Background.ts\n"));

/***/ })

});