{"version": "0.10.2", "name": "<PERSON>js", "description": "A virtual joystick for touch capable interfaces", "author": "<PERSON><PERSON> <<EMAIL>> (https://yoannmoi.net)", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/yoannmoinet/nipplejs.git"}, "bugs": {"url": "https://github.com/yoannmoinet/nipplejs/issues"}, "files": ["dist/*", "src/*", "types/*"], "homepage": "https://github.com/yoannmoinet/nipplejs", "main": "./dist/nipplejs.js", "browser": "./dist/nipplejs.js", "module": "./src/index.js", "types": "./types/index.d.ts", "directories": {"lib": "src", "test": "test", "example": "example"}, "scripts": {"postversion": "npm run changelog", "prepack": "NODE_ENV=production npm run build", "build": "webpack --config webpack.config.js", "build:dev": "webpack-dev-server --config webpack.config.js", "test": "casperjs test ./test/nipplejs.casper.js", "changelog": "node ./bin/changelog.js", "start": "npm run build:dev -- --progress --watch", "toc": "markdown-toc -i README.md", "copyGh": "node ./bin/copyToGhPages.js"}, "devDependencies": {"@babel/core": "7.5.5", "@babel/preset-env": "7.5.5", "babel-loader": "8.0.6", "babel-plugin-add-module-exports": "1.0.2", "conventional-changelog": "3.1.24", "eslint": "5.10.0", "eslint-loader": "2.2.1", "markdown-toc": "1.2.0", "webpack": "4.39.2", "webpack-cli": "3.3.7", "webpack-dev-server": "4.11.1"}}