import { TextureStyle, type TextureStyleOptions } from '../../rendering/renderers/shared/texture/TextureStyle';
import { AbstractText } from './AbstractText';
import { type BatchableText } from './canvas/BatchableText';
import { TextStyle } from './TextStyle';
import type { View } from '../../rendering/renderers/shared/view/View';
import type { TextOptions, TextString } from './AbstractText';
import type { TextStyleOptions } from './TextStyle';
export interface Text extends PixiMixins.Text, AbstractText<TextStyle, TextStyleOptions, CanvasTextOptions, BatchableText> {
}
/**
 * Constructor options used for `Text` instances. These options extend TextOptions with
 * canvas-specific features like texture styling.
 * @example
 * ```ts
 * // Create basic canvas text
 * const text = new Text({
 *     text: 'Hello Pixi!',
 *     style: {
 *         fontSize: 24,
 *         fill: 0xff1010,
 *     }
 * });
 *
 * // Create text with custom texture style
 * const customText = new Text({
 *     text: 'Custom Text',
 *     style: {
 *         fontSize: 32,
 *         fill: 0x4a4a4a
 *     },
 *     textureStyle: {
 *         scaleMode: 'nearest',
 *         resolution: 2
 *     }
 * });
 * ```
 * @extends TextOptions
 * @category text
 * @standard
 */
export interface CanvasTextOptions extends TextOptions {
    /**
     * Optional texture style to use for the text texture. This allows fine control over
     * how the text is rendered to a texture before being displayed.
     *
     * The texture style can affect:
     * - Scale mode (nearest/linear)
     * - Resolution
     * - Format (rgb/rgba)
     * - Alpha handling
     * @example
     * ```ts
     * const text = new Text({
     *     text: 'Crisp Text',
     *     textureStyle: {
     *         scaleMode: 'nearest', // Pixel-perfect scaling
     *         format: 'rgba',       // Include alpha channel
     *         resolution: 2,        // Higher resolution
     *         premultiplyAlpha: true
     *     }
     * });
     * ```
     * @advanced
     */
    textureStyle?: TextureStyle | TextureStyleOptions;
}
/**
 * A powerful text rendering class that creates one or multiple lines of text using the Canvas API.
 * Provides rich text styling capabilities with runtime modifications.
 *
 * Key features:
 * - Dynamic text content and styling
 * - Multi-line text support
 * - Word wrapping
 * - Custom texture styling
 * - High-quality text rendering
 * @example
 * ```ts
 * import { Text } from 'pixi.js';
 *
 * // Basic text creation
 * const basicText = new Text({
 *     text: 'Hello Pixi!',
 *     style: {
 *         fontFamily: 'Arial',
 *         fontSize: 24,
 *         fill: 0xff1010,
 *         align: 'center',
 *     }
 * });
 *
 * // Rich text with multiple styles
 * const richText = new Text({
 *     text: 'Styled\nMultiline\nText',
 *     style: {
 *         fontFamily: 'Arial',
 *         fontSize: 36,
 *         fill: 'red',
 *         stroke: { color: '#4a1850', width: 5 },
 *         align: 'center',
 *         lineHeight: 45,
 *         dropShadow: {
 *             color: '#000000',
 *             blur: 4,
 *             distance: 6,
 *         }
 *     },
 *     anchor: 0.5,
 * });
 *
 * // Text with custom texture settings
 * const crispText = new Text({
 *     text: 'High Quality Text',
 *     style: {
 *         fontSize: 24,
 *         fill: 0x4a4a4a,
 *     },
 *     textureStyle: {
 *         scaleMode: 'nearest',
 *         resolution: 2,
 *         format: 'rgba',
 *     }
 * });
 *
 * // Word-wrapped text
 * const wrappedText = new Text({
 *     text: 'This is a long piece of text that will automatically wrap to multiple lines',
 *     style: {
 *         fontSize: 20,
 *         wordWrap: true,
 *         wordWrapWidth: 200,
 *         lineHeight: 30,
 *     }
 * });
 * ```
 *
 * Performance Considerations:
 * - Each text instance creates its own texture
 * - Texture is regenerated when text or style changes
 * - Use BitmapText for better performance with static text
 * - Consider texture style options for quality vs performance tradeoffs
 * @category text
 * @standard
 * @see {@link TextStyle} For detailed style options
 * @see {@link BitmapText} For better performance with static text
 * @see {@link HTMLText} For HTML/CSS-based text rendering
 */
export declare class Text extends AbstractText<TextStyle, TextStyleOptions, CanvasTextOptions, BatchableText> implements View {
    /** @internal */
    readonly renderPipeId: string;
    /**
     * Optional texture style to use for the text.
     * > [!NOTE] Text is not updated when this property is updated,
     * > you must update the text manually by calling `text.onViewUpdate()`
     * @advanced
     */
    textureStyle?: TextureStyle;
    /**
     * @param {CanvasTextOptions} options - The options of the text.
     */
    constructor(options?: CanvasTextOptions);
    /** @deprecated since 8.0.0 */
    constructor(text?: TextString, options?: Partial<TextStyle>);
    /** @private */
    protected updateBounds(): void;
}
