import { BatchableSprite } from '../../sprite/BatchableSprite.mjs';

"use strict";
class BatchableText extends BatchableSprite {
  constructor(renderer) {
    super();
    this._renderer = renderer;
    renderer.runners.resolutionChange.add(this);
  }
  resolutionChange() {
    const text = this.renderable;
    if (text._autoResolution) {
      text.onViewUpdate();
    }
  }
  destroy() {
    this._renderer.canvasText.returnTexture(this.texture);
    this._renderer = null;
  }
}

export { BatchableText };
//# sourceMappingURL=BatchableText.mjs.map
