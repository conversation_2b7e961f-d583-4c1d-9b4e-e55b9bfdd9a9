import { ExtensionType } from '../../../extensions/Extensions.mjs';
import { updateTextBounds } from '../utils/updateTextBounds.mjs';
import { BatchableText } from './BatchableText.mjs';

"use strict";
class CanvasTextPipe {
  constructor(renderer) {
    this._renderer = renderer;
  }
  validateRenderable(text) {
    return text._didTextUpdate;
  }
  addRenderable(text, instructionSet) {
    const batchableText = this._getGpuText(text);
    if (text._didTextUpdate) {
      this._updateGpuText(text);
      text._didTextUpdate = false;
    }
    this._renderer.renderPipes.batch.addToBatch(batchableText, instructionSet);
  }
  updateRenderable(text) {
    const batchableText = this._getGpuText(text);
    batchableText._batcher.updateElement(batchableText);
  }
  _updateGpuText(text) {
    const batchableText = this._getGpuText(text);
    if (batchableText.texture) {
      this._renderer.canvasText.returnTexture(batchableText.texture);
    }
    text._resolution = text._autoResolution ? this._renderer.resolution : text.resolution;
    batchableText.texture = batchableText.texture = this._renderer.canvasText.getTexture(text);
    updateTextBounds(batchableText, text);
  }
  _getGpuText(text) {
    return text._gpuData[this._renderer.uid] || this.initGpuText(text);
  }
  initGpuText(text) {
    const batchableText = new BatchableText(this._renderer);
    batchableText.renderable = text;
    batchableText.transform = text.groupTransform;
    batchableText.bounds = { minX: 0, maxX: 1, minY: 0, maxY: 0 };
    batchableText.roundPixels = this._renderer._roundPixels | text._roundPixels;
    text._gpuData[this._renderer.uid] = batchableText;
    return batchableText;
  }
  destroy() {
    this._renderer = null;
  }
}
/** @ignore */
CanvasTextPipe.extension = {
  type: [
    ExtensionType.WebGLPipes,
    ExtensionType.WebGPUPipes,
    ExtensionType.CanvasPipes
  ],
  name: "text"
};

export { CanvasTextPipe };
//# sourceMappingURL=CanvasTextPipe.mjs.map
