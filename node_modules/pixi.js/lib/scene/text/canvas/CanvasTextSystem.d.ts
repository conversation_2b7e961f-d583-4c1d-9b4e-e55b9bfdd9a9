import { ExtensionType } from '../../../extensions/Extensions';
import { type CanvasTextOptions } from '../Text';
import { TextStyle } from '../TextStyle';
import type { System } from '../../../rendering/renderers/shared/system/System';
import type { Texture } from '../../../rendering/renderers/shared/texture/Texture';
import type { Renderer } from '../../../rendering/renderers/types';
/**
 * System plugin to the renderer to manage canvas text.
 * @category rendering
 * @advanced
 */
export declare class CanvasTextSystem implements System {
    /** @ignore */
    static extension: {
        readonly type: readonly [ExtensionType.WebGLSystem, ExtensionType.WebGPUSystem, ExtensionType.CanvasSystem];
        readonly name: "canvasText";
    };
    private readonly _renderer;
    constructor(_renderer: Renderer);
    /** @deprecated since 8.0.0 */
    getTexture(text: string, resolution: number, style: TextStyle, textKey: string): Texture;
    /**
     * This is a function that will create a texture from a text string, style and resolution.
     * Useful if you want to make a texture of your text and use if for various other pixi things!
     * @param options - The options of the text that will be used to generate the texture.
     * @param options.text - the text to render
     * @param options.style - the style of the text
     * @param options.resolution - the resolution of the texture
     * @returns the newly created texture
     */
    getTexture(options: CanvasTextOptions): Texture;
    /**
     * Returns a texture that was created wit the above `getTexture` function.
     * Handy if you are done with a texture and want to return it to the pool.
     * @param texture - The texture to be returned.
     */
    returnTexture(texture: Texture): void;
    /**
     * Renders text to its canvas, and updates its texture.
     * @deprecated since 8.10.0
     */
    renderTextToCanvas(): void;
    /**
     * Applies the specified filters to the given texture.
     *
     * This method takes a texture and a list of filters, applies the filters to the texture,
     * and returns the resulting texture. It also ensures that the alpha mode of the resulting
     * texture is set to 'premultiplied-alpha'.
     * @param {Texture} texture - The texture to which the filters will be applied.
     * @param {Filter[]} filters - The filters to apply to the texture.
     * @returns {Texture} The resulting texture after all filters have been applied.
     */
    private _applyFilters;
    destroy(): void;
}
