import { Rectangle } from '../../../maths/shapes/Rectangle';
import { type CanvasAndContext } from '../../../rendering/renderers/shared/texture/CanvasPool';
import { type TextStyle } from '../TextStyle';
/**
 * Utility for generating and managing canvas-based text rendering.
 *
 * This class is responsible for rendering text to canvas elements based on provided styles,
 * measuring the resulting text dimensions, and managing the lifecycle of canvas resources.
 *
 * CanvasTextGenerator supports:
 * - Text rendering with various styles (fill, stroke, gradient, etc.)
 * - Drop shadows and letter spacing
 * - Automatic trimming of transparent pixels
 * - Canvas resource pooling
 *
 * As a singleton, it's accessed via the exported `CanvasTextGenerator` constant.
 * @example
 * ```typescript
 * // Basic usage - render text to a canvas
 * import { CanvasTextGenerator } from 'pixi.js';
 * import { TextStyle } from 'pixi.js';
 *
 * // Create a text style
 * const style = new TextStyle({
 *   fontFamily: 'Arial',
 *   fontSize: 24,
 *   fill: 0xff1010,
 *   align: 'center',
 * });
 *
 * // Get a canvas with the text rendered to it
 * const { canvasAndContext, frame } = CanvasTextGenerator.getCanvasAndContext({
 *   text: 'Hello Pixi!',
 *   style,
 *   resolution: 1
 * });
 *
 * @internal
 */
declare class CanvasTextGeneratorClass {
    /**
     * Creates a canvas with the specified text rendered to it.
     *
     * Generates a canvas of appropriate size, renders the text with the provided style,
     * and returns both the canvas/context and a Rectangle representing the text bounds.
     *
     * When trim is enabled in the style, the frame will represent the bounds of the
     * non-transparent pixels, which can be smaller than the full canvas.
     * @param options - The options for generating the text canvas
     * @param options.text - The text to render
     * @param options.style - The style to apply to the text
     * @param options.resolution - The resolution of the canvas (defaults to 1)
     * @param options.padding
     * @returns An object containing the canvas/context and the frame (bounds) of the text
     */
    getCanvasAndContext(options: {
        text: string;
        style: TextStyle;
        resolution?: number;
        padding?: number;
    }): {
        canvasAndContext: CanvasAndContext;
        frame: Rectangle;
    };
    /**
     * Returns a canvas and context to the pool.
     *
     * This should be called when you're done with the canvas to allow reuse
     * and prevent memory leaks.
     * @param canvasAndContext - The canvas and context to return to the pool
     */
    returnCanvasAndContext(canvasAndContext: CanvasAndContext): void;
    /**
     * Renders text to its canvas, and updates its texture.
     * @param text - The text to render
     * @param style - The style of the text
     * @param padding - The padding of the text
     * @param resolution - The resolution of the text
     * @param canvasAndContext - The canvas and context to render the text to
     */
    private _renderTextToCanvas;
    /**
     * Render the text with letter-spacing.
     *
     * This method handles rendering text with the correct letter spacing, using either:
     * 1. Native letter spacing if supported by the browser
     * 2. Manual letter spacing calculation if not natively supported
     *
     * For manual letter spacing, it calculates the position of each character
     * based on its width and the desired spacing.
     * @param text - The text to draw
     * @param style - The text style to apply
     * @param canvasAndContext - The canvas and context to draw to
     * @param x - Horizontal position to draw the text
     * @param y - Vertical position to draw the text
     * @param isStroke - Whether to render the stroke (true) or fill (false)
     * @private
     */
    private _drawLetterSpacing;
}
/** @internal */
export declare const CanvasTextGenerator: CanvasTextGeneratorClass;
export {};
