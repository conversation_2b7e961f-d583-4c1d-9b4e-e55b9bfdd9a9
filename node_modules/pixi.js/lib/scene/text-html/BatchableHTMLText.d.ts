import { type Texture } from '../../rendering/renderers/shared/texture/Texture';
import { BatchableSprite } from '../sprite/BatchableSprite';
import type { Renderer } from '../../rendering/renderers/types';
/**
 * The BatchableHTMLText class extends the BatchableSprite class and is used to handle HTML text rendering.
 * It includes a promise for the texture as generating the HTML texture takes some time.
 * @internal
 */
export declare class BatchableHTMLText extends BatchableSprite {
    private readonly _renderer;
    texturePromise: Promise<Texture>;
    generatingTexture: boolean;
    /**
     * Creates an instance of BatchableHTMLText.
     * @param renderer - The renderer instance to be used.
     */
    constructor(renderer: Renderer);
    /** <PERSON>les resolution changes for the HTML text. If the text has auto resolution enabled, it triggers a view update. */
    resolutionChange(): void;
    /** Destroys the BatchableHTMLText instance. Returns the texture promise to the renderer and cleans up references. */
    destroy(): void;
}
