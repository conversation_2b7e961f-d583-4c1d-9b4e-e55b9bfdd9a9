/**
 * Two Pi.
 * @type {number}
 * @category maths
 * @standard
 */
export declare const PI_2: number;
/**
 * Conversion factor for converting radians to degrees.
 * @type {number} RAD_TO_DEG
 * @category maths
 * @standard
 */
export declare const RAD_TO_DEG: number;
/**
 * Conversion factor for converting degrees to radians.
 * @type {number}
 * @category maths
 * @standard
 */
export declare const DEG_TO_RAD: number;
/**
 * Constants that identify shapes, mainly to prevent `instanceof` calls.
 * @category maths
 * @advanced
 */
export type SHAPE_PRIMITIVE = 'polygon' | 'rectangle' | 'circle' | 'ellipse' | 'triangle' | 'roundedRectangle';
