/**
 * A helper object containing the hsl shader code for both glsl
 * @internal
 */
export declare const hslgl = "\n\tfloat getLuminosity(vec3 c) {\n\t\treturn 0.3 * c.r + 0.59 * c.g + 0.11 * c.b;\n\t}\n\n\tvec3 setLuminosity(vec3 c, float lum) {\n\t\tfloat modLum = lum - getLuminosity(c);\n\t\tvec3 color = c.rgb + vec3(modLum);\n\n\t\t// clip back into legal range\n\t\tmodLum = getLuminosity(color);\n\t\tvec3 modLumVec = vec3(modLum);\n\n\t\tfloat cMin = min(color.r, min(color.g, color.b));\n\t\tfloat cMax = max(color.r, max(color.g, color.b));\n\n\t\tif(cMin < 0.0) {\n\t\t\tcolor = mix(modLumVec, color, modLum / (modLum - cMin));\n\t\t}\n\n\t\tif(cMax > 1.0) {\n\t\t\tcolor = mix(modLumVec, color, (1.0 - modLum) / (cMax - modLum));\n\t\t}\n\n\t\treturn color;\n\t}\n\n\tfloat getSaturation(vec3 c) {\n\t\treturn max(c.r, max(c.g, c.b)) - min(c.r, min(c.g, c.b));\n\t}\n\n\tvec3 setSaturationMinMidMax(vec3 cSorted, float s) {\n\t\tvec3 colorSorted = cSorted;\n\n\t\tif(colorSorted.z > colorSorted.x) {\n\t\t\tcolorSorted.y = (((colorSorted.y - colorSorted.x) * s) / (colorSorted.z - colorSorted.x));\n\t\t\tcolorSorted.z = s;\n\t\t}\n\t\telse {\n\t\t\tcolorSorted.y = 0.0;\n\t\t\tcolorSorted.z = 0.0;\n\t\t}\n\n\t\tcolorSorted.x = 0.0;\n\n\t\treturn colorSorted;\n\t}\n\n\tvec3 setSaturation(vec3 c, float s) {\n\t\tvec3 color = c;\n\n\t\tif(color.r <= color.g && color.r <= color.b) {\n\t\t\tif(color.g <= color.b) {\n\t\t\t\tcolor = setSaturationMinMidMax(color.rgb, s).rgb;\n\t\t\t}\n\t\t\telse {\n\t\t\t\tcolor = setSaturationMinMidMax(color.rbg, s).rbg;\n\t\t\t}\n\t\t}\n\t\telse if(color.g <= color.r && color.g <= color.b) {\n\t\t\tif(color.r <= color.b) {\n\t\t\t\tcolor = setSaturationMinMidMax(color.grb, s).grb;\n\t\t\t}\n\t\t\telse {\n\t\t\t\tcolor = setSaturationMinMidMax(color.gbr, s).gbr;\n\t\t\t}\n\t\t}\n\t\telse {\n\t\t\t// Using bgr for both fixes part of hue\n\t\t\tif(color.r <= color.g) {\n\t\t\t\tcolor = setSaturationMinMidMax(color.brg, s).brg;\n\t\t\t}\n\t\t\telse {\n\t\t\t\tcolor = setSaturationMinMidMax(color.bgr, s).bgr;\n\t\t\t}\n\t\t}\n\n\t\treturn color;\n\t}\n    ";
