/**
 * A helper object containing the hsl shader code for wgsl
 * @internal
 */
export declare const hslgpu = "\n\tfn getLuminosity(c: vec3<f32>) -> f32\n\t{\n\t\treturn 0.3*c.r + 0.59*c.g + 0.11*c.b;\n\t}\n\n\tfn setLuminosity(c: vec3<f32>, lum: f32) -> vec3<f32>\n\t{\n\t\tvar modLum: f32 = lum - getLuminosity(c);\n\t\tvar color: vec3<f32> = c.rgb + modLum;\n\n\t\t// clip back into legal range\n\t\tmodLum = getLuminosity(color);\n\t\tlet modLumVec = vec3<f32>(modLum);\n\n\t\tlet cMin: f32 = min(color.r, min(color.g, color.b));\n\t\tlet cMax: f32 = max(color.r, max(color.g, color.b));\n\n\t\tif(cMin < 0.0)\n\t\t{\n\t\t\tcolor = mix(modLumVec, color, modLum / (modLum - cMin));\n\t\t}\n\n\t\tif(cMax > 1.0)\n\t\t{\n\t\t\tcolor = mix(modLumVec, color, (1 - modLum) / (cMax - modLum));\n\t\t}\n\n\t\treturn color;\n\t}\n\n\tfn getSaturation(c: vec3<f32>) -> f32\n\t{\n\t\treturn max(c.r, max(c.g, c.b)) - min(c.r, min(c.g, c.b));\n\t}\n\n\tfn setSaturationMinMidMax(cSorted: vec3<f32>, s: f32) -> vec3<f32>\n\t{\n\t\tvar colorSorted = cSorted;\n\n\t\tif(colorSorted.z > colorSorted.x)\n\t\t{\n\t\t\tcolorSorted.y = (((colorSorted.y - colorSorted.x) * s) / (colorSorted.z - colorSorted.x));\n\t\t\tcolorSorted.z = s;\n\t\t}\n\t\telse\n\t\t{\n\t\t\tcolorSorted.y = 0;\n\t\t\tcolorSorted.z = 0;\n\t\t}\n\n\t\tcolorSorted.x = 0;\n\n\t\treturn colorSorted;\n\t}\n\n\tfn setSaturation(c: vec3<f32>, s: f32) -> vec3<f32>\n\t{\n\t\tvar color = c;\n\n\t\tif (color.r <= color.g && color.r <= color.b)\n\t\t{\n\t\t\tif (color.g <= color.b)\n\t\t\t{\n\t\t\t\tcolor = vec3<f32>(setSaturationMinMidMax(color.rgb, s)).rgb;\n\t\t\t}\n\t\t\telse\n\t\t\t{\n\t\t\t\tcolor = vec3<f32>(setSaturationMinMidMax(color.rbg, s)).rbg;\n\t\t\t}\n\t\t}\n\t\telse if (color.g <= color.r && color.g <= color.b)\n\t\t{\n\t\t\tif (color.r <= color.b)\n\t\t\t{\n\t\t\t\tcolor = vec3<f32>(setSaturationMinMidMax(color.grb, s)).grb;\n\t\t\t}\n\t\t\telse\n\t\t\t{\n\t\t\t\tcolor = vec3<f32>(setSaturationMinMidMax(color.gbr, s)).gbr;\n\t\t\t}\n\t\t}\n\t\telse\n\t\t{\n\t\t\t// Using bgr for both fixes part of hue\n\t\t\tif (color.r <= color.g)\n\t\t\t{\n\t\t\t\tcolor = vec3<f32>(setSaturationMinMidMax(color.brg, s)).brg;\n\t\t\t}\n\t\t\telse\n\t\t\t{\n\t\t\t\tcolor  = vec3<f32>(setSaturationMinMidMax(color.bgr, s)).bgr;\n\t\t\t}\n\t\t}\n\n\t\treturn color;\n\t}\n\t";
