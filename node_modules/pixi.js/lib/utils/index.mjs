export { detectVideoAlphaMode } from './browser/detectVideoAlphaMode.mjs';
export { isMobile } from './browser/isMobile.mjs';
export { isSafari } from './browser/isSafari.mjs';
export { isWebGLSupported } from './browser/isWebGLSupported.mjs';
export { isWebGPUSupported } from './browser/isWebGPUSupported.mjs';
export { unsafeEvalSupported } from './browser/unsafeEvalSupported.mjs';
export { getCanvasBoundingBox } from './canvas/getCanvasBoundingBox.mjs';
export { DATA_URI, VERSION } from './const.mjs';
export { cleanArray, cleanHash } from './data/clean.mjs';
export { removeItems } from './data/removeItems.mjs';
export { resetUids, uid } from './data/uid.mjs';
export { updateQuadBounds } from './data/updateQuadBounds.mjs';
export { ViewableBuffer } from './data/ViewableBuffer.mjs';
export { ApplicationInitHook, RendererInitHook } from './global/globalHooks.mjs';
export { deprecation, v8_0_0, v8_3_4 } from './logging/deprecation.mjs';
export { logDebugTexture } from './logging/logDebugTexture.mjs';
export { logRenderGroupScene, logScene } from './logging/logScene.mjs';
export { warn } from './logging/warn.mjs';
export { NOOP } from './misc/NOOP.mjs';
export { Transform } from './misc/Transform.mjs';
export { getResolutionOfUrl } from './network/getResolutionOfUrl.mjs';
export { path } from './path.mjs';
export { Pool } from './pool/Pool.mjs';
export { BigPool, PoolGroupClass } from './pool/PoolGroup.mjs';
export { sayHello } from './sayHello.mjs';
import './types.mjs';
export { earcut } from './utils.mjs';

"use strict";
//# sourceMappingURL=index.mjs.map
