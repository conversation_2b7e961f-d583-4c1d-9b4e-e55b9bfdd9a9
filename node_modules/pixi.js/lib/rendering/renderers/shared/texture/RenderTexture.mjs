import { TextureSource } from './sources/TextureSource.mjs';
import { Texture } from './Texture.mjs';

"use strict";
class RenderTexture extends Texture {
  static create(options) {
    return new RenderTexture({
      source: new TextureSource(options)
    });
  }
  /**
   * Resizes the render texture.
   * @param width - The new width of the render texture.
   * @param height - The new height of the render texture.
   * @param resolution - The new resolution of the render texture.
   * @returns This texture.
   */
  resize(width, height, resolution) {
    this.source.resize(width, height, resolution);
    return this;
  }
}

export { RenderTexture };
//# sourceMappingURL=RenderTexture.mjs.map
