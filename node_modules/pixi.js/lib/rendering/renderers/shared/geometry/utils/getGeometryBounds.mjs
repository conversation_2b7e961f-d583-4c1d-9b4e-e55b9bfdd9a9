"use strict";
function getGeometryBounds(geometry, attributeId, bounds) {
  const attribute = geometry.getAttribute(attributeId);
  if (!attribute) {
    bounds.minX = 0;
    bounds.minY = 0;
    bounds.maxX = 0;
    bounds.maxY = 0;
    return bounds;
  }
  const data = attribute.buffer.data;
  let minX = Infinity;
  let minY = Infinity;
  let maxX = -Infinity;
  let maxY = -Infinity;
  const byteSize = data.BYTES_PER_ELEMENT;
  const offset = (attribute.offset || 0) / byteSize;
  const stride = (attribute.stride || 2 * 4) / byteSize;
  for (let i = offset; i < data.length; i += stride) {
    const x = data[i];
    const y = data[i + 1];
    if (x > maxX)
      maxX = x;
    if (y > maxY)
      maxY = y;
    if (x < minX)
      minX = x;
    if (y < minY)
      minY = y;
  }
  bounds.minX = minX;
  bounds.minY = minY;
  bounds.maxX = maxX;
  bounds.maxY = maxY;
  return bounds;
}

export { getGeometryBounds };
//# sourceMappingURL=getGeometryBounds.mjs.map
